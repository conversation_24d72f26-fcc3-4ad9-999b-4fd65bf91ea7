<template>
  <el-dialog
    :model-value="modelValue"
    title="文档预览"
    fullscreen
    append-to-body
    destroy-on-close
    class="preview-url-dialog"
    :body-class="previewUrlDialogBody"
    @close="handleClose"
  >
    
    <div class="preview-container">
      <div class="panel">
        <div class="panel-content">
          <iframe
            v-if="docUrl"
            :src="getPreviewUrl(docUrl)"
            frameborder="0"
            class="url-preview-iframe"
            allowfullscreen
          ></iframe>
          <div v-else class="empty-hint">
            <el-empty description="无内容可预览" />
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, watch, defineProps, defineEmits } from 'vue';
import { Close } from '@element-plus/icons-vue';

interface Props {
  modelValue: boolean;
  docUrl: string | null;
}

const previewUrlDialogBody = 'preview-url-dialog-body';

const props = defineProps<Props>();
const emit = defineEmits(['update:modelValue']);

const handleClose = () => {
  emit('update:modelValue', false);
};

// Helper function to construct the preview URL
// You might want to adjust this based on the types of URLs you expect
const getPreviewUrl = (url: string): string => {
  if (!url) return '';
  // Example for Microsoft Office documents, adjust as needed
  if (url.match(/\.(docx|xlsx|pptx)$/i)) {
    return `https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(url)}`;
  }
  // For other URLs, just return them directly or handle other specific viewers
  return url;
};

watch(() => props.modelValue, (newValue) => {
  // Optional: any logic to run when dialog visibility changes
});
</script>

<style lang="scss" scoped>
.preview-url-dialog-body{
  height: 95%!important;
  padding: 0!important;
}

.preview-url-dialog {
  .el-dialog__header {
    padding-bottom: 10px;
    margin-right: 0; // Reset margin if needed
    border-bottom: 1px solid #eee;
  }

  .el-dialog__body {
    padding: 0; // Remove default padding to make iframe fill space
    height: 100%; // Occupy full dialog body height in fullscreen
  }
}

.dialog-close {
  position: absolute;
  top: 15px; // Adjust based on your dialog header height
  right: 20px; // Adjust based on your dialog header padding
  cursor: pointer;
  font-size: 20px;
  color: #909399;
  z-index: 2000; // Ensure it's above other dialog content

  &:hover {
    color: #409EFF;
  }
}
.preview-container {
  display: flex;
  height: 100%;
  width: 100%;
}

.panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden; // Prevent scrollbars on the panel itself
  height: 100%;
}

.panel-content {
  flex-grow: 1;
  overflow: auto; // Allow scrolling within the content area if iframe content is large
  height: 100%;
  display: flex; // For el-empty centering
  justify-content: center; // For el-empty centering
  align-items: center; // For el-empty centering
}

.url-preview-iframe {
  width: 100%;
  height: 100%;
  border: none;
}

.empty-hint {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 100%;
}
</style>
