<template>
  <el-dialog
    v-model="dialogVisible"
    :title="title"
    fullscreen
    :close-on-click-modal="false"
    :close-on-press-escape="true"
    destroy-on-close
    class="assign-resource-dialog"
    @close="handleClose"
  >
    <!-- 顶部选择器区域 -->
    <div class="top-selectors">
      <div class="left-selector">
        <label>板块选择：</label>
        <el-select
          v-model="selectedPlateId"
          placeholder="请选择板块"
          @change="handlePlateChange"
          style="width: 200px;"
          size="small"
        >
          <el-option
            v-for="item in plateList"
            :key="item.id"
            :label="item.title"
            :value="item.id"
          />
        </el-select>
      </div>
      <div class="right-selector">
        <label>版本选择：</label>
        <el-select
          v-model="selectedVer"
          placeholder="请选择版本"
          @change="handleVerChange"
          style="width: 120px; margin-right: 10px;"
          size="small"
        >
          <el-option
            v-for="item in verList"
            :key="item.ver"
            :label="item.ver"
            :value="item.ver"
          />
        </el-select>
        <label>课程选择：</label>
        <el-select
          v-model="selectedCourseId"
          placeholder="请选择课程"
          @change="handleCourseChange"
          style="width: 300px; margin-right: 10px;"
          size="small"
          filterable
          :disabled="!selectedVer"
        >
          <el-option
            v-for="item in courseList"
            :key="item.id"
            :label="`${item.course_code} ${item.course_name}`"
            :value="item.id"
          />
        </el-select>
        <el-button
          type="primary"
          size="small"
          :disabled="!canBatchAdd"
          @click="showBatchAddDialog"
        >
          批量添加
        </el-button>
      </div>
    </div>

    <div class="dialog-content">
      <!-- 左侧：课程章节结构 -->
      <div class="left-panel">
        <div class="panel-header">
          <h3>课程章节</h3>
          <span class="subtitle">拖拽右侧资源到对应章节</span>
        </div>
        <div class="panel-content">
          <div v-if="loading" style="padding: 20px; text-align: center; color: #999;">
            <el-icon class="is-loading"><Loading /></el-icon>
            正在加载章节数据...
          </div>
          <div v-else-if="!selectedPlateId" style="padding: 20px; text-align: center; color: #999;">
            请先选择板块
          </div>
          <div v-else-if="filteredChapterTreeData.length === 0" style="padding: 20px; text-align: center; color: #999;">
            暂无章节数据
          </div>
          <div v-else style="margin-bottom: 10px; font-size: 12px; color: #666;">
            共 {{ filteredChapterTreeData.length }} 个章节
          </div>
          <div v-if="!loading && selectedPlateId" style="margin-bottom: 10px; font-size: 12px; color: #666;">
            拖拽提示：从右侧资源拖拽到左侧章节
          </div>
          <el-tree
            v-if="!loading"
            ref="chapterTreeRef"
            :data="filteredChapterTreeData"
            :props="chapterTreeProps"
            node-key="id"
            :expand-on-click-node="false"
            :default-expand-all="true"
            class="chapter-tree"
          >
            <template #default="{ node, data }">
              <div
                class="tree-node chapter-drop-zone"
                :class="{ 'drag-over': data.isDragOver }"
                @dragover="handleChapterDragOver($event, data)"
                @dragleave="handleChapterDragLeave($event, data)"
                @drop="handleChapterDrop($event, data)"
              >
                <div class="node-content">
                  <el-icon class="node-icon">
                    <Folder v-if="!data.isLeaf" />
                    <Document v-else />
                  </el-icon>
                  <span class="node-label">{{ node.label }}</span>
                </div>
                <div v-if="data.resources && data.resources.length > 0" class="resource-tags">
                  <template v-for="resource in data.resources" :key="resource.id">
                    <!-- 对于job类型，使用卡片式显示 -->
                    <div
                      v-if="resource.type === 'job'"
                      class="job-resource-card"
                    >
                      <div class="job-card-header">
                        <el-icon class="job-icon">
                          <component :is="getResourceIcon(resource.type)" />
                        </el-icon>
                        <el-tooltip
                          :content="getJobTooltipContentForTag(resource)"
                          placement="top"
                          :show-after="500"
                        >
                          <span class="job-title">{{ resource.title.length > 20 ? resource.title.slice(0, 20) + '...' : resource.title }}</span>
                        </el-tooltip>
                        <div class="job-actions">
                          <el-button
                            type="primary"
                            size="small"
                            :icon="View"
                            circle
                            class="preview-btn"
                            @click="previewResource(resource)"
                            title="预览"
                          />
                          <el-button
                            type="danger"
                            size="small"
                            :icon="Close"
                            circle
                            class="remove-btn"
                            @click="removeResource(data, resource)"
                          />
                        </div>
                      </div>
                      <div class="job-card-tags">
                        <el-tag v-if="getJobExtraInfoForTag(resource, 'competence_level')" size="mini" type="info">
                          能力: {{ getJobExtraInfoForTag(resource, 'competence_level') }}
                        </el-tag>
                        <el-tag v-if="getJobExtraInfoForTag(resource, 'difficulty_level')" size="mini" type="warning">
                          难度: {{ getJobExtraInfoForTag(resource, 'difficulty_level') }}
                        </el-tag>
                        <el-tag v-if="getJobExtraInfoForTag(resource, 'questiontype')" size="mini" type="success">
                          类型: {{ getJobExtraInfoForTag(resource, 'questiontype') }}
                        </el-tag>
                      </div>
                    </div>

                    <!-- 对于其他类型，使用自定义布局显示 -->
                    <div v-else class="other-resource-card">
                      <el-tag
                        :type="getResourceTagType(resource.type)"
                        size="small"
                        class="resource-tag"
                      >
                        <el-icon class="tag-icon">
                          <component :is="getResourceIcon(resource.type)" />
                        </el-icon>
                        <span class="tag-title">{{ resource.title.length > 10 ? resource.title.slice(0, 10) + '...' : resource.title }}</span>
                      </el-tag>
                      <div class="resource-actions">
                        <el-button
                          type="primary"
                          size="small"
                          :icon="View"
                          circle
                          class="preview-btn"
                          @click="previewResource(resource)"
                          title="预览"
                        />
                        <el-button
                          type="danger"
                          size="small"
                          :icon="Close"
                          circle
                          class="remove-btn"
                          @click="removeResource(data, resource)"
                        />
                      </div>
                    </div>
                  </template>
                </div>
              </div>
            </template>
          </el-tree>
        </div>
      </div>

      <!-- 右侧：可用资源 -->
      <div class="right-panel">
        <div class="panel-header">
          <h3>可用资源</h3>
          <el-radio-group v-model="resourceFilter" size="small" @change="handleResourceFilterChange">
            <el-radio label="">
              <el-icon><Files /></el-icon>
              全部
            </el-radio>
            <el-radio label="video" :disabled="!availableResourceTypes.includes('video')">
              <el-icon><VideoPlay /></el-icon>
              视频
            </el-radio>
            <el-radio label="dzs" :disabled="!availableResourceTypes.includes('dzs')">
              <el-icon><Reading /></el-icon>
              电子书
            </el-radio>
            <el-radio label="ksdg" :disabled="!availableResourceTypes.includes('ksdg')">
              <el-icon><Document /></el-icon>
              考试大纲
            </el-radio>
            <el-radio label="jc" :disabled="!availableResourceTypes.includes('jc')">
              <el-icon><Reading /></el-icon>
              教材
            </el-radio>
            <el-radio label="job" :disabled="!availableResourceTypes.includes('job')">
              <el-icon><EditPen /></el-icon>
              试题
            </el-radio>
            <el-radio label="job_source" :disabled="!availableResourceTypes.includes('job_source')">
              <el-icon><EditPen /></el-icon>
              历年真题
            </el-radio>
          </el-radio-group>
        </div>
        <div class="panel-content">
          <div v-if="loading" style="padding: 20px; text-align: center; color: #999;">
            <el-icon class="is-loading"><Loading /></el-icon>
            正在加载资源数据...
          </div>
          <div v-else-if="!selectedCourseId" style="padding: 20px; text-align: center; color: #999;">
            请先选择课程
          </div>
          <div v-else-if="filteredResourceTreeData.length === 0" style="padding: 20px; text-align: center; color: #999;">
            暂无资源数据
          </div>
          <div v-else class="resource-header">
            <div style="font-size: 12px; color: #666; margin-bottom: 8px;">
              共 {{ filteredResourceTreeData.length }} 个资源章节，可拖拽资源到左侧
            </div>

            <!-- 过滤器 -->
            <div class="resource-filters">
              <!-- 搜索框 -->
              <el-input
                v-model="resourceSearchText"
                placeholder="搜索资源..."
                size="small"
                clearable
                style="width: 150px; margin-right: 8px;"
                @input="applyResourceFilter"
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>

              <!-- job类型的额外过滤器 -->
              <template v-if="selectedPlateResource === 'job' || (selectedPlateResource && selectedPlateResource.includes('job'))">
                <el-select
                  v-model="selectedCompetenceLevel"
                  placeholder="能力层次"
                  size="small"
                  clearable
                  style="width: 100px; margin-right: 8px;"
                  @change="applyResourceFilter"
                >
                  <el-option
                    v-for="option in competenceLevelOptions"
                    :key="option"
                    :label="option"
                    :value="option"
                  />
                </el-select>

                <el-select
                  v-model="selectedDifficultyLevel"
                  placeholder="难度层次"
                  size="small"
                  clearable
                  style="width: 80px; margin-right: 8px;"
                  @change="applyResourceFilter"
                >
                  <el-option
                    v-for="option in difficultyLevelOptions"
                    :key="option"
                    :label="option"
                    :value="option"
                  />
                </el-select>

                <el-select
                  v-model="selectedQuestionType"
                  placeholder="题目类型"
                  size="small"
                  clearable
                  style="width: 100px; margin-right: 8px;"
                  @change="applyResourceFilter"
                >
                  <el-option
                    v-for="option in questionTypeOptions"
                    :key="option"
                    :label="option"
                    :value="option"
                  />
                </el-select>
              </template>
            </div>
          </div>
          <el-tree
            v-if="!loading"
            ref="resourceTreeRef"
            :data="filteredResourceTreeData"
            :props="resourceTreeProps"
            node-key="id"
            :expand-on-click-node="false"
            :default-expand-all="true"
            class="resource-tree"
          >
            <template #default="{ node, data }">
              <div
                class="tree-node resource-node"
                :class="{ 'is-resource': data.isResource }"
                :draggable="data.isResource"
                @dragstart="handleResourceDragStart($event, data)"
                @dragend="handleResourceDragEnd($event, data)"
              >
                <el-icon class="node-icon" :class="getResourceIconClass(data.type)">
                  <component :is="getResourceIcon(data.type)" />
                </el-icon>

                <!-- 对于job类型，显示完整信息和悬停提示 -->
                <div v-if="data.isResource && data.type === 'job'" class="resource-content" >
                  <div class="job-info-section" style="display: inline-block;">
                    <el-tooltip
                      :content="getJobTooltipContent(data)"
                      placement="top"
                      :show-after="500"
                    >
                      <span class="node-label job-title">{{ node.label }}</span>
                    </el-tooltip>
                    <div class="job-extra-info">
                      <el-tag v-if="getJobExtraInfo(data, 'competence_level')" size="mini" type="info">
                        能力: {{ getJobExtraInfo(data, 'competence_level') }}
                      </el-tag>
                      <el-tag v-if="getJobExtraInfo(data, 'difficulty_level')" size="mini" type="warning">
                        难度: {{ getJobExtraInfo(data, 'difficulty_level') }}
                      </el-tag>
                      <el-tag v-if="getJobExtraInfo(data, 'questiontype')" size="mini" type="success">
                        类型: {{ getJobExtraInfo(data, 'questiontype') }}
                      </el-tag>
                    </div>
                  </div>
                <div style="display: inline-block;float: right;">
                  <el-button
                    type="primary"
                    size="small"
                    :icon="View"
                    circle
                    class="preview-btn-right"
                    @click.stop="previewResource(data)"
                    title="预览"
                  />
                </div>
                </div>

                <!-- 对于其他类型，显示原来的逻辑 -->
                <div v-else-if="data.isResource" class="other-resource-content">
                  <span class="node-label">{{ node.label }}</span>
                  <el-button
                    type="primary"
                    size="small"
                    :icon="View"
                    circle
                    class="preview-btn-right"
                    @click.stop="previewResource(data)"
                    title="预览"
                  />
                </div>

                <!-- 对于章节节点，显示原来的逻辑 -->
                <span v-else class="node-label">{{ node.label }}</span>

                <el-tag
                  v-if="data.isResource"
                  :type="getResourceTagType(data.type)"
                  size="small"
                  class="resource-type-tag"
                >
                  {{ getResourceTypeName(data.type) }}
                </el-tag>
              </div>
            </template>
          </el-tree>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose" :disabled="loading || saving">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="saving" :disabled="loading">保存</el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 批量添加对话框 -->
  <el-dialog
    v-model="batchAddDialog.visible"
    title="批量添加资源"
    width="400px"
    :close-on-click-modal="false"
    :close-on-press-escape="true"
    append-to-body
  >
    <div class="batch-add-content">
      <p style="margin-bottom: 20px; color: #666;">请选择批量添加的方式：</p>
      <el-radio-group v-model="batchAddDialog.method" style="display: flex; flex-direction: column; gap: 12px;">
        <el-radio value="byOrder">
          <div style="margin-left: 8px;">
            <div style="font-weight: 500;">按章节数量对应</div>
            <div style="font-size: 12px; color: #999; margin-top: 4px;">按照左右两边表格中的章顺序进行对应分配</div>
          </div>
        </el-radio>
        <el-radio value="byName">
          <div style="margin-left: 8px;">
            <div style="font-weight: 500;">按章节名称对应</div>
            <div style="font-size: 12px; color: #999; margin-top: 4px;">按照左右两边表格中的章名称进行对应分配</div>
          </div>
        </el-radio>
      </el-radio-group>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="batchAddDialog.visible = false">取消</el-button>
        <el-button type="primary" @click="handleBatchAdd" :loading="batchAddDialog.loading">确定</el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 预览对话框 -->
  <el-dialog
    v-model="previewDialog.visible"
    :title="`预览 - ${previewDialog.type === 'job' ? '试题' : previewDialog.type}`"
    :fullscreen="true"
    :close-on-press-escape="true"
    :close-on-click-modal="false"
    destroy-on-close
    class="preview-dialog"
    @close="closePreviewDialog"
  >
    <!-- job类型的预览内容 - 使用原有的PreviewQuestionBank组件 -->
    <div v-if="previewDialog.type === 'job' && previewDialog.data" class="job-preview-wrapper">
      <component
        :is="PreviewQuestionBankComponent"
        :coursebaseid="previewDialog.data.course_base_id"
        :jobbanksourceid="''"
        :jobbankver="''"
        :jobbankid="previewDialog.data.id"
      />
    </div>

    <!-- job_source类型的预览内容 - 历年真题 -->
    <div v-else-if="previewDialog.type === 'job_source' && previewDialog.data" class="job-preview-wrapper">
      <component
        :is="PreviewQuestionBankComponent"
        :coursebaseid="previewDialog.data.course_base_id"
        :jobbanksourceid="previewDialog.data.jobbank_source_id"
        :jobbankver="previewDialog.data.jobbank_ver"
        :jobbankid="''"
      />
    </div>

    <!-- ksdg类型的预览内容 - 考试大纲 -->
    <div v-else-if="previewDialog.type === 'ksdg' && previewDialog.data" class="ksdg-preview-wrapper">
      <div class="ksdg-preview-content">
        <v-md-preview
          :text="previewDialog.data.content || '暂无内容'"
          style="height: calc(100vh - 150px); overflow: auto;"
        />
      </div>
    </div>

    <!-- jc类型的预览内容 - 教材 -->
    <div v-else-if="previewDialog.type === 'jc' && previewDialog.data" class="jc-preview-wrapper">
      <div class="jc-preview-content">
        <div class="textbook-display">
          <!-- 左侧：教材封面 -->
          <div class="textbook-cover">
            <img
              :src="previewDialog.data.textbook_url"
              :alt="previewDialog.data.textbook_name"
              class="cover-image"
              @error="handleImageError"
            />
          </div>

          <!-- 右侧：教材信息 -->
          <div class="textbook-info">
            <div class="info-item">
              <span class="info-label">教材名称：</span>
              <span class="info-value">{{ previewDialog.data.textbook_name || previewDialog.data.course_name }}</span>
            </div>

            <div class="info-item">
              <span class="info-label">主编：</span>
              <span class="info-value">{{ previewDialog.data.textbook_editor }}</span>
            </div>

            <div class="info-item">
              <span class="info-label">出版社：</span>
              <span class="info-value">{{ previewDialog.data.publication_info }}</span>
            </div>

            <div class="info-item" v-if="previewDialog.data.course_code">
              <span class="info-label">课程代码：</span>
              <span class="info-value">{{ previewDialog.data.course_code }}</span>
            </div>

            <div class="info-item" v-if="previewDialog.data.course_credits">
              <span class="info-label">学分：</span>
              <span class="info-value">{{ previewDialog.data.course_credits }}</span>
            </div>

            <div class="info-item" v-if="previewDialog.data.course_type">
              <span class="info-label">课程类型：</span>
              <span class="info-value">{{ previewDialog.data.course_type }}</span>
            </div>

            <div class="info-item" v-if="previewDialog.data.ver">
              <span class="info-label">版本：</span>
              <span class="info-value">{{ previewDialog.data.ver }}</span>
            </div>

            <!-- 操作按钮 -->
            <div class="action-buttons" v-if="previewDialog.data.url">
              <el-button
                type="primary"
                :icon="Document"
                @click="openTextbookPdf"
              >
                查看教材PDF
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- video类型的预览内容 - 视频播放 -->
    <div v-else-if="previewDialog.type === 'video' && previewDialog.data" class="video-preview-wrapper">
      <div class="video-preview-content">
        <div class="video-header">
          <h3 class="video-title">{{ previewDialog.data.course_video_title || previewDialog.data.node_name || '视频预览' }}</h3>
          <div class="video-info">
            <el-tag v-if="previewDialog.data.course_name" type="info" size="small">
              {{ previewDialog.data.course_name }}
            </el-tag>
            <el-tag v-if="previewDialog.data.chapter_name" type="success" size="small">
              {{ previewDialog.data.chapter_name }}
            </el-tag>
            <el-tag v-if="previewDialog.data.node_name" type="warning" size="small">
              {{ previewDialog.data.node_name }}
            </el-tag>
          </div>
        </div>

        <div class="video-player-container">
          <video
            v-if="previewDialog.data.mp4_url"
            :src="previewDialog.data.mp4_url"
            controls
            preload="metadata"
            class="video-player"
            @error="handleVideoError"
          >
            您的浏览器不支持视频播放。
          </video>

          <div v-else class="no-video">
            <el-icon size="48" color="#909399">
              <VideoPlay />
            </el-icon>
            <p>暂无视频资源</p>
          </div>
        </div>

        <!-- 视频详细信息 -->
        <div class="video-details">
          <div class="detail-item" v-if="previewDialog.data.course_code">
            <span class="detail-label">课程代码：</span>
            <span class="detail-value">{{ previewDialog.data.course_code }}</span>
          </div>

          <div class="detail-item" v-if="previewDialog.data.chapter_code">
            <span class="detail-label">章节代码：</span>
            <span class="detail-value">{{ previewDialog.data.chapter_code }}</span>
          </div>

          <div class="detail-item" v-if="previewDialog.data.node_code">
            <span class="detail-label">节点代码：</span>
            <span class="detail-value">{{ previewDialog.data.node_code }}</span>
          </div>

          <div class="detail-item" v-if="previewDialog.data.course_video_id">
            <span class="detail-label">视频ID：</span>
            <span class="detail-value">{{ previewDialog.data.course_video_id }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- dzs类型的预览内容 - 电子书章节 -->
    <div v-else-if="previewDialog.type === 'dzs' && previewDialog.data" class="dzs-preview-wrapper">
      <div class="dzs-preview-content">
        <div class="dzs-header">
          <h3 class="dzs-title">{{ previewDialog.data.chap_title || '电子书章节预览' }}</h3>
          <div class="dzs-info">
            <el-tag v-if="previewDialog.data.chap_num" type="primary" size="small">
              第{{ previewDialog.data.chap_num }}章
            </el-tag>
            <el-tag v-if="previewDialog.data.course_dzs_id" type="info" size="small">
              电子书ID: {{ previewDialog.data.course_dzs_id }}
            </el-tag>
            <el-tag v-if="previewDialog.data.id" type="success" size="small">
              章节ID: {{ previewDialog.data.id }}
            </el-tag>
          </div>
        </div>

        <div class="dzs-content">
          <v-md-preview
            :text="previewDialog.data.chap_content || '暂无内容'"
            style="height: calc(100vh - 200px); overflow: auto;"
          />
        </div>
      </div>
    </div>

    <!-- dzs_s类型的预览内容 - 电子书（单本） -->
    <div v-else-if="previewDialog.type === 'dzs_s' && previewDialog.data" class="dzs-preview-wrapper">
      <div class="dzs-preview-content">
        <div class="dzs-header">
          <h3 class="dzs-title">{{ previewDialog.data.title || '电子书预览' }}</h3>
          <div class="dzs-info">
          </div>
        </div>

        <div class="dzs-content">
          <v-md-preview
            :text="previewDialog.data.content || '暂无内容'"
            style="height: calc(100vh - 200px); overflow: auto;"
          />
        </div>
      </div>
    </div>

    <!-- 其他类型的预览内容（暂时显示基本信息） -->
    <div v-else-if="previewDialog.data" class="other-preview">
      <h3>{{ previewDialog.data.title }}</h3>
      <p>类型: {{ previewDialog.type }}</p>
      <pre>{{ JSON.stringify(previewDialog.data, null, 2) }}</pre>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="closePreviewDialog">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, watch, computed, defineAsyncComponent } from 'vue';
import { ElMessage } from 'element-plus';
import {
  Folder,
  Document,
  VideoPlay,
  Reading,
  EditPen,
  Files,
  Loading,
  Close,
  Search,
  View
} from '@element-plus/icons-vue';
import {
  getCourseChapterResource,
  getCourseChapterVideos,
  saveCourseResource,
  getCourseChapterDzs,
  getCourseChapterDzs_s,
  getCourseChapterJobs,
  getCourseKsdg,
  getCourseJc,
  getCourseJobSource
} from '@/api/kcmgr';
import { getPlateDataForOlsCourseById_op } from '@/api/course';
import { GetJcVer, GetJcList } from '@/api/dzs';

// 定义组件名
defineOptions({
  name: 'AssignResourceDialog'
});

// Props
interface Props {
  modelValue: boolean;
  courseBaseId: number | null;
  courseInfoId: number | null;
  kcBm?: string; // 课程编码，用于默认选中课程
  plateId?: number; // 板块ID，用于默认选中板块
  title?: string;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  title: '指定资料'
});

// Emits
const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void;
  (e: 'close'): void;
  (e: 'save'): void;
}>();

// 类型定义
interface ChapterNode {
  id: string;
  label: string;
  isLeaf: boolean;
  chapterId: number;
  courseInfoId: number;
  resources: ResourceItem[];
  children?: ChapterNode[];
}

interface ResourceNode {
  id: string;
  label: string;
  title: string;
  type: string;
  isResource: boolean;
  course_video_id?: number;
  course_dzs_id?: number;
  course_dzs_s_id?: number; // 电子书（单本）ID
  course_job_id?: number;
  course_ksdg_id?: number; // 考试大纲ID
  course_jc_id?: number; // 教材ID
  dzs_chapter_id?: number; // 电子书章节ID
  children?: ResourceNode[];
}

interface ResourceItem {
  id: number;
  title: string;
  type: string;
  course_video_id?: number;
  course_dzs_id?: number;
  course_dzs_s_id?: number;
  course_job_id?: number;
  course_ksdg_id?: number;
  course_jc_id?: number;
  sn?: number;
}

// Reactive data
const dialogVisible = ref(props.modelValue);
const saving = ref(false);
const loading = ref(false);
const resourceFilter = ref('');

// 左侧板块数据
const plateList = ref<any[]>([]);
const selectedPlateId = ref<number | null>(null);

// 右侧版本和课程数据
const verList = ref<any[]>([]);
const courseList = ref<any[]>([]);
const selectedVer = ref<string>('');
const selectedCourseId = ref<number | null>(null);

// 可用的资源类型（根据左侧选中的板块决定）
const availableResourceTypes = ref<string[]>([]);

// Tree data
const chapterTreeData = ref<ChapterNode[]>([]);
const filteredChapterTreeData = ref<ChapterNode[]>([]); // 过滤后的左侧章节数据
const resourceTreeData = ref<ResourceNode[]>([]);
const filteredResourceTreeData = ref<ResourceNode[]>([]);

// 资源信息映射 (resource_id -> resource_info)
const resourceInfoMap = ref<Map<string, any>>(new Map());

// 资源过滤相关
const resourceSearchText = ref(''); // 搜索文本
const selectedCompetenceLevel = ref(''); // 选中的能力层次
const selectedDifficultyLevel = ref(''); // 选中的难度层次
const selectedQuestionType = ref(''); // 选中的题目类型

// 过滤选项数据
const competenceLevelOptions = ref<string[]>([]);
const difficultyLevelOptions = ref<string[]>([]);
const questionTypeOptions = ref<string[]>([]);

// 当前选中板块的资源类型
const selectedPlateResource = computed(() => {
  const selectedPlate = plateList.value.find(plate => plate.id === selectedPlateId.value);
  return selectedPlate?.resource || '';
});

// 批量添加对话框
const batchAddDialog = ref({
  visible: false,
  method: 'byName', // 'byOrder' | 'byName'
  loading: false
});

// 预览对话框
const previewDialog = ref<{
  visible: boolean;
  type: string;
  data: any;
}>({
  visible: false,
  type: '', // 资源类型：job, video, dzs 等
  data: null // 预览的数据
});

// Tree props
const chapterTreeProps = {
  children: 'children',
  label: 'label'
};

const resourceTreeProps = {
  children: 'children',
  label: 'label'
};

// Tree refs
const chapterTreeRef = ref();
const resourceTreeRef = ref();

// Resource type mappings
const resourceTypeNames: Record<string, string> = {
  video: '视频',
  dzs: '电子书',
  dzs_s: '单电子书',
  ksdg: '考试大纲',
  jc: '教材',
  job: '试题',
  job_source: '历年真题'
};

const resourceIcons: Record<string, any> = {
  video: VideoPlay,
  dzs: Reading,
  ksdg: Document,
  jc: Reading,
  job: EditPen,
  job_source: EditPen,
  default: Files
};

const resourceTagTypes: Record<string, string> = {
  video: 'primary',
  dzs: 'success',
  ksdg: 'info',
  jc: 'success',
  job: 'warning',
  job_source: 'danger'
};

// Computed properties
// 判断是否可以批量添加（只有有章节的板块才能批量添加）
const canBatchAdd = computed(() => {
  const selectedPlate = plateList.value.find(plate => plate.id === selectedPlateId.value);
  return selectedPlate && selectedPlate.hava_chapter === 1 && filteredResourceTreeData.value.length > 0;
});

// Methods
const getResourceTypeName = (type: string) => {
  return resourceTypeNames[type] || type;
};

const getResourceIcon = (type: string) => {
  return resourceIcons[type] || resourceIcons.default;
};

const getResourceTagType = (type: string) => {
  return resourceTagTypes[type] || '';
};

const getResourceIconClass = (type: string) => {
  const classMap: Record<string, string> = {
    dzs: 'dzs-icon',
    video: 'video-icon',
    job: 'job-icon'
  };
  return classMap[type] || '';
};

// 获取job类型资源的悬停提示内容
const getJobTooltipContent = (data: any) => {
  const resourceKey = `job_${data.course_job_id || data.id}`;
  const resourceInfo = resourceInfoMap.value.get(resourceKey);

  if (!resourceInfo) return data.title || data.label;

  let content = `标题: ${resourceInfo.fullTitle || resourceInfo.title}`;
  if (resourceInfo.competence_level) {
    content += `\n能力层次: ${resourceInfo.competence_level}`;
  }
  if (resourceInfo.difficulty_level) {
    content += `\n难度层次: ${resourceInfo.difficulty_level}`;
  }
  if (resourceInfo.questiontype) {
    content += `\n题目类型: ${resourceInfo.questiontype}`;
  }

  return content;
};

// 获取job类型资源的额外信息
const getJobExtraInfo = (data: any, field: string) => {
  const resourceKey = `job_${data.course_job_id || data.id}`;
  const resourceInfo = resourceInfoMap.value.get(resourceKey);

  return resourceInfo?.[field] || '';
};

// 获取job类型资源标签的悬停提示内容
const getJobTooltipContentForTag = (resource: any) => {
  const resourceKey = `job_${resource.id}`;
  const resourceInfo = resourceInfoMap.value.get(resourceKey);

  if (!resourceInfo) return resource.title;

  let content = `${resourceInfo.fullTitle || resource.title}`;

  return content;
};

// 获取job类型资源标签的额外信息
const getJobExtraInfoForTag = (resource: any, field: string) => {
  const resourceKey = `job_${resource.id}`;
  const resourceInfo = resourceInfoMap.value.get(resourceKey);

  return resourceInfo?.[field] || '';
};

// 当前拖拽的资源数据
let currentDragData: any = null;

// 资源拖拽开始事件
const handleResourceDragStart = (event: DragEvent, data: any) => {
  console.log('=== 资源拖拽开始 ===');
  console.log('拖拽资源:', data);

  if (!data.isResource) {
    event.preventDefault();
    return;
  }

  currentDragData = data;

  // 设置拖拽效果
  if (event.dataTransfer) {
    event.dataTransfer.effectAllowed = 'move';
    event.dataTransfer.setData('text/plain', JSON.stringify(data));
  }

  // 添加拖拽样式
  (event.target as HTMLElement)?.classList.add('dragging');
};

// 资源拖拽结束事件
const handleResourceDragEnd = (event: DragEvent, data: any) => {
  console.log('=== 资源拖拽结束 ===');
  console.log('拖拽资源结束:', data);

  currentDragData = null;

  // 移除拖拽样式
  (event.target as HTMLElement)?.classList.remove('dragging');
};

// 章节拖拽悬停事件
const handleChapterDragOver = (event: DragEvent, chapterData: any) => {
  if (!currentDragData || !currentDragData.isResource) {
    return;
  }

  event.preventDefault();
  event.dataTransfer!.dropEffect = 'move';

  // 添加悬停样式
  chapterData.isDragOver = true;
};

// 章节拖拽离开事件
const handleChapterDragLeave = (_event: DragEvent, chapterData: any) => {
  // 移除悬停样式
  chapterData.isDragOver = false;
};

// 章节拖拽放置事件
const handleChapterDrop = (event: DragEvent, chapterData: any) => {
  event.preventDefault();

  console.log('=== 章节拖拽放置 ===');
  console.log('拖拽资源:', currentDragData);
  console.log('目标章节:', chapterData);

  // 移除悬停样式
  chapterData.isDragOver = false;

  if (!currentDragData || !currentDragData.isResource) {
    console.log('拖拽条件不满足');
    return;
  }

  // 执行资源分配
  assignResourceToChapter(currentDragData, chapterData);
};

// 将资源分配到章节的核心逻辑
const assignResourceToChapter = (resource: any, targetNode: any) => {
  console.log('分配资源到节点:', { resource, targetNode });

  // 如果拖拽到章上面，允许分配到章级别
  if (!targetNode.isLeaf) {
    console.log('拖拽到章，允许分配到章级别');
  }

  // 添加资源到目标节点（章或节）
  if (!targetNode.resources) {
    targetNode.resources = [];
  }
  
  // 检查是否已存在
  const resourceId = resource.course_video_id || resource.dzs_chapter_id || resource.course_dzs_s_id || resource.course_job_id || resource.course_ksdg_id || resource.course_jc_id || resource.id;
  const exists = targetNode.resources.some((r: any) => {
    if (resource.type === 'video' && r.type === 'video') {
      return r.course_video_id === resourceId;
    } else if (resource.type === 'dzs' && r.type === 'dzs') {
      return r.course_dzs_id === resourceId || r.id === resourceId;
    } else if (resource.type === 'dzs_s' && r.type === 'dzs_s') {
      return r.course_dzs_s_id === resourceId || r.id === resourceId;
    } else if (resource.type === 'job' && r.type === 'job') {
      return r.course_job_id === resourceId || r.id === resourceId;
    } else if (resource.type === 'ksdg' && r.type === 'ksdg') {
      return r.course_ksdg_id === resourceId || r.id === resourceId;
    } else if (resource.type === 'jc' && r.type === 'jc') {
      return r.course_jc_id === resourceId || r.id === resourceId;
    }
    return false;
  });

  if (exists) {
    const nodeType = targetNode.isLeaf ? '节' : '章';
    ElMessage.warning(`该资源已存在于此${nodeType}`);
    return;
  }

  // 添加资源到目标节点（章或节）
  const newResource: ResourceItem = {
    id: resourceId, // 对于电子书，这是章节ID
    title: resource.title || resource.label,
    type: resource.type,
    course_video_id: resource.type === 'video' ? resourceId : undefined,
    course_dzs_id: resource.type === 'dzs' ? resource.course_dzs_id : undefined, // 保存电子书ID
    course_dzs_s_id: resource.type === 'dzs_s' ? resource.course_dzs_s_id || resourceId : undefined, // 保存电子书（单本）ID
    course_job_id: resource.type === 'job' ? resourceId : undefined,
    course_ksdg_id: resource.type === 'ksdg' ? resource.course_ksdg_id || resourceId : undefined,
    course_jc_id: resource.type === 'jc' ? resource.course_jc_id || resourceId : undefined,
    // 对于job_source，从ID中提取真正的jobbank_source_id（数字）
    jobbank_source_id: resource.type === 'job_source' ? (() => {
      // 从 "job_source_2025年04月真题_3" 中提取最后的数字 "3"
      const idStr = String(resource.id);
      const parts = idStr.split('_');
      return parseInt(parts[parts.length - 1]);
    })() : undefined,
    // 对于job_source，保存额外的信息
    ver: resource.type === 'job_source' ? (resource as any).ver : undefined,
    vertitle: resource.type === 'job_source' ? (resource as any).vertitle : undefined,
    sn: targetNode.resources.length + 1 // 设置序号
  } as any;

  // 检查是否已经存在相同的资源，避免重复添加
  const existingResource = targetNode.resources.find((r: any) =>
    r.type === newResource.type &&
    r.id === newResource.id
  );

  if (!existingResource) {
    targetNode.resources.push(newResource);
  } else {
    console.log('资源已存在，跳过添加:', newResource.type, newResource.id);
    ElMessage.warning(`资源"${resource.title || resource.label}"已经存在于"${targetNode.label}"中`);
    return;
  }

  // 从右侧资源树中移除该资源
  removeResourceFromTree(resource);

  const nodeType = targetNode.isLeaf ? '节' : '章';
  console.log(`资源分配完成，${nodeType}资源:`, targetNode.resources);
  ElMessage.success(`资源"${resource.title || resource.label}"已添加到"${targetNode.label}"`);
};

// 批量模式的资源分配（不立即移除右侧资源，不显示成功消息）
const assignResourceToChapterBatch = (resource: any, targetNode: any) => {
  console.log('批量分配资源到节点:', { resource: resource.title, targetNode: targetNode.label });

  // 添加资源到目标节点（章或节）
  if (!targetNode.resources) {
    targetNode.resources = [];
  }

  // 检查是否已存在
  const resourceId = resource.course_video_id || resource.dzs_chapter_id || resource.course_dzs_s_id || resource.course_job_id || resource.course_ksdg_id || resource.course_jc_id || resource.id;
  const exists = targetNode.resources.some((r: any) => {
    if (resource.type === 'video' && r.type === 'video') {
      return r.course_video_id === resourceId;
    } else if (resource.type === 'dzs' && r.type === 'dzs') {
      return r.course_dzs_id === resourceId || r.id === resourceId;
    } else if (resource.type === 'dzs_s' && r.type === 'dzs_s') {
      return r.course_dzs_s_id === resourceId || r.id === resourceId;
    } else if (resource.type === 'job' && r.type === 'job') {
      return r.course_job_id === resourceId || r.id === resourceId;
    } else if (resource.type === 'ksdg' && r.type === 'ksdg') {
      return r.course_ksdg_id === resourceId || r.id === resourceId;
    } else if (resource.type === 'jc' && r.type === 'jc') {
      return r.course_jc_id === resourceId || r.id === resourceId;
    }
    return false;
  });

  if (exists) {
    console.log('资源已存在，跳过:', resource.title || resource.label);
    return false; // 返回false表示未添加
  }

  // 添加资源到目标节点（章或节）
  // 对于video类型，优先使用node_name字段
  let resourceTitle = resource.title || resource.label;
  if (resource.type === 'video' && resource.node_name) {
    resourceTitle = `视频_${resource.node_name}`;
  }

  const newResource: ResourceItem = {
    id: resourceId,
    title: resourceTitle,
    type: resource.type,
    course_video_id: resource.type === 'video' ? resourceId : undefined,
    course_dzs_id: resource.type === 'dzs' ? resource.course_dzs_id : undefined,
    course_dzs_s_id: resource.type === 'dzs_s' ? resource.course_dzs_s_id || resourceId : undefined,
    course_job_id: resource.type === 'job' ? resourceId : undefined,
    course_ksdg_id: resource.type === 'ksdg' ? resource.course_ksdg_id || resourceId : undefined,
    course_jc_id: resource.type === 'jc' ? resource.course_jc_id || resourceId : undefined,
    jobbank_source_id: resource.type === 'job_source' ? (() => {
      const idStr = String(resource.id);
      const parts = idStr.split('_');
      return parseInt(parts[parts.length - 1]);
    })() : undefined,
    ver: resource.type === 'job_source' ? (resource as any).ver : undefined,
    vertitle: resource.type === 'job_source' ? (resource as any).vertitle : undefined,
    sn: targetNode.resources.length + 1
  } as any;

  // 检查是否已经存在相同的资源，避免重复添加
  const existingResource = targetNode.resources.find((r: any) =>
    r.type === newResource.type &&
    r.id === newResource.id
  );

  if (!existingResource) {
    targetNode.resources.push(newResource);
    console.log(`批量添加资源成功: "${newResource.title}" -> "${targetNode.label}"`);
    return true; // 返回true表示成功添加
  } else {
    console.log('资源已存在，跳过添加:', newResource.type, newResource.id);
    return false; // 返回false表示未添加
  }
};

// 批量清理已分配的资源
const batchCleanupAssignedResources = (resourceAssignments: Array<{ resource: any, targetChapter: ChapterNode }>) => {
  console.log('开始批量清理已分配的资源...');

  const resourcesToRemove = new Set<string>();

  // 收集所有需要移除的资源ID
  for (const assignment of resourceAssignments) {
    resourcesToRemove.add(assignment.resource.id);
  }

  // 从右侧资源树中移除已分配的资源
  resourceTreeData.value.forEach(chapter => {
    if (chapter.children) {
      chapter.children = chapter.children.filter(child => {
        if (child.isResource && resourcesToRemove.has(child.id)) {
          console.log(`从右侧移除已分配的资源: ${child.title || child.label}`);
          return false; // 移除
        }
        return true; // 保留
      });
    }
  });

  console.log(`批量清理完成，移除了 ${resourcesToRemove.size} 个资源`);
};

// 从右侧资源树中移除资源
const removeResourceFromTree = (resource: any) => {
  console.log('从资源树中移除资源:', resource);

  // 遍历资源树数据，找到并移除该资源
  filteredResourceTreeData.value.forEach(chapter => {
    if (chapter.children) {
      const index = chapter.children.findIndex(child => {
        // 精确匹配：优先使用唯一的 id 进行匹配
        return child.id === resource.id;
      });
      if (index > -1) {
        chapter.children.splice(index, 1);
        console.log(`从章节 ${chapter.label} 中移除了${resource.type}资源`);
      }
    }
  });

  // 同时从原始资源树数据中移除
  resourceTreeData.value.forEach(chapter => {
    if (chapter.children) {
      const index = chapter.children.findIndex(child => {
        // 精确匹配：优先使用唯一的 id 进行匹配
        return child.id === resource.id;
      });
      if (index > -1) {
        chapter.children.splice(index, 1);
      }
    }
  });
};

// Remove resource from chapter
const removeResource = (chapter: any, resource: any) => {
  console.log('删除资源:', { chapter: chapter.label, resource: resource.title, chapterId: chapter.chapterId });

  // 首先从原始数据中删除资源
  const removeFromOriginalData = (nodes: ChapterNode[]) => {
    for (const node of nodes) {
      if (node.chapterId === chapter.chapterId) {
        const index = node.resources.findIndex((r: any) => r.id === resource.id && r.type === resource.type);
        if (index > -1) {
          node.resources.splice(index, 1);
          console.log(`从原始数据中删除资源成功: ${resource.title}`);
          return true;
        }
      }
      // 递归检查子节点
      if (node.children && node.children.length > 0) {
        if (removeFromOriginalData(node.children)) {
          return true;
        }
      }
    }
    return false;
  };

  // 从原始章节数据中删除
  const removedFromOriginal = removeFromOriginalData(chapterTreeData.value);

  // 同时从当前显示的过滤数据中删除（确保界面立即更新）
  const index = chapter.resources.findIndex((r: any) => r.id === resource.id && r.type === resource.type);
  if (index > -1) {
    chapter.resources.splice(index, 1);
  }

  if (removedFromOriginal || index > -1) {
    ElMessage.success('资源移除成功');

    // 删除资源后，重新过滤资源以更新右侧显示
    filterResources();
  } else {
    console.error('删除资源失败，未找到对应资源');
    ElMessage.error('删除资源失败');
  }
};

// Filter resources
const filterResources = () => {
  console.log('=== 开始过滤资源 ===');
  console.log('resourceTreeData.value:', resourceTreeData.value);
  console.log('chapterTreeData.value:', chapterTreeData.value);
  console.log('resourceFilter.value:', resourceFilter.value);
  console.log('availableResourceTypes.value:', availableResourceTypes.value);

  // 过滤右侧资源数据
  const unassignedResourceData = getUnassignedResources();
  console.log('未分配的资源数据:', unassignedResourceData);

  if (!resourceFilter.value) {
    filteredResourceTreeData.value = unassignedResourceData;
  } else {
    filteredResourceTreeData.value = unassignedResourceData.map(chapter => ({
      ...chapter,
      children: chapter.children?.filter((resource: ResourceNode) => resource.type === resourceFilter.value) || []
    })).filter(chapter => chapter.children && chapter.children.length > 0);
  }

  // 过滤左侧章节数据 - 根据板块的resource字段和右侧资源过滤器
  filteredChapterTreeData.value = chapterTreeData.value.map(chapter => {
    const filteredChapter = { ...chapter };

    // 过滤章级别的资源
    if (filteredChapter.resources) {
      filteredChapter.resources = filteredChapter.resources.filter(resource => {
        // 首先根据板块的resource字段过滤
        const isAllowedByPlate = availableResourceTypes.value.includes(resource.type);
        // 然后根据右侧资源过滤器过滤（如果有选择的话）
        const isAllowedByFilter = !resourceFilter.value || resource.type === resourceFilter.value;
        return isAllowedByPlate && isAllowedByFilter;
      });
    }

    // 过滤子节点（节级别）的资源
    if (filteredChapter.children) {
      filteredChapter.children = filteredChapter.children.map(child => {
        const filteredChild = { ...child };
        if (filteredChild.resources) {
          filteredChild.resources = filteredChild.resources.filter(resource => {
            // 首先根据板块的resource字段过滤
            const isAllowedByPlate = availableResourceTypes.value.includes(resource.type);
            // 然后根据右侧资源过滤器过滤（如果有选择的话）
            const isAllowedByFilter = !resourceFilter.value || resource.type === resourceFilter.value;
            return isAllowedByPlate && isAllowedByFilter;
          });
        }
        return filteredChild;
      });

      // 如果右侧有资源过滤器，则只保留有对应类型资源的子节点
      if (resourceFilter.value) {
        filteredChapter.children = filteredChapter.children.filter(child =>
          child.resources && child.resources.length > 0
        );
      }
    }

    return filteredChapter;
  });

  // 获取未分配的资源并应用额外过滤
  filteredResourceTreeData.value = getUnassignedResources();

  // 应用额外的资源过滤（搜索和job类型字段过滤）
  applyResourceFilter();

  console.log('最终过滤后的右侧数据:', filteredResourceTreeData.value);
  console.log('最终过滤后的左侧数据:', filteredChapterTreeData.value);
  console.log('=== 过滤资源完成 ===');
};

// 获取未分配的资源数据
const getUnassignedResources = () => {
  console.log('=== 获取未分配资源 ===');
  console.log('chapterTreeData.value:', chapterTreeData.value);
  console.log('resourceTreeData.value:', resourceTreeData.value);

  // 收集所有已分配的资源ID
  const assignedVideoIds = new Set<number>();
  const assignedDzsIds = new Set<number>();
  const assignedDzsSIds = new Set<number>();
  const assignedJobIds = new Set<number>();
  const assignedKsdgIds = new Set<number>();
  const assignedJcIds = new Set<number>();
  const assignedJobSourceIds = new Set<string>();

  chapterTreeData.value.forEach(chapter => {
    // 处理章级别的资源（如果有）
    if (chapter.resources) {
      chapter.resources.forEach(resource => {
        if (resource.type === 'video' && resource.course_video_id) {
          assignedVideoIds.add(resource.course_video_id);
        } else if (resource.type === 'dzs' && resource.id) {
          // 电子书使用章节ID（resource.id）
          assignedDzsIds.add(resource.id);
        } else if (resource.type === 'dzs_s' && resource.course_dzs_s_id) {
          // 电子书（单本）使用course_dzs_s_id
          assignedDzsSIds.add(resource.course_dzs_s_id);
        } else if (resource.type === 'job' && resource.id) {
          // 试题使用ID
          assignedJobIds.add(resource.id);
        } else if (resource.type === 'ksdg' && resource.course_ksdg_id) {
          // 考试大纲使用ID
          console.log('收集已分配的ksdg资源ID:', resource.course_ksdg_id);
          assignedKsdgIds.add(resource.course_ksdg_id);
        } else if (resource.type === 'jc' && resource.course_jc_id) {
          // 教材使用ID
          console.log('收集已分配的jc资源ID:', resource.course_jc_id);
          assignedJcIds.add(resource.course_jc_id);
        } else if (resource.type === 'job_source' && resource.id) {
          // 历年真题使用 id 作为唯一标识
          console.log('收集已分配的job_source资源ID:', resource.id);
          assignedJobSourceIds.add(String(resource.id));
        }
      });
    }

    // 处理节级别的资源
    if (chapter.children) {
      chapter.children.forEach(section => {
        if (section.resources) {
          section.resources.forEach(resource => {
            if (resource.type === 'video' && resource.course_video_id) {
              assignedVideoIds.add(resource.course_video_id);
            } else if (resource.type === 'dzs' && resource.id) {
              // 电子书使用章节ID（resource.id）
              assignedDzsIds.add(resource.id);
            } else if (resource.type === 'dzs_s' && resource.course_dzs_s_id) {
              // 电子书（单本）使用course_dzs_s_id
              assignedDzsSIds.add(resource.course_dzs_s_id);
            } else if (resource.type === 'job' && resource.id) {
              // 试题使用ID
              assignedJobIds.add(resource.id);
            } else if (resource.type === 'ksdg' && resource.course_ksdg_id) {
              // 考试大纲使用ID
              console.log('收集已分配的ksdg资源ID(节级别):', resource.course_ksdg_id);
              assignedKsdgIds.add(resource.course_ksdg_id);
            } else if (resource.type === 'jc' && resource.course_jc_id) {
              // 教材使用ID
              console.log('收集已分配的jc资源ID(节级别):', resource.course_jc_id);
              assignedJcIds.add(resource.course_jc_id);
            } else if (resource.type === 'job_source' && resource.id) {
              // 历年真题使用 id 作为唯一标识
              console.log('收集已分配的job_source资源ID(节级别):', resource.id);
              assignedJobSourceIds.add(String(resource.id));
            }
          });
        }
      });
    }
  });

  console.log('已分配的资源ID:', { assignedVideoIds, assignedDzsIds, assignedDzsSIds, assignedJobIds, assignedKsdgIds, assignedJcIds, assignedJobSourceIds });

  // 从资源树中过滤掉已分配的资源
  const result = resourceTreeData.value.map(chapter => {
    const filteredChildren = chapter.children?.filter(resource => {
      if (resource.type === 'video' && resource.course_video_id) {
        return !assignedVideoIds.has(resource.course_video_id);
      } else if (resource.type === 'dzs' && resource.dzs_chapter_id) {
        // 电子书使用章节ID进行过滤
        return !assignedDzsIds.has(resource.dzs_chapter_id);
      } else if (resource.type === 'dzs_s' && resource.course_dzs_s_id) {
        // 电子书（单本）使用course_dzs_s_id进行过滤
        const isAssigned = assignedDzsSIds.has(resource.course_dzs_s_id);
        console.log(`过滤dzs_s资源 ${resource.course_dzs_s_id}:`, isAssigned ? '已分配，过滤掉' : '未分配，保留');
        return !isAssigned;
      } else if (resource.type === 'job' && resource.course_job_id) {
        return !assignedJobIds.has(resource.course_job_id);
      } else if (resource.type === 'ksdg' && resource.course_ksdg_id) {
        const isAssigned = assignedKsdgIds.has(resource.course_ksdg_id);
        console.log(`过滤ksdg资源 ${resource.course_ksdg_id}:`, isAssigned ? '已分配，过滤掉' : '未分配，保留');
        return !isAssigned;
      } else if (resource.type === 'jc' && resource.course_jc_id) {
        const isAssigned = assignedJcIds.has(resource.course_jc_id);
        console.log(`过滤jc资源 ${resource.course_jc_id}:`, isAssigned ? '已分配，过滤掉' : '未分配，保留');
        return !isAssigned;
      } else if (resource.type === 'job_source' && resource.id) {
        // 右侧资源ID格式：job_source_2024年04月真题_3
        // 左侧资源ID格式：2024年04月真题_3
        // 需要从右侧ID中提取出对应部分进行比较
        const rightSideId = String(resource.id);
        const extractedId = rightSideId.startsWith('job_source_') ? rightSideId.substring(11) : rightSideId;
        const isAssigned = assignedJobSourceIds.has(extractedId);
        console.log(`过滤job_source资源 ${rightSideId} -> ${extractedId}:`, isAssigned ? '已分配，过滤掉' : '未分配，保留');
        return !isAssigned;
      }
      return true;
    }) || [];

    return {
      ...chapter,
      children: filteredChildren
    };
  }).filter(chapter => {
    // 如果章节原本就没有数据，保留章节显示
    // 如果章节原本有数据但现在被过滤空了，删除章节
    const originalChildrenCount = resourceTreeData.value.find(c => c.id === chapter.id)?.children?.length || 0;
    if (originalChildrenCount === 0) {
      return true; // 保留原本就没有数据的章节
    }
    return chapter.children && chapter.children.length > 0; // 只保留还有数据的章节
  });

  console.log('getUnassignedResources 返回结果:', result);
  return result;
};

// filteredResourceTreeData is already reactive, no need for computed

// 提取job类型资源的过滤选项
const extractJobFilterOptions = () => {
  const competenceLevels = new Set<string>();
  const difficultyLevels = new Set<string>();
  const questionTypes = new Set<string>();

  console.log('=== 提取job过滤选项 ===');
  console.log('resourceInfoMap.value:', resourceInfoMap.value);

  // 遍历resourceInfoMap获取所有job类型的字段值
  resourceInfoMap.value.forEach((info, key) => {
    console.log('检查资源:', key, info);
    if (key.startsWith('job_')) {
      console.log('找到job类型资源:', key, info);
      if (info.competence_level) {
        competenceLevels.add(info.competence_level);
        console.log('添加能力层次:', info.competence_level);
      }
      if (info.difficulty_level) {
        difficultyLevels.add(info.difficulty_level);
        console.log('添加难度层次:', info.difficulty_level);
      }
      if (info.questiontype) {
        questionTypes.add(info.questiontype);
        console.log('添加题目类型:', info.questiontype);
      }
    }
  });

  competenceLevelOptions.value = Array.from(competenceLevels).sort();
  difficultyLevelOptions.value = Array.from(difficultyLevels).sort();
  questionTypeOptions.value = Array.from(questionTypes).sort();

  console.log('最终过滤选项:');
  console.log('competenceLevelOptions:', competenceLevelOptions.value);
  console.log('difficultyLevelOptions:', difficultyLevelOptions.value);
  console.log('questionTypeOptions:', questionTypeOptions.value);
  console.log('=== 提取job过滤选项完成 ===');
};

// 应用资源过滤
const applyResourceFilter = () => {
  // 首先获取未分配的资源（保留原来的过滤逻辑）
  const unassignedResources = getUnassignedResources();

  if (!unassignedResources.length) {
    filteredResourceTreeData.value = [];
    return;
  }

  const searchText = resourceSearchText.value.toLowerCase().trim();
  const competenceLevel = selectedCompetenceLevel.value;
  const difficultyLevel = selectedDifficultyLevel.value;
  const questionType = selectedQuestionType.value;

  // 如果没有任何过滤条件，直接使用未分配的资源
  if (!searchText && !competenceLevel && !difficultyLevel && !questionType) {
    filteredResourceTreeData.value = unassignedResources;
    return;
  }

  // 在未分配资源的基础上进行进一步过滤
  filteredResourceTreeData.value = unassignedResources.map(chapter => {
    const filteredChapter = { ...chapter };

    if (chapter.children) {
      filteredChapter.children = chapter.children.filter(resource => {
        if (!resource.isResource) return true;

        // 基础文本过滤
        const matchesText = !searchText ||
          resource.label.toLowerCase().includes(searchText) ||
          resource.title.toLowerCase().includes(searchText);

        // 如果不是job类型或者当前板块不包含job，只进行文本过滤
        if (resource.type !== 'job' || !selectedPlateResource.value.includes('job')) {
          return matchesText;
        }

        // job类型的额外过滤
        const resourceKey = `job_${resource.course_job_id || resource.id}`;
        const resourceInfo = resourceInfoMap.value.get(resourceKey);

        if (!resourceInfo) return matchesText;

        const matchesCompetence = !competenceLevel || resourceInfo.competence_level === competenceLevel;
        const matchesDifficulty = !difficultyLevel || resourceInfo.difficulty_level === difficultyLevel;
        const matchesQuestionType = !questionType || resourceInfo.questiontype === questionType;

        return matchesText && matchesCompetence && matchesDifficulty && matchesQuestionType;
      });
    }

    return filteredChapter;
  }).filter(chapter => !chapter.children || chapter.children.length > 0);
};

// 重置资源过滤器
const resetResourceFilter = () => {
  resourceSearchText.value = '';
  selectedCompetenceLevel.value = '';
  selectedDifficultyLevel.value = '';
  selectedQuestionType.value = '';
  applyResourceFilter();
};

// 处理资源过滤器变化
const handleResourceFilterChange = (value: string) => {
  console.log('资源过滤器变化:', value);
  console.log('resourceFilter.value:', resourceFilter.value);
  filterResources();
};

// 处理图片加载错误
const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement;
  img.src = '/src/assets/images/default-textbook.png'; // 设置默认图片
  console.log('教材封面加载失败，使用默认图片');
};

// 处理视频加载错误
const handleVideoError = (event: Event) => {
  const video = event.target as HTMLVideoElement;
  console.error('视频加载失败:', video.src);
  ElMessage.error('视频加载失败，请检查网络连接或联系管理员');
};

// 打开教材PDF
const openTextbookPdf = () => {
  if (previewDialog.value.data?.url) {
    window.open(previewDialog.value.data.url, '_blank');
  }
};

// 预览资源
const previewResource = (resource: any) => {
  console.log('预览资源:', resource);

  // 获取完整的资源信息
  let fullResourceData = null;

  if (resource.type === 'video') {
    // 对于video类型，从resourceInfoMap中获取完整信息
    const resourceKey = `video_${resource.course_video_id || resource.id}`;
    const rightSideData = resourceInfoMap.value.get(resourceKey);

    if (rightSideData) {
      // video预览主要需要mp4_url字段
      fullResourceData = {
        ...rightSideData,
        // 确保有必要的字段
        mp4_url: rightSideData.mp4_url || '',
        course_video_title: rightSideData.course_video_title || rightSideData.title,
        node_name: rightSideData.node_name || '',
        chapter_name: rightSideData.chapter_name || '',
        course_name: rightSideData.course_name || '',
        course_code: rightSideData.course_code || '',
        chapter_code: rightSideData.chapter_code || '',
        node_code: rightSideData.node_code || '',
        course_video_id: rightSideData.course_video_id || rightSideData.id
      };
    }
  } else if (resource.type === 'dzs') {
    // 对于dzs类型，从resourceInfoMap中获取完整信息
    const resourceKey = `dzs_${resource.id}`;
    const rightSideData = resourceInfoMap.value.get(resourceKey);

    if (rightSideData) {
      // dzs预览主要需要chap_content字段
      fullResourceData = {
        ...rightSideData,
        // 确保有必要的字段
        chap_content: rightSideData.chap_content || '',
        chap_title: rightSideData.chap_title || rightSideData.title,
        chap_num: rightSideData.chap_num || '',
        course_dzs_id: rightSideData.course_dzs_id || '',
        id: rightSideData.id || resource.id
      };
    }
  } else if (resource.type === 'dzs_s') {
    // 对于dzs_s类型，从resourceInfoMap中获取完整信息
    const resourceKey = `dzs_s_${resource.id}`;
    const rightSideData = resourceInfoMap.value.get(resourceKey);

    if (rightSideData) {
      // dzs_s预览主要需要content字段
      fullResourceData = {
        ...rightSideData,
        // 确保有必要的字段
        content: rightSideData.content || '',
        title: rightSideData.title || rightSideData.menu || '',
        menu: rightSideData.menu || '',
        zsd: rightSideData.zsd || '',
        url: rightSideData.url || '',
        execute_status: rightSideData.execute_status || '',
        execute_url: rightSideData.execute_url || '',
        course_jc_id: rightSideData.course_jc_id || '',
        course_base_id: rightSideData.course_base_id || '',
        id: rightSideData.id || resource.id
      };
    }
  } else if (resource.type === 'job') {
    // 从resourceInfoMap中获取完整的job信息
    const resourceKey = `job_${resource.course_job_id || resource.id}`;
    fullResourceData = resourceInfoMap.value.get(resourceKey);

    // 如果获取到了完整数据，补充course_base_id
    if (fullResourceData) {
      // 优先使用props中的courseBaseId，如果没有则从选中的课程中获取
      let courseBaseId = props.courseBaseId;
      if (!courseBaseId && selectedCourseId.value) {
        const selectedCourse = courseList.value.find(course => course.id === selectedCourseId.value);
        courseBaseId = selectedCourse ? selectedCourse.course_base_id : selectedCourseId.value;
      }

      // 补充course_base_id字段
      fullResourceData = {
        ...fullResourceData,
        course_base_id: courseBaseId
      };
    }
  } else if (resource.type === 'job_source') {
    // 对于job_source类型，需要从右侧数据中获取完整信息
    const resourceKey = `job_source_${resource.id}`;
    const rightSideData = resourceInfoMap.value.get(resourceKey);

    if (rightSideData) {
      // 获取course_base_id
      let courseBaseId = props.courseBaseId;
      if (!courseBaseId && selectedCourseId.value) {
        const selectedCourse = courseList.value.find(course => course.id === selectedCourseId.value);
        courseBaseId = selectedCourse ? selectedCourse.course_base_id : selectedCourseId.value;
      }

      // 构建job_source预览所需的数据
      fullResourceData = {
        course_base_id: courseBaseId,
        jobbank_source_id: rightSideData.id.split("_")[1],
        jobbank_ver: rightSideData.ver, // ver对应jobbank_ver
        jobbank_id: '', // jobbank_id为空
        // 保留原始数据
        ...rightSideData
      };
    }
  } else if (resource.type === 'ksdg') {
    // 对于ksdg类型，从resourceInfoMap中获取完整信息
    const resourceKey = `ksdg_${resource.id}`;
    const rightSideData = resourceInfoMap.value.get(resourceKey);

    if (rightSideData) {
      // ksdg预览主要需要content字段
      fullResourceData = {
        ...rightSideData,
        // 确保有content字段用于预览
        content: rightSideData.content || rightSideData.title || '暂无内容'
      };
    }
  } else if (resource.type === 'jc') {
    // 对于jc类型，从resourceInfoMap中获取完整信息
    const resourceKey = `jc_${resource.id}`;
    const rightSideData = resourceInfoMap.value.get(resourceKey);

    if (rightSideData) {
      // jc预览需要所有教材相关字段
      fullResourceData = {
        ...rightSideData,
        // 确保有必要的字段
        textbook_name: rightSideData.textbook_name || rightSideData.course_name || rightSideData.title,
        textbook_editor: rightSideData.textbook_editor || '',
        publication_info: rightSideData.publication_info || '',
        textbook_url: rightSideData.textbook_url || '',
        url: rightSideData.url || '',
        course_code: rightSideData.course_code || '',
        course_credits: rightSideData.course_credits || '',
        course_type: rightSideData.course_type || '',
        ver: rightSideData.ver || ''
      };
    }
  }

  // 如果没有获取到完整数据，使用原始resource并补充必要字段
  let finalData = fullResourceData || resource;

  // 对于job类型补充course_base_id
  if (resource.type === 'job' && !finalData.course_base_id) {
    let courseBaseId = props.courseBaseId;
    if (!courseBaseId && selectedCourseId.value) {
      const selectedCourse = courseList.value.find(course => course.id === selectedCourseId.value);
      courseBaseId = selectedCourse ? selectedCourse.course_base_id : selectedCourseId.value;
    }

    finalData = {
      ...finalData,
      course_base_id: courseBaseId
    };
  }

  // 对于video类型补充必要字段
  if (resource.type === 'video' && !fullResourceData) {
    // 如果没有获取到完整数据，使用resource中的信息
    finalData.mp4_url = resource.mp4_url || '';
    finalData.course_video_title = resource.course_video_title || resource.title || '';
    finalData.node_name = resource.node_name || '';
    finalData.chapter_name = resource.chapter_name || '';
    finalData.course_name = resource.course_name || '';
    finalData.course_code = resource.course_code || '';
    finalData.chapter_code = resource.chapter_code || '';
    finalData.node_code = resource.node_code || '';
    finalData.course_video_id = resource.course_video_id || resource.id;
  }

  // 对于dzs类型补充必要字段
  if (resource.type === 'dzs' && !fullResourceData) {
    // 如果没有获取到完整数据，使用resource中的信息
    finalData.chap_content = resource.chap_content || resource.content || '暂无内容';
    finalData.chap_title = resource.chap_title || resource.title || '';
    finalData.chap_num = resource.chap_num || '';
    finalData.course_dzs_id = resource.course_dzs_id || '';
    finalData.id = resource.id;
  }

  // 对于dzs_s类型补充必要字段
  if (resource.type === 'dzs_s' && !fullResourceData) {
    // 如果没有获取到完整数据，使用resource中的信息
    finalData.content = resource.content || '暂无内容';
    finalData.title = resource.title || resource.menu || '';
    finalData.menu = resource.menu || '';
    finalData.zsd = resource.zsd || '';
    finalData.url = resource.url || '';
    finalData.execute_status = resource.execute_status || '';
    finalData.execute_url = resource.execute_url || '';
    finalData.course_jc_id = resource.course_jc_id || '';
    finalData.course_base_id = resource.course_base_id || '';
    finalData.id = resource.id;
  }

  // 对于job_source类型补充必要字段
  if (resource.type === 'job_source' && !fullResourceData) {
    let courseBaseId = props.courseBaseId;
    if (!courseBaseId && selectedCourseId.value) {
      const selectedCourse = courseList.value.find(course => course.id === selectedCourseId.value);
      courseBaseId = selectedCourse ? selectedCourse.course_base_id : selectedCourseId.value;
    }

    // 尝试从resource中获取必要信息，或者设置默认值
    finalData = {
      ...finalData,
      course_base_id: courseBaseId,
      jobbank_source_id: resource.jobbank_source_id || '',
      jobbank_ver: resource.ver || resource.jobbank_ver || '',
      jobbank_id: ''
    };
  }

  // 对于ksdg类型补充必要字段

  if (resource.type === 'ksdg' && !fullResourceData) {
    // 如果没有获取到完整数据，使用resource中的信息
    finalData = {
      ...finalData,
      content: resource.content || resource.title || '暂无内容'
    };
  }

  // 对于jc类型补充必要字段
  if (resource.type === 'jc' && !fullResourceData) {
    // 如果没有获取到完整数据，使用resource中的信息
    finalData = {
      ...finalData,
      textbook_name: resource.textbook_name || resource.title || '未知教材',
      textbook_editor: resource.textbook_editor || '',
      publication_info: resource.publication_info || '',
      textbook_url: resource.textbook_url || '',
      url: resource.url || '',
      course_code: resource.course_code || '',
      course_credits: resource.course_credits || '',
      course_type: resource.course_type || '',
      ver: resource.ver || ''
    };
  }

  console.log('预览数据:', finalData);

  previewDialog.value = {
    visible: true,
    type: resource.type,
    data: finalData
  };
};

// 关闭预览对话框
const closePreviewDialog = () => {
  previewDialog.value.visible = false;
  previewDialog.value.type = '';
  previewDialog.value.data = null;
};

// 动态导入PreviewQuestionBank组件
const PreviewQuestionBankComponent = defineAsyncComponent(() =>
  import('@/components/question/preview_question_bank.vue')
);

// Watch props
watch(() => props.modelValue, (newVal) => {
  dialogVisible.value = newVal;
  if (newVal) {
    loadData();
  }
});

watch(dialogVisible, (newVal) => {
  emit('update:modelValue', newVal);
});

// 监听资源类型变化，重置过滤器
watch(resourceFilter, () => {
  resetResourceFilter();
});

// 获取板块数据
const getPlateData = async () => {
  if (!props.courseInfoId) return;

  try {
    const res = await getPlateDataForOlsCourseById_op({ id: props.courseInfoId });
    if (res.code === 200) {
      plateList.value = res.data || [];
      // 根据传入的plateId参数选中对应的板块
      if (props.plateId && plateList.value.length > 0) {
        const targetPlate = plateList.value.find(plate => plate.plate_id === props.plateId);
        if (targetPlate) {
          selectedPlateId.value = targetPlate.id;
          console.log('根据plateId参数选中板块:', targetPlate);
        } else {
          // 如果没有找到对应的板块，默认选中第一个
          selectedPlateId.value = plateList.value[0].id;
          console.log('未找到指定plateId的板块，默认选中第一个:', plateList.value[0]);
        }
      } else if (plateList.value.length > 0) {
        // 如果没有传入plateId，默认选中第一个
        selectedPlateId.value = plateList.value[0].id;
        console.log('没有传入plateId，默认选中第一个板块:', plateList.value[0]);
      }
    }
  } catch (error) {
    console.error('获取板块数据失败:', error);
  }
};

// 获取版本数据
const getVerData = async () => {
  try {
    const res = await GetJcVer({});
    if (res.code === 200) {
      verList.value = res.data || [];
      // 默认选中第一个版本
      if (verList.value.length > 0) {
        selectedVer.value = verList.value[0].ver;
        await getCourseData();
      }
    }
  } catch (error) {
    console.error('获取版本数据失败:', error);
  }
};

// 获取课程数据
const getCourseData = async () => {
  if (!selectedVer.value) return;

  try {
    const res = await GetJcList({ ver: selectedVer.value });
    if (res.code === 200) {
      courseList.value = res.data?.list || [];

      // 如果传入了 kcBm，默认选中对应的课程
      if (props.courseBaseId && courseList.value.length > 0) {
        const defaultCourse = courseList.value.find(course => course.course_base_id === props.courseBaseId);
        if (defaultCourse) {
          selectedCourseId.value = defaultCourse.id;
          console.log('默认选中课程:', defaultCourse);
          // 选中课程后自动加载资源数据
          loadResourceData();
        }
      }
    }
  } catch (error) {
    console.error('获取课程数据失败:', error);
  }
};



// 更新虚拟章节的已分配资源（在资源数据加载完成后调用）
const updateVirtualChapterResources = async (plate: any) => {
  if (!chapterTreeData.value.length) return;

  const virtualChapter = chapterTreeData.value[0];

  // 清空现有资源
  virtualChapter.resources = [];

  // 加载已分配的资源数据
  try {
    console.log('更新虚拟章节的资源数据, courseInfoId:', props.courseInfoId);
    const res: any = await getCourseChapterResource({
      courseInfoId: props.courseInfoId,
      have_chapter: plateList.value.find(c => c.id == selectedPlateId.value)?.hava_chapter,
      plate_id: plateList.value.find(c => c.id == selectedPlateId.value)?.plate_id
    });
    console.log('虚拟章节资源数据API响应:', res);

    if (res.code === 200 && res.data && res.data.length > 0) {
      // 查找当前板块的资源分配数据
      const plateResourceData = res.data.find((item: any) =>
        item.bm_plate_id === plate.plate_id && item.resource_ids
      );

      if (plateResourceData && plateResourceData.resource_ids) {
        console.log('找到板块资源数据:', plateResourceData);

        // 解析resource_ids
        let resourceIds: any;
        if (typeof plateResourceData.resource_ids === 'string') {
          resourceIds = JSON.parse(plateResourceData.resource_ids);
        } else {
          resourceIds = plateResourceData.resource_ids;
        }

        console.log('解析虚拟章节resource_ids:', resourceIds);

        // 处理各种类型的资源
        Object.keys(resourceIds).forEach(resourceType => {
          const resources = resourceIds[resourceType];
          if (Array.isArray(resources)) {
            resources.forEach((resource: any) => {
              let resourceItem: ResourceItem;

              switch (resourceType) {
                case 'video':
                  // 从资源信息映射中获取正确的标题，优先使用node_name
                  const videoResourceKey = `video_${resource.id}`;
                  const videoResourceInfo = resourceInfoMap.value.get(videoResourceKey);
                  let videoTitle = `视频_${resource.id}`;

                  if (videoResourceInfo) {
                    if (videoResourceInfo.node_name) {
                      videoTitle = `视频_${videoResourceInfo.node_name}`;
                    } else {
                      videoTitle = videoResourceInfo.title || videoTitle;
                    }
                  } else if (resource.title) {
                    videoTitle = resource.title;
                  }

                  resourceItem = {
                    id: resource.id,
                    title: videoTitle,
                    type: 'video',
                    sn: resource.sn,
                    course_video_id: resource.id
                  };
                  break;

                case 'dzs':
                  // 从资源信息映射中获取正确的标题
                  const dzsResourceKey = `dzs_${resource.id}`;
                  const dzsResourceInfo = resourceInfoMap.value.get(dzsResourceKey);
                  const dzsTitle = dzsResourceInfo ? dzsResourceInfo.title : `电子书_${resource.id}`;

                  resourceItem = {
                    id: resource.id,
                    title: dzsTitle,
                    type: 'dzs',
                    sn: resource.sn
                  };
                  break;

                case 'job':
                  // 从资源信息映射中获取正确的标题，优先使用fullTitle
                  const jobResourceKey = `job_${resource.id}`;
                  const jobResourceInfo = resourceInfoMap.value.get(jobResourceKey);
                  let jobTitle = `试题_${resource.id}`;

                  if (jobResourceInfo) {
                    jobTitle = jobResourceInfo.fullTitle || jobResourceInfo.title || jobTitle;
                    if (jobTitle.length > 50) {
                      jobTitle = jobTitle.substring(0, 50) + '...';
                    }
                  } else if (resource.title) {
                    jobTitle = resource.title;
                  }

                  resourceItem = {
                    id: resource.id,
                    title: jobTitle,
                    type: 'job',
                    sn: resource.sn,
                    course_job_id: resource.id
                  };
                  break;

                case 'ksdg':
                  // 从资源信息映射中获取正确的标题
                  const ksdgResourceKey = `ksdg_${resource.id}`;
                  const ksdgResourceInfo = resourceInfoMap.value.get(ksdgResourceKey);
                  const ksdgTitle = ksdgResourceInfo ? ksdgResourceInfo.title : `考试大纲_${resource.id}`;

                  resourceItem = {
                    id: resource.id,
                    title: ksdgTitle,
                    type: 'ksdg',
                    sn: resource.sn,
                    course_ksdg_id: resource.id
                  };
                  break;

                case 'jc':
                  // 从资源信息映射中获取正确的标题
                  const jcResourceKey = `jc_${resource.id}`;
                  const jcResourceInfo = resourceInfoMap.value.get(jcResourceKey);
                  const jcTitle = jcResourceInfo ? jcResourceInfo.title : `教材_${resource.id}`;

                  resourceItem = {
                    id: resource.id,
                    title: jcTitle,
                    type: 'jc',
                    sn: resource.sn,
                    course_jc_id: resource.id
                  };
                  break;

                case 'job_source':
                  // 对于job_source，ID应该是 ver + "_" + jobbank_source_id 的组合
                  const resourceId = `${resource.ver || ''}_${resource.jobbank_source_id}`;
                  const jobSourceResourceKey = `job_source_${resourceId}`;
                  const jobSourceResourceInfo = resourceInfoMap.value.get(jobSourceResourceKey);
                  const jobSourceTitle = jobSourceResourceInfo ? jobSourceResourceInfo.title : (resource.title || resource.vertitle || `历年真题_${resource.jobbank_source_id}`);

                  console.log('处理虚拟章节job_source资源:', {
                    resource: resource,
                    resourceId: resourceId,
                    resourceKey: jobSourceResourceKey,
                    resourceInfo: jobSourceResourceInfo,
                    title: jobSourceTitle
                  });

                  resourceItem = {
                    id: resourceId,
                    title: jobSourceTitle,
                    type: 'job_source',
                    sn: resource.sn,
                    jobbank_source_id: resource.jobbank_source_id,
                    // 从保存的数据中获取ver信息
                    ver: resource.ver || (jobSourceResourceInfo ? jobSourceResourceInfo.ver : ''),
                    vertitle: resource.title || resource.vertitle || jobSourceTitle
                  } as any;
                  break;

                case 'dzs_s':
                  // 从资源信息映射中获取正确的标题
                  const dzsSResourceKey = `dzs_s_${resource.id}`;
                  const dzsSResourceInfo = resourceInfoMap.value.get(dzsSResourceKey);
                  const dzsSTitle = dzsSResourceInfo ? dzsSResourceInfo.title : `单电子书_${resource.id}`;

                  resourceItem = {
                    id: resource.id,
                    title: dzsSTitle,
                    type: 'dzs_s',
                    sn: resource.sn,
                    course_dzs_s_id: resource.id
                  };
                  break;

                default:
                  console.warn('虚拟章节中未知的资源类型:', resourceType);
                  return;
              }

              // 检查是否已经存在相同的资源，避免重复添加
              const existingResource = virtualChapter.resources.find((r: any) =>
                r.type === resourceItem.type &&
                r.id === resourceItem.id
              );

              if (!existingResource) {
                virtualChapter.resources.push(resourceItem);
              } else {
                console.log('跳过重复的虚拟章节资源:', resourceItem.type, resourceItem.id);
              }
            });
          }
        });
      }
    }

    console.log('更新后的虚拟章节:', virtualChapter);

    // 虚拟章节更新完成后，重新过滤资源
    filterResources();
  } catch (error) {
    console.error('更新虚拟章节资源数据失败:', error);
  }
};

// 板块变化处理
const handlePlateChange = async (plateId: number) => {
  selectedPlateId.value = plateId;

  // 设置loading状态
  loading.value = true;

  try {
    // 根据选中的板块信息决定如何加载章节数据
    const selectedPlate = plateList.value.find(plate => plate.id === plateId);
  if (selectedPlate) {
    // 解析resource字段，设置可用的资源类型
    if (selectedPlate.resource) {
      // resource可能是 "video,dzs" 或单个类型 "video"
      availableResourceTypes.value = selectedPlate.resource.split(',').map((type: string) => type.trim());
    } else {
      // 如果没有resource字段，默认支持所有类型
      availableResourceTypes.value = ['video', 'dzs', 'dzs_s', 'ksdg', 'jc', 'job', 'job_source'];
    }

    console.log('选中板块:', selectedPlate);
    console.log('可用资源类型:', availableResourceTypes.value);

    // 重置资源过滤器
    resourceFilter.value = '';

    if (selectedPlate.hava_chapter === 1) {
      // 需要分章节，先加载资源数据（填充resourceInfoMap），然后加载章节数据
      if (selectedVer.value && selectedCourseId.value) {
        await loadResourceData();
      }
      loadChapterData();
    } else {
      // 不需要分章节，先创建空的虚拟章节
      const virtualChapter: ChapterNode = {
        id: `virtual_chapter_${selectedPlate.id}`,
        label: `${selectedPlate.title} - 资源分配`,
        isLeaf: true,
        chapterId: selectedPlate.id,
        courseInfoId: props.courseInfoId || 0,
        resources: []
      };
      chapterTreeData.value = [virtualChapter];

      // 虚拟章节创建后，也需要过滤资源
      filterResources();
    }

    // 如果右侧两个select都有值，重新加载右侧表格数据
    if (selectedVer.value && selectedCourseId.value) {
      console.log('板块变化，重新加载右侧资源数据');
      await loadResourceData();

      // 对于虚拟章节，在资源数据加载完成后更新已分配的资源
      if (selectedPlate.hava_chapter === 0) {
        await updateVirtualChapterResources(selectedPlate);
      }
    } else {
      // 如果没有选中课程，但已经有章节数据，也需要重新过滤左侧数据
      if (chapterTreeData.value.length > 0) {
        filterResources();
      }
    }
  }
  } catch (error) {
    console.error('板块变化处理失败:', error);
    ElMessage.error('加载数据失败');
  } finally {
    // 无论成功还是失败，都要重置loading状态
    loading.value = false;
  }
};

// 版本变化处理
const handleVerChange = async (ver: string) => {
  loading.value = true;

  try {
    selectedVer.value = ver;
    selectedCourseId.value = null;
    await getCourseData();
    // 清空资源数据
    resourceTreeData.value = [];
    filteredResourceTreeData.value = [];
  } catch (error) {
    console.error('版本变化处理失败:', error);
    ElMessage.error('加载数据失败');
  } finally {
    loading.value = false;
  }
};

// 课程变化处理
const handleCourseChange = async (courseId: number) => {
  loading.value = true;

  try {
    selectedCourseId.value = courseId;
    // 重新加载资源数据
    await loadResourceData();

    // 对于虚拟章节，在资源数据加载完成后更新已分配的资源
    const selectedPlate = plateList.value.find(plate => plate.id === selectedPlateId.value);
    if (selectedPlate && selectedPlate.hava_chapter === 0) {
      await updateVirtualChapterResources(selectedPlate);
    }
  } catch (error) {
    console.error('课程变化处理失败:', error);
    ElMessage.error('加载数据失败');
  } finally {
    loading.value = false;
  }
};

// Load data
const loadData = async () => {
  loading.value = true;

  try {
    // 加载板块数据和版本数据
    await Promise.all([
      getPlateData(),
      getVerData()
    ]);

    if (!props.courseBaseId) {
      console.log('没有courseBaseId，使用测试数据');

    // 添加测试章节数据（章节-小节结构）
    chapterTreeData.value = [
      {
        id: 'chapter_174',
        label: '第一章 行政法学的基本范畴',
        isLeaf: false,
        chapterId: 174,
        courseInfoId: 23,
        resources: [],
        children: [
          {
            id: 'section_192',
            label: '0101 行政',
            isLeaf: true,
            chapterId: 192,
            courseInfoId: 23,
            resources: [
              {
                id: 101,
                title: '[第一章] 已分配的测试视频1',
                type: 'video',
                course_video_id: 101,
                sn: 1
              }
            ]
          }
        ]
      },
      {
        id: 'chapter_2',
        label: '第二章 测试空章节',
        isLeaf: false,
        chapterId: 2,
        courseInfoId: 23,
        resources: [],
        children: []
      }
    ];

    // 添加测试资源数据
    resourceTreeData.value = [
      {
        id: 'resource_chapter_1',
        label: '第一章 测试资源章节',
        title: '测试资源章节',
        type: 'chapter',
        isResource: false,
        children: [
          {
            id: 'video_101',
            label: '[第一章] 已分配的测试视频1',
            title: '[第一章] 已分配的测试视频1',
            type: 'video',
            isResource: true,
            course_video_id: 101
          },
          {
            id: 'video_102',
            label: '[第一章] 未分配的测试视频2',
            title: '[第一章] 未分配的测试视频2',
            type: 'video',
            isResource: true,
            course_video_id: 102
          },
          {
            id: 'dzs_201',
            label: '[第一章] 测试电子书1',
            title: '[第一章] 测试电子书1',
            type: 'dzs',
            isResource: true,
            course_dzs_id: 201
          },
          {
            id: 'dzs_202',
            label: '[第一章] 测试电子书2',
            title: '[第一章] 测试电子书2',
            type: 'dzs',
            isResource: true,
            course_dzs_id: 202
          },
          {
            id: 'job_301',
            label: '[第一章] 测试试题1',
            title: '[第一章] 测试试题1',
            type: 'job',
            isResource: true,
            course_job_id: 301
          }
        ]
      },
      {
        id: 'resource_chapter_empty',
        label: '第二章 空章节',
        title: '空章节',
        type: 'chapter',
        isResource: false,
        children: []
      }
    ];

      // 过滤已分配的资源
      filterAssignedResources();
      return;
    }

    // 如果选中了板块，触发板块变化处理
    if (selectedPlateId.value) {
      await handlePlateChange(selectedPlateId.value);
    }

    // 只有在有选中课程时才加载资源数据
    if (selectedCourseId.value) {
      await loadResourceData();
      // 数据加载完成后，过滤掉已分配的资源
      filterAssignedResources();
    }
  } catch (error) {
    console.error('加载数据失败:', error);
    ElMessage.error('加载数据失败');
  } finally {
    loading.value = false;
  }
};

// Load chapter data
const loadChapterData = async () => {
  try {
    console.log('开始加载章节数据, courseBaseId:', props.courseBaseId);

    // 确保资源数据已经加载完成（resourceInfoMap已填充）
    if (selectedCourseId.value && resourceInfoMap.value.size === 0) {
      console.log('资源信息映射为空，先加载资源数据');
      await loadResourceData();
    }

    const res: any = await getCourseChapterResource({
      courseInfoId: props.courseInfoId,
      have_chapter: plateList.value.find(c => c.id == selectedPlateId.value)?.hava_chapter,
      plate_id: plateList.value.find(c => c.id == selectedPlateId.value)?.plate_id
    });
    console.log('章节数据API响应:', res);
    if (res.code === 200) {
      const processedData = processChapterData(res.data || []);
      console.log('处理后的章节数据:', processedData);
      chapterTreeData.value = processedData;

      // 章节数据加载完成后，重新过滤
      filterResources();
    }
  } catch (error) {
    console.error('加载章节数据失败:', error);
  }
};

// Load resource data
const loadResourceData = async () => {
  // 如果没有选中课程，不加载资源数据
  if (!selectedCourseId.value) {
    resourceTreeData.value = [];
    filteredResourceTreeData.value = [];
    return;
  }

  // 如果没有可用的资源类型，不加载数据
  if (availableResourceTypes.value.length === 0) {
    resourceTreeData.value = [];
    filteredResourceTreeData.value = [];
    return;
  }

  try {
    console.log('开始加载资源数据，可用类型:', availableResourceTypes.value);

    // 根据可用的资源类型动态构建API调用
    const apiCalls: Promise<any>[] = [];
    const resourceTypeMap: string[] = [];

    if (availableResourceTypes.value.includes('video')) {
      apiCalls.push(getCourseChapterVideos({ course_jc_id: selectedCourseId.value }));
      resourceTypeMap.push('video');
    }

    if (availableResourceTypes.value.includes('dzs')) {
      apiCalls.push(getCourseChapterDzs({ course_jc_id: selectedCourseId.value }));
      resourceTypeMap.push('dzs');
    }

    if (availableResourceTypes.value.includes('dzs_s')) {
      apiCalls.push(getCourseChapterDzs_s({ course_jc_id: selectedCourseId.value }));
      resourceTypeMap.push('dzs_s');
    }

    if (availableResourceTypes.value.includes('ksdg')) {
      apiCalls.push(getCourseKsdg({ course_jc_id: selectedCourseId.value }));
      resourceTypeMap.push('ksdg');
    }

    if (availableResourceTypes.value.includes('jc')) {
      // jc数据需要传入course_code，值为右侧课程选择select的id
      const selectedCourse = courseList.value.find(course => course.id === selectedCourseId.value);
      const courseCode = selectedCourse ? selectedCourse.course_code : selectedCourseId.value;
      apiCalls.push(getCourseJc({ course_code: courseCode }));
      resourceTypeMap.push('jc');
    }

    if (availableResourceTypes.value.includes('job')) {
      apiCalls.push(getCourseChapterJobs({ course_jc_id: selectedCourseId.value }));
      resourceTypeMap.push('job');
    }

    if (availableResourceTypes.value.includes('job_source')) {
      // job_source数据需要传入course_base_id
      const selectedCourse = courseList.value.find(course => course.id === selectedCourseId.value);
      const courseBaseId = selectedCourse ? selectedCourse.course_base_id : selectedCourseId.value;
      apiCalls.push(getCourseJobSource({ course_base_id: courseBaseId }));
      resourceTypeMap.push('job_source');
    }

    // 并行调用所有需要的API
    const results = await Promise.all(apiCalls);

    console.log('API调用结果:', results);

    // 合并所有资源数据
    const allResourceData: any[] = [];

    results.forEach((res, index) => {
      const resourceType = resourceTypeMap[index];
      console.log(`${resourceType}数据:`, res);

      if (res && res.code === 200 && res.data) {
        allResourceData.push(...res.data.map((item: any) => ({
          ...item,
          resourceType
        })));
      }
    });

    console.log('合并后的资源数据:', allResourceData);

    resourceTreeData.value = processResourceData(allResourceData);

    // 提取job类型的过滤选项
    extractJobFilterOptions();

    // 数据加载完成后，调用过滤函数更新显示数据（包含applyResourceFilter）
    filterResources();
  } catch (error) {
    console.error('加载资源数据失败:', error);
  }
};

// Process chapter data
const processChapterData = (data: any[]): ChapterNode[] => {
  console.log('处理章节数据:', data);

  if (!data || data.length === 0) {
    console.log('没有章节数据');
    return [];
  }

  // 分离章和节
  const chapters = data.filter(item => item.pid === 0); // 章
  const sections = data.filter(item => item.pid !== 0); // 节

  const chapterMap = new Map<string, ChapterNode>();

  // 先处理章
  chapters.forEach(chapter => {
    console.log('处理章:', chapter);
    const chapterId = `chapter_${chapter.id}`;

    // 格式化章节显示：chapter_code 01/02/03 -> 第一章/第二章/第三章
    const chapterCode = chapter.chapter_code || '';
    const chapterNum = chapterCode ? parseInt(chapterCode, 10) : 0;
    const chapterDisplay = chapterNum > 0 ? `第${chapterNum}章` : '';
    const chapterLabel = chapterDisplay ?
      `${chapterDisplay} ${chapter.chapter_name || ''}`.trim() :
      (chapter.chapter_name || `章节_${chapter.id}`);

    chapterMap.set(chapterId, {
      id: chapterId,
      label: chapterLabel,
      isLeaf: false, // 章不是叶子节点
      chapterId: chapter.id,
      courseInfoId: chapter.course_info_id,
      resources: [],
      children: []
    });
  });

  // 再处理节
  sections.forEach(section => {
    console.log('处理节:', section);
    const sectionId = `section_${section.id}`;
    const parentChapterId = `chapter_${section.pid}`;

    // 格式化节显示
    const sectionCode = section.chapter_code || '';
    const sectionLabel = `${sectionCode} ${section.chapter_name || ''}`.trim() || `节_${section.id}`;

    const sectionNode: ChapterNode = {
      id: sectionId,
      label: sectionLabel,
      isLeaf: true, // 节是叶子节点，可以分配资源
      chapterId: section.id,
      courseInfoId: section.course_info_id,
      resources: []
    };

    // 添加到对应的章下面
    const parentChapter = chapterMap.get(parentChapterId);
    if (parentChapter && parentChapter.children) {
      parentChapter.children.push(sectionNode);
    }
  });

  // 处理资源分配（章和节都可以分配资源）
  // 先去重，避免同一个章节的resource_ids被重复处理
  const processedItems = new Set<string>();

  data.forEach(item => {
    // 创建唯一标识符，避免重复处理同一个章节的resource_ids
    const itemKey = `${item.id}_${item.pid}`;
    if (processedItems.has(itemKey)) {
      console.log('跳过重复处理的章节:', item.id, item.chapter_name);
      return;
    }
    processedItems.add(itemKey);
    let targetNode: ChapterNode | undefined;

    if (item.pid === 0) {
      // 处理章级别的资源
      const chapterId = `chapter_${item.id}`;
      targetNode = chapterMap.get(chapterId);
    } else {
      // 处理节级别的资源
      const sectionId = `section_${item.id}`;
      const parentChapterId = `chapter_${item.pid}`;
      const parentChapter = chapterMap.get(parentChapterId);

      if (parentChapter && parentChapter.children) {
        targetNode = parentChapter.children.find(child => child.id === sectionId);
      }
    }

    if (!targetNode) return;

    // 处理已有的资源（新的resource_ids JSON格式）
    if (item.resource_ids) {
      try {
        let resourceIds: any;
        if (typeof item.resource_ids === 'string') {
          resourceIds = JSON.parse(item.resource_ids);
        } else {
          resourceIds = item.resource_ids;
        }

        console.log('解析resource_ids:', resourceIds);

        // 处理各种类型的资源
        Object.keys(resourceIds).forEach(resourceType => {
          const resources = resourceIds[resourceType];
          if (Array.isArray(resources)) {
            resources.forEach((resource: any) => {
              let resourceItem: ResourceItem;

              switch (resourceType) {
                case 'video':
                  // 从资源信息映射中获取正确的标题，优先使用node_name
                  // 注意：resource.id 对应的是 course_video_id
                  const resourceKey = `video_${resource.id}`;
                  const resourceInfo = resourceInfoMap.value.get(resourceKey);
                  let videoTitle = `视频_${resource.id}`;

                  if (resourceInfo) {
                    // 优先使用包含node_name的title，如果没有则使用原title
                    if (resourceInfo.node_name) {
                      videoTitle = `视频_${resourceInfo.node_name}`;
                    } else {
                      videoTitle = resourceInfo.title || videoTitle;
                    }
                  } else if (resource.title) {
                    // 如果资源本身有title，使用资源的title
                    videoTitle = resource.title;
                  }

                  resourceItem = {
                    id: resource.id, // 这是course_video_id
                    title: videoTitle,
                    type: 'video',
                    sn: resource.sn,
                    course_video_id: resource.id // course_video_id
                  };
                  break;

                case 'dzs':
                  // 从资源信息映射中获取正确的标题
                  const dzsResourceKey = `dzs_${resource.id}`;
                  const dzsResourceInfo = resourceInfoMap.value.get(dzsResourceKey);
                  const dzsTitle = dzsResourceInfo ? dzsResourceInfo.title : `电子书_${resource.id}`;

                  resourceItem = {
                    id: resource.id,
                    title: dzsTitle,
                    type: 'dzs',
                    sn: resource.sn
                  };
                  break;

                case 'dzs_s':
                  // 从资源信息映射中获取正确的标题
                  const dzsSResourceKey = `dzs_s_${resource.id}`;
                  const dzsSResourceInfo = resourceInfoMap.value.get(dzsSResourceKey);
                  const dzsSTitle = dzsSResourceInfo ? dzsSResourceInfo.title : `电子书_${resource.id}`;

                  resourceItem = {
                    id: resource.id,
                    title: dzsSTitle,
                    type: 'dzs_s',
                    sn: resource.sn
                  };
                  break;

                case 'job':
                  // 从资源信息映射中获取正确的标题，优先使用fullTitle
                  const jobResourceKey = `job_${resource.id}`;
                  const jobResourceInfo = resourceInfoMap.value.get(jobResourceKey);
                  let jobTitle = `试题_${resource.id}`;

                  if (jobResourceInfo) {
                    // 优先使用完整标题，如果没有则使用截断的标题
                    jobTitle = jobResourceInfo.fullTitle || jobResourceInfo.title || jobTitle;
                    // 如果完整标题过长，截断显示
                    if (jobTitle.length > 50) {
                      jobTitle = jobTitle.substring(0, 50) + '...';
                    }
                  } else if (resource.title) {
                    // 如果资源本身有title，使用资源的title
                    jobTitle = resource.title;
                  }

                  resourceItem = {
                    id: resource.id,
                    title: jobTitle,
                    type: 'job',
                    sn: resource.sn,
                    course_job_id: resource.id
                  };
                  break;

                case 'ksdg':
                  // 从资源信息映射中获取正确的标题
                  const ksdgResourceKey = `ksdg_${resource.id}`;
                  const ksdgResourceInfo = resourceInfoMap.value.get(ksdgResourceKey);
                  const ksdgTitle = ksdgResourceInfo ? ksdgResourceInfo.title : `考试大纲_${resource.id}`;

                  resourceItem = {
                    id: resource.id,
                    title: ksdgTitle,
                    type: 'ksdg',
                    sn: resource.sn,
                    course_ksdg_id: resource.id
                  };
                  break;

                case 'jc':
                  // 从资源信息映射中获取正确的标题
                  const jcResourceKey = `jc_${resource.id}`;
                  const jcResourceInfo = resourceInfoMap.value.get(jcResourceKey);
                  const jcTitle = jcResourceInfo ? jcResourceInfo.title : `教材_${resource.id}`;

                  resourceItem = {
                    id: resource.id,
                    title: jcTitle,
                    type: 'jc',
                    sn: resource.sn,
                    course_jc_id: resource.id
                  };
                  break;

                case 'dzs_s':
                  // 从资源信息映射中获取正确的标题
                  const dzsSResourceKey2 = `dzs_s_${resource.id}`;
                  const dzsSResourceInfo2 = resourceInfoMap.value.get(dzsSResourceKey2);
                  const dzsSTitle2 = dzsSResourceInfo2 ? dzsSResourceInfo2.title : `电子书_${resource.id}`;

                  resourceItem = {
                    id: resource.id,
                    title: dzsSTitle2,
                    type: 'dzs_s',
                    sn: resource.sn,
                    course_dzs_s_id: resource.id
                  };
                  break;

                case 'job_source':
                  // 对于job_source，ID应该是 ver + "_" + jobbank_source_id 的组合
                  const resourceId = `${resource.ver || ''}_${resource.jobbank_source_id}`;
                  const jobSourceResourceKey = `job_source_${resourceId}`;
                  const jobSourceResourceInfo = resourceInfoMap.value.get(jobSourceResourceKey);
                  const jobSourceTitle = jobSourceResourceInfo ? jobSourceResourceInfo.title : (resource.title || resource.vertitle || `历年真题_${resource.jobbank_source_id}`);

                  console.log('处理job_source资源:', {
                    resource: resource,
                    resourceId: resourceId,
                    resourceKey: jobSourceResourceKey,
                    resourceInfo: jobSourceResourceInfo,
                    title: jobSourceTitle
                  });

                  resourceItem = {
                    id: resourceId,
                    title: jobSourceTitle,
                    type: 'job_source',
                    sn: resource.sn,
                    jobbank_source_id: resource.jobbank_source_id,
                    // 从保存的数据中获取ver信息
                    ver: resource.ver || (jobSourceResourceInfo ? jobSourceResourceInfo.ver : ''),
                    vertitle: resource.title || resource.vertitle || jobSourceTitle
                  } as any;
                  break;

                default:
                  console.warn('未知的资源类型:', resourceType);
                  return;
              }

              // 检查是否已经存在相同的资源，避免重复添加
              const existingResource = targetNode!.resources.find((r: any) =>
                r.type === resourceItem.type &&
                r.id === resourceItem.id
              );

              if (!existingResource) {
                targetNode!.resources.push(resourceItem);
              } else {
                console.log('跳过重复资源:', resourceItem.type, resourceItem.id);
              }
            });
          }
        });
      } catch (e) {
        console.error('处理resource_ids失败:', e, item.resource_ids);
      }
    }
  });

  const result = Array.from(chapterMap.values());
  console.log('最终章节数据:', result);
  return result;
};

// Process resource data
const processResourceData = (data: any[]): ResourceNode[] => {
  const chapterMap = new Map<string, ResourceNode>();

  // 清空并重建资源信息映射
  resourceInfoMap.value.clear();
  
  data.forEach(item => {
    let chapterKey = '';
    let chapterCode = '';
    let resourceId = '';
    let resourceTitle = '';
    let resourceType = item.resourceType;

    // 根据资源类型处理不同的数据结构
    if (resourceType === 'video') {
      chapterCode = item.chapter_code || '';
      // 格式化章节显示：01 -> 第一章, 02 -> 第二章
      const chapterNum = chapterCode ? parseInt(chapterCode, 10) : 0;
      const chapterDisplay = chapterNum > 0 ? `第${chapterNum}章` : '其他';
      chapterKey = item.chapter_name ? `${chapterDisplay} ${item.chapter_name}` : chapterDisplay;
      resourceId = item.course_video_id; // 使用course_video_id作为资源ID
      // 视频标题优先使用node_name，如果没有则使用course_video_title
      if (item.node_name) {
        resourceTitle = `视频_${item.node_name}`;
      } else if (item.course_video_title) {
        resourceTitle = `${item.course_video_title.split("_")[1]}`;
      } else {
        resourceTitle = `视频_${item.course_video_id}`;
      }
    } else if (resourceType === 'dzs') {
      chapterCode = item.chap_num || '';
      const chapterNum = chapterCode ? parseInt(chapterCode, 10) : 0;
      const chapterDisplay = chapterNum > 0 ? `第${chapterNum}章` : (item.ver || '其他');
      // 如果有 chap_title，则显示章节标题
      chapterKey = item.chap_title ?
        `${chapterDisplay} ${item.chap_title}`.trim() :
        chapterDisplay;
      resourceId = item.id; // 使用章节的ID，不是course_dzs_id
      // 截取电子书内容的前50个字符作为标题
      resourceTitle = item.chap_content ? item.chap_content.substring(0, 50).replace(/[#\n\r]/g, '').trim() + '...' : `电子书_${item.id}`;
    } else if (resourceType === 'dzs_s') {
      // dzs_s数据不需要分章节，只用列出来title
      chapterKey = '电子书';
      chapterCode = '';
      resourceId = item.id;
      // 优先使用title，如果没有则使用menu，最后使用content的前50个字符
      if (item.title) {
        resourceTitle = item.title;
      } else if (item.menu) {
        resourceTitle = item.menu;
      } else if (item.content) {
        resourceTitle = item.content.substring(0, 50).replace(/[#\n\r]/g, '').trim() + '...';
      } else {
        resourceTitle = `电子书_${item.id}`;
      }
    } else if (resourceType === 'job') {
      chapterKey = item.chapter_mc || (item.ver || '其他');
      chapterCode = item.chapter_no || '';
      resourceId = item.id;
      // 从title对象中提取标题（可能是对象或JSON字符串）
      try {
        let titleObj;
        if (typeof item.title === 'object') {
          titleObj = item.title;
        } else {
          titleObj = JSON.parse(item.title || '{}');
        }
        const fullTitle = titleObj.title || `试题_${item.id}`;
        // 限制显示50个字符
        resourceTitle = fullTitle.length > 50 ? fullTitle.substring(0, 50) + '...' : fullTitle;
      } catch (e) {
        resourceTitle = `试题_${item.id}`;
      }
    } else if (resourceType === 'ksdg') {
      // ksdg数据不需要分章节，只用列出来title
      chapterKey = '考试大纲';
      chapterCode = '';
      resourceId = item.id;
      resourceTitle = item.title || `考试大纲_${item.id}`;
    } else if (resourceType === 'jc') {
      // jc数据按ver来分章节，列出来title
      chapterKey = item.ver ? `版本 ${item.ver}` : '其他版本';
      chapterCode = item.ver || '';
      resourceId = item.id;
      resourceTitle = item.textbook_name || item.course_name || `教材_${item.id}`;
    } else if (resourceType === 'job_source') {
      // job_source数据按ver来分章节，列出来vertitle，使用新的id字段
      chapterKey = item.ver ? `${item.ver}` : '其他版本';
      chapterCode = item.ver || '';
      resourceId = item.id; // 使用新的id字段作为唯一标识
      resourceTitle = item.vertitle || `历年真题_${item.id}`;
    }

    if (!chapterKey) return;

    // 创建或获取章节
    if (!chapterMap.has(chapterKey)) {
      chapterMap.set(chapterKey, {
        id: `resource_chapter_${resourceType}_${chapterCode || Date.now()}`,
        label: chapterKey,
        title: chapterKey,
        type: 'chapter',
        isResource: false,
        children: []
      });
    }

    const chapter = chapterMap.get(chapterKey)!;

    if (resourceId && resourceTitle) {
      // 存储资源信息到映射中
      const resourceKey = `${resourceType}_${resourceId}`;
      const resourceInfo: any = {
        id: resourceId, // 对于video类型，这是course_video_id
        title: resourceTitle,
        type: resourceType,
        chapter_name: chapterKey,
        chapter_code: chapterCode
      };

      // 对于video类型，额外保存node_name信息
      if (resourceType === 'video') {
        resourceInfo.node_name = item.node_name || '';
        resourceInfo.course_video_title = item.course_video_title || '';
        resourceInfo.course_video_id = item.course_video_id; // 明确保存course_video_id
        resourceInfo.node_id = item.id; // 保存节点ID以备用
        resourceInfo.mp4_url = item.mp4_url || ''; // 保存视频URL
        resourceInfo.chapter_name = item.chapter_name || '';
        resourceInfo.course_name = item.course_name || '';
        resourceInfo.course_code = item.course_code || '';
        resourceInfo.chapter_code = item.chapter_code || '';
        resourceInfo.node_code = item.node_code || '';
        resourceInfo.course_jc_id = item.course_jc_id || '';
      }

      // 对于job类型，额外保存完整标题和其他字段
      if (resourceType === 'job') {
        try {
          let titleObj;
          if (typeof item.title === 'object') {
            titleObj = item.title;
          } else {
            titleObj = JSON.parse(item.title || '{}');
          }
          resourceInfo.fullTitle = titleObj.title || `试题_${item.id}`;
          resourceInfo.competence_level = item.competence_level || '';
          resourceInfo.difficulty_level = item.difficulty_level || '';
          resourceInfo.questiontype = item.questiontype || '';
        } catch (e) {
          resourceInfo.fullTitle = `试题_${item.id}`;
          resourceInfo.competence_level = '';
          resourceInfo.difficulty_level = '';
          resourceInfo.questiontype = '';
        }
      }

      // 对于job_source类型，额外保存ver信息
      if (resourceType === 'job_source') {
        resourceInfo.ver = item.ver || '';
        resourceInfo.vertitle = item.vertitle || resourceTitle;
      }

      // 对于dzs类型，额外保存章节内容信息
      if (resourceType === 'dzs') {
        resourceInfo.chap_content = item.chap_content || '';
        resourceInfo.chap_title = item.chap_title || '';
        resourceInfo.chap_num = item.chap_num || '';
        resourceInfo.course_dzs_id = item.course_dzs_id || '';
        resourceInfo.ver = item.ver || '';
      }

      // 对于dzs_s类型，额外保存电子书内容信息
      if (resourceType === 'dzs_s') {
        resourceInfo.content = item.content || '';
        resourceInfo.title = item.title || '';
        resourceInfo.menu = item.menu || '';
        resourceInfo.zsd = item.zsd || '';
        resourceInfo.url = item.url || '';
        resourceInfo.zsd_key = item.zsd_key || '';
        resourceInfo.execute_id = item.execute_id || '';
        resourceInfo.execute_status = item.execute_status || '';
        resourceInfo.execute_url = item.execute_url || '';
        resourceInfo.course_jc_id = item.course_jc_id || '';
        resourceInfo.course_base_id = item.course_base_id || '';
        resourceInfo.status = item.status || '';
        resourceInfo.create_date = item.create_date || '';
        resourceInfo.create_user = item.create_user || '';
      }

      // 对于ksdg类型，额外保存content信息
      if (resourceType === 'ksdg') {
        resourceInfo.content = item.content || '';
        resourceInfo.url = item.url || '';
        resourceInfo.execute_status = item.execute_status || '';
        resourceInfo.execute_url = item.execute_url || '';
      }

      // 对于jc类型，额外保存教材相关信息
      if (resourceType === 'jc') {
        resourceInfo.textbook_name = item.textbook_name || '';
        resourceInfo.textbook_editor = item.textbook_editor || '';
        resourceInfo.publication_info = item.publication_info || '';
        resourceInfo.textbook_url = item.textbook_url || '';
        resourceInfo.url = item.url || '';
        resourceInfo.course_code = item.course_code || '';
        resourceInfo.course_credits = item.course_credits || '';
        resourceInfo.course_type = item.course_type || '';
        resourceInfo.ver = item.ver || '';
        resourceInfo.course_name = item.course_name || '';
        resourceInfo.course_base_id = item.course_base_id || '';
        resourceInfo.status = item.status || '';
      }

      resourceInfoMap.value.set(resourceKey, resourceInfo);

      const resourceNode = {
        id: resourceKey,
        label: resourceTitle,
        title: resourceTitle,
        type: resourceType,
        isResource: true,
        course_video_id: resourceType === 'video' ? Number(resourceId) : undefined,
        course_dzs_id: resourceType === 'dzs' ? Number(item.course_dzs_id) : undefined, // 保存电子书ID
        course_job_id: resourceType === 'job' ? Number(resourceId) : undefined,
        course_ksdg_id: resourceType === 'ksdg' ? Number(resourceId) : undefined,
        course_jc_id: resourceType === 'jc' ? Number(resourceId) : undefined,
        jobbank_source_id: resourceType === 'job_source' ? Number(resourceId) : undefined,
        // 对于电子书，还需要保存章节ID
        dzs_chapter_id: resourceType === 'dzs' ? Number(resourceId) : undefined,
        // 对于dzs_s类型，保存电子书ID
        course_dzs_s_id: resourceType === 'dzs_s' ? Number(resourceId) : undefined,
        // 对于video类型，保存node_name信息以便拖拽时使用
        node_name: resourceType === 'video' ? item.node_name : undefined,
        // 对于job_source，保存额外的信息
        ver: resourceType === 'job_source' ? item.ver : undefined,
        vertitle: resourceType === 'job_source' ? item.vertitle : undefined
      };

      // 调试信息：输出jc、ksdg和dzs_s资源的详细信息
      if (resourceType === 'ksdg' || resourceType === 'jc' || resourceType === 'dzs_s') {
        console.log(`创建${resourceType}资源节点:`, resourceNode);
      }

      chapter.children!.push(resourceNode);
    }
  });

  return Array.from(chapterMap.values());
};

// 过滤掉已分配的资源
const filterAssignedResources = () => {
  console.log('开始过滤已分配的资源');

  // 使用统一的过滤逻辑
  filteredResourceTreeData.value = getUnassignedResources();

  console.log('过滤后的资源数据:', filteredResourceTreeData.value);
};

// Handle save
const handleSave = async () => {
  if (!props.courseInfoId) {
    ElMessage.error('缺少课程信息ID');
    return;
  }

  try {
    saving.value = true;

    // 收集所有需要保存的数据
    const allSaveData: any[] = [];

    // 收集全局资源（ksdg、jc和job_source，不需要章节ID）
    // 对于虚拟章节，video、dzs、job也会被作为全局资源处理
    const globalResources: Record<string, any[]> = {
      ksdg: [],
      jc: [],
      job_source: [],
      video: [],
      dzs: [],
      dzs_s: [],
      job: []
    };

    // 检查是否为虚拟章节（移到外层，避免作用域问题）
    const selectedPlateData = plateList.value.find(c => c.id == selectedPlateId.value);
    const isVirtualChapter = selectedPlateData?.hava_chapter === 0;
    console.log('当前是否为虚拟章节:', isVirtualChapter);

    // 收集所有章节和节的资源分配
    const processNode = (node: ChapterNode) => {
      console.log('处理节点:', node.label, '资源数量:', node.resources?.length || 0);
      console.log('节点资源详情:', node.resources);

      // 处理当前节点的资源（章或节都可以有资源）
      if (node.resources && node.resources.length > 0) {
        // 按类型分组资源
        const chapterResourcesByType: Record<string, any[]> = {
          video: [],
          dzs: [],
          job: []
        };

        // 分类收集资源
        node.resources.forEach((r: ResourceItem, index: number) => {
          console.log(`处理资源 ${index + 1}:`, r);

          const resourceData = {
            id: r.id,
            sn: r.sn || (index + 1)
          };

          // 对于虚拟章节，所有资源都作为全局资源处理
          if (isVirtualChapter) {
            console.log('虚拟章节资源，作为全局资源处理:', r.type);

            if (r.type === 'video' && r.course_video_id) {
              globalResources.video = globalResources.video || [];
              globalResources.video.push({
                id: r.course_video_id,
                sn: r.sn || (index + 1)
              });
            } else if (r.type === 'dzs') {
              globalResources.dzs = globalResources.dzs || [];
              globalResources.dzs.push(resourceData);
            } else if (r.type === 'dzs_s' && r.course_dzs_s_id) {
              globalResources.dzs_s = globalResources.dzs_s || [];
              globalResources.dzs_s.push({
                id: r.course_dzs_s_id,
                sn: r.sn || (index + 1)
              });
            } else if (r.type === 'job') {
              globalResources.job = globalResources.job || [];
              globalResources.job.push(resourceData);
            } else if (r.type === 'ksdg' && r.course_ksdg_id) {
              globalResources.ksdg.push({
                id: r.course_ksdg_id,
                sn: r.sn || (index + 1)
              });
            } else if (r.type === 'jc' && r.course_jc_id) {
              globalResources.jc.push({
                id: r.course_jc_id,
                sn: r.sn || (index + 1)
              });
            } else if (r.type === 'job_source') {
              // job_source资源处理逻辑
              console.log('收集job_source全局资源，完整对象:', r);

              let jobbank_source_id = (r as any).jobbank_source_id;
              if (!jobbank_source_id && r.id) {
                const idStr = String(r.id);
                const parts = idStr.split('_');
                jobbank_source_id = parseInt(parts[parts.length - 1]);
              }

              if (typeof jobbank_source_id === 'string' && jobbank_source_id.includes('_')) {
                const parts = jobbank_source_id.split('_');
                jobbank_source_id = parseInt(parts[parts.length - 1]) || jobbank_source_id;
              }

              if (jobbank_source_id) {
                globalResources.job_source.push({
                  jobbank_source_id: jobbank_source_id,
                  ver: (r as any).ver || '',
                  title: r.title,
                  sn: r.sn || (index + 1)
                });
              }
            }
          } else {
            // 非虚拟章节，按原来的逻辑处理
            if (r.type === 'video' && r.course_video_id) {
              console.log('收集video资源:', r.course_video_id);
              chapterResourcesByType.video.push({
                id: r.course_video_id,
                sn: r.sn || (index + 1)
              });
            } else if (r.type === 'dzs') {
              console.log('收集dzs资源:', r.id);
              chapterResourcesByType.dzs.push(resourceData);
            } else if (r.type === 'dzs_s' && r.course_dzs_s_id) {
              // dzs_s资源不需要章节ID，收集到全局资源中
              console.log('收集dzs_s全局资源:', r.course_dzs_s_id);
              globalResources.dzs_s.push({
                id: r.course_dzs_s_id,
                sn: r.sn || (index + 1)
              });
            } else if (r.type === 'job') {
              console.log('收集job资源:', r.id);
              chapterResourcesByType.job.push(resourceData);
            } else if (r.type === 'ksdg' && r.course_ksdg_id) {
              // ksdg资源不需要章节ID，收集到全局资源中
              console.log('收集ksdg全局资源:', r.course_ksdg_id);
              globalResources.ksdg.push({
                id: r.course_ksdg_id,
                sn: r.sn || (index + 1)
              });
            } else if (r.type === 'jc' && r.course_jc_id) {
              // jc资源不需要章节ID，收集到全局资源中
              console.log('收集jc全局资源:', r.course_jc_id);
              globalResources.jc.push({
                id: r.course_jc_id,
                sn: r.sn || (index + 1)
              });
            } else if (r.type === 'job_source') {
              // job_source资源不需要章节ID，收集到全局资源中
              console.log('收集job_source全局资源，完整对象:', r);

              // 尝试获取 jobbank_source_id
              let jobbank_source_id = (r as any).jobbank_source_id;

              // 如果没有 jobbank_source_id，尝试从 id 中提取
              if (!jobbank_source_id && r.id) {
                // 从 "job_source_2024年04月真题_3" 中提取最后的数字 "3"
                const idStr = String(r.id);
                const parts = idStr.split('_');
                jobbank_source_id = parseInt(parts[parts.length - 1]);
                console.log('从ID中提取的jobbank_source_id:', jobbank_source_id);
              }

              // 如果 jobbank_source_id 是字符串格式的完整ID，需要提取数字部分
              if (typeof jobbank_source_id === 'string' && jobbank_source_id.includes('_')) {
                const parts = jobbank_source_id.split('_');
                jobbank_source_id = parseInt(parts[parts.length - 1]) || jobbank_source_id;
                console.log('处理后的jobbank_source_id:', jobbank_source_id);
              }

              console.log('最终使用的jobbank_source_id:', jobbank_source_id);

              if (jobbank_source_id) {
                globalResources.job_source.push({
                  jobbank_source_id: jobbank_source_id,
                  ver: (r as any).ver || '',
                  title: r.title,
                  sn: r.sn || (index + 1)
                });
                console.log('已添加job_source到全局资源');
              } else {
                console.warn('无法获取jobbank_source_id，跳过此资源:', r);
              }
            } else {
              console.warn('未处理的资源类型或缺少必要字段:', r);
            }
          }
        });

        // 按资源类型分别保存，每种资源类型一条记录
        const nodeType = node.isLeaf ? '节' : '章';

        Object.keys(chapterResourcesByType).forEach(resourceType => {
          if (chapterResourcesByType[resourceType].length > 0) {
            // 为每种资源类型创建单独的resource_ids JSON对象
            const resourceIds: Record<string, any[]> = {};
            resourceIds[resourceType] = chapterResourcesByType[resourceType];

            const saveData = {
              course_info_id: props.courseInfoId,
              ols_course_chapter_id: isVirtualChapter ? null : node.chapterId, // 虚拟章节不需要章节ID
              bm_plate_id: selectedPlateData?.plate_id,
              bm_plate_type_id: selectedPlateData?.bm_plate_type_id,
              resource: resourceType, // 设置resource字段为资源类型
              resource_ids: JSON.stringify(resourceIds)
            };

            console.log(`收集${nodeType}${resourceType}资源 (虚拟章节: ${isVirtualChapter}):`, node.label, saveData);
            console.log(`${resourceType}资源内容:`, resourceIds);

            allSaveData.push(saveData);
          }
        });
      }

      // 递归处理子节点
      if (node.children && node.children.length > 0) {
        for (const child of node.children) {
          processNode(child);
        }
      }
    };

    // 处理所有章节，收集数据 - 使用filteredChapterTreeData，因为这是用户实际操作的数据
    for (const chapter of filteredChapterTreeData.value) {
      processNode(chapter);
    }

    console.log('收集完成后的globalResources:', globalResources);

    // 处理全局资源（ksdg、jc和job_source，不需要章节ID），按资源类型分别保存
    
    Object.keys(globalResources).forEach(resourceType => {
      if (globalResources[resourceType].length > 0) {
        // 为每种全局资源类型创建单独的resource_ids JSON对象
        const resourceIds: Record<string, any[]> = {};
        resourceIds[resourceType] = globalResources[resourceType];

        const globalSaveData = {
          course_info_id: props.courseInfoId,
          ols_course_chapter_id: null, // 全局资源不需要章节ID
          bm_plate_id: selectedPlateData?.plate_id,
          bm_plate_type_id: selectedPlateData?.bm_plate_type_id,
          resource: resourceType, // 设置resource字段为资源类型
          resource_ids: JSON.stringify(resourceIds)
        };

        console.log(`收集全局${resourceType}资源:`, globalSaveData);
        console.log(`全局${resourceType}资源内容:`, resourceIds);

        allSaveData.push(globalSaveData);
      }
    });

    // 批量保存所有数据
    if (allSaveData.length > 0) {
      console.log('批量保存数据:', allSaveData);
      await saveCourseResource(allSaveData);
    } else {
      debugger
      // 即使没有资源分配，也要保存空的记录，确保数据一致性
      console.log('没有资源分配，保存空的resource_ids记录');
      const emptyResourceData = {
        course_info_id: props.courseInfoId,
        ols_course_chapter_id: isVirtualChapter ? null : null, // 对于空数据，章节ID设为null
        bm_plate_id: selectedPlateData?.plate_id,
        bm_plate_type_id: selectedPlateData?.bm_plate_type_id,
        resource: '', // 空的资源类型
        resource_ids: '' // 空的resource_ids
      };

      console.log('保存空资源数据:', emptyResourceData);
      await saveCourseResource([emptyResourceData]);
    }

    ElMessage.success('保存成功');
    emit('save');
    // 保存成功后不关闭对话框，只发出保存事件
  } catch (error) {
    console.error('保存失败:', error);
    ElMessage.error('保存失败');
  } finally {
    saving.value = false;
  }
};

// 清空所有数据
const clearAllData = () => {
  // 清空选择器数据
  selectedPlateId.value = null;
  selectedVer.value = '';
  selectedCourseId.value = null;
  resourceFilter.value = '';

  // 清空列表数据
  plateList.value = [];
  verList.value = [];
  courseList.value = [];
  availableResourceTypes.value = [];

  // 清空树形数据
  chapterTreeData.value = [];
  filteredChapterTreeData.value = [];
  resourceTreeData.value = [];
  filteredResourceTreeData.value = [];

  // 清空资源信息映射
  resourceInfoMap.value.clear();

  // 重置状态
  loading.value = false;
  saving.value = false;

  console.log('已清空所有数据');
};

// 显示批量添加对话框
const showBatchAddDialog = () => {
  batchAddDialog.value.visible = true;
  batchAddDialog.value.method = 'byName';
};

// 处理批量添加
const handleBatchAdd = async () => {
  try {
    batchAddDialog.value.loading = true;

    // 检查是否有可用的章节和资源
    const leftChapters = chapterTreeData.value.filter(chapter => !chapter.isLeaf);
    const rightChapters = filteredResourceTreeData.value;

    if (leftChapters.length === 0) {
      ElMessage.warning('没有可用的左侧章节，无法进行批量添加');
      return;
    }

    if (rightChapters.length === 0) {
      ElMessage.warning('没有可用的右侧资源，无法进行批量添加');
      return;
    }

    // 统计可用资源数量
    const totalResources = rightChapters.reduce((count, chapter) => {
      return count + (chapter.children?.filter(child => child.isResource).length || 0);
    }, 0);

    if (totalResources === 0) {
      ElMessage.warning('没有可分配的资源，无法进行批量添加');
      return;
    }

    console.log(`开始批量添加: ${leftChapters.length} 个左侧章节, ${rightChapters.length} 个右侧章节, ${totalResources} 个资源`);

    if (batchAddDialog.value.method === 'byOrder') {
      await batchAddByOrder();
    } else if (batchAddDialog.value.method === 'byName') {
      await batchAddByName();
    }

    ElMessage.success('批量添加完成');
    batchAddDialog.value.visible = false;
  } catch (error) {
    console.error('批量添加失败:', error);
    ElMessage.error('批量添加失败');
  } finally {
    batchAddDialog.value.loading = false;
  }
};

// 按章节数量对应批量添加
const batchAddByOrder = async () => {
  console.log('=== 按章节数量对应批量添加 ===');

  // 获取左侧章节列表（只要章级别）
  const leftChapters = chapterTreeData.value.filter(chapter => !chapter.isLeaf);
  // 获取右侧资源章节列表
  const rightChapters = filteredResourceTreeData.value;

  console.log('左侧章节:', leftChapters);
  console.log('右侧资源章节:', rightChapters);

  let assignedCount = 0;
  let skippedCount = 0;

  // 先收集所有要分配的资源，避免在循环中修改数组
  const resourceAssignments: Array<{ resource: any, targetChapter: ChapterNode }> = [];

  // 按顺序对应分配 - 右侧第i个章节对应左侧第i个章节
  for (let i = 0; i < rightChapters.length; i++) {
    const rightChapter = rightChapters[i];

    if (rightChapter.children && rightChapter.children.length > 0) {
      // 确定目标章节
      let targetChapter: ChapterNode | null = null;

      if (i < leftChapters.length) {
        // 如果左侧有对应位置的章节，直接使用
        targetChapter = leftChapters[i];
      } else {
        // 如果左侧章节不够，循环使用已有章节
        const targetIndex = i % leftChapters.length;
        targetChapter = leftChapters[targetIndex];
      }

      if (targetChapter) {
        console.log(`将右侧第${i+1}章 "${rightChapter.label}" 的资源分配到左侧 "${targetChapter.label}"`);

        // 收集右侧章节的所有资源（创建副本避免引用问题）
        const resourcesCopy = [...rightChapter.children];
        for (const resource of resourcesCopy) {
          if (resource.isResource) {
            resourceAssignments.push({ resource: { ...resource }, targetChapter });
          }
        }
      } else {
        skippedCount += rightChapter.children.filter(r => r.isResource).length;
        console.warn('无法找到目标章节，跳过右侧章节:', rightChapter.label);
      }
    }
  }

  console.log(`准备分配 ${resourceAssignments.length} 个资源`);

  // 执行所有资源分配
  for (const assignment of resourceAssignments) {
    try {
      const success = assignResourceToChapterBatch(assignment.resource, assignment.targetChapter);
      if (success) {
        assignedCount++;
      } else {
        skippedCount++;
      }
    } catch (error) {
      console.error('分配资源失败:', error);
      skippedCount++;
    }
  }

  console.log(`按顺序批量添加完成: 分配了 ${assignedCount} 个资源，跳过了 ${skippedCount} 个资源`);

  // 批量添加完成后，清理右侧已分配的资源
  batchCleanupAssignedResources(resourceAssignments);

  // 重新过滤资源
  filterResources();
};

// 按章节名称对应批量添加
const batchAddByName = async () => {
  console.log('=== 按章节名称对应批量添加 ===');

  // 获取左侧章节列表（只要章，不要节）
  const leftChapters = chapterTreeData.value.filter(chapter => !chapter.isLeaf);
  // 获取右侧资源章节列表
  const rightChapters = filteredResourceTreeData.value;

  console.log('左侧章节:', leftChapters);
  console.log('右侧资源章节:', rightChapters);

  let assignedCount = 0;
  let skippedCount = 0;
  const processedRightChapters = new Set<string>();

  // 先收集所有要分配的资源
  const resourceAssignments: Array<{ resource: any, targetChapter: ChapterNode }> = [];

  // 按名称对应分配
  for (const leftChapter of leftChapters) {
    // 尝试在右侧找到名称相似的章节
    const matchingRightChapter = findMatchingChapterByName(leftChapter.label, rightChapters);

    if (matchingRightChapter && matchingRightChapter.children && matchingRightChapter.children.length > 0) {
      console.log(`匹配成功: "${leftChapter.label}" <-> "${matchingRightChapter.label}"`);

      // 收集匹配的右侧章节的所有资源（创建副本避免引用问题）
      const resourcesCopy = [...matchingRightChapter.children];
      for (const resource of resourcesCopy) {
        if (resource.isResource) {
          resourceAssignments.push({ resource: { ...resource }, targetChapter: leftChapter });
        }
      }
      processedRightChapters.add(matchingRightChapter.id);
    }
  }

  // 处理未匹配的右侧章节资源 - 分配到第一个左侧章节
  if (leftChapters.length > 0) {
    const fallbackTarget = leftChapters[0];

    for (const rightChapter of rightChapters) {
      if (!processedRightChapters.has(rightChapter.id) && rightChapter.children && rightChapter.children.length > 0) {
        console.log(`未匹配的右侧章节 "${rightChapter.label}" 的资源将分配到 "${fallbackTarget.label}"`);

        // 收集未匹配章节的所有资源（创建副本避免引用问题）
        const resourcesCopy = [...rightChapter.children];
        for (const resource of resourcesCopy) {
          if (resource.isResource) {
            resourceAssignments.push({ resource: { ...resource }, targetChapter: fallbackTarget });
          }
        }
      }
    }
  }

  console.log(`准备分配 ${resourceAssignments.length} 个资源`);

  // 执行所有资源分配
  for (const assignment of resourceAssignments) {
    try {
      const success = assignResourceToChapterBatch(assignment.resource, assignment.targetChapter);
      if (success) {
        assignedCount++;
      } else {
        skippedCount++;
      }
    } catch (error) {
      console.error('分配资源失败:', error);
      skippedCount++;
    }
  }

  console.log(`按名称批量添加完成: 分配了 ${assignedCount} 个资源，跳过了 ${skippedCount} 个资源`);

  // 批量添加完成后，清理右侧已分配的资源
  batchCleanupAssignedResources(resourceAssignments);

  // 重新过滤资源
  filterResources();
};

// 根据名称查找匹配的章节
const findMatchingChapterByName = (leftChapterName: string, rightChapters: ResourceNode[]) => {
  // 提取章节编号和名称关键词
  const leftChapterInfo = extractChapterInfo(leftChapterName);

  for (const rightChapter of rightChapters) {
    const rightChapterInfo = extractChapterInfo(rightChapter.label);

    // 优先按章节编号匹配
    if (leftChapterInfo.number && rightChapterInfo.number &&
        leftChapterInfo.number === rightChapterInfo.number) {
      return rightChapter;
    }

    // 如果没有编号，按名称关键词匹配
    if (leftChapterInfo.keywords.length > 0 && rightChapterInfo.keywords.length > 0) {
      const commonKeywords = leftChapterInfo.keywords.filter(keyword =>
        rightChapterInfo.keywords.some(rightKeyword =>
          rightKeyword.includes(keyword) || keyword.includes(rightKeyword)
        )
      );

      if (commonKeywords.length > 0) {
        return rightChapter;
      }
    }
  }

  return null;
};

// 提取章节信息（编号和关键词）
const extractChapterInfo = (chapterName: string) => {
  // 提取章节编号（如：第一章、第二章、第1章、第2章等）
  const numberMatch = chapterName.match(/第([一二三四五六七八九十\d]+)章/);
  let number = null;

  if (numberMatch) {
    const numStr = numberMatch[1];
    // 转换中文数字为阿拉伯数字
    const chineseNumbers: Record<string, number> = {
      '一': 1, '二': 2, '三': 3, '四': 4, '五': 5,
      '六': 6, '七': 7, '八': 8, '九': 9, '十': 10
    };

    if (chineseNumbers[numStr]) {
      number = chineseNumbers[numStr];
    } else if (/^\d+$/.test(numStr)) {
      number = parseInt(numStr, 10);
    }
  }

  // 提取关键词（去除章节编号后的内容）
  const keywords = chapterName
    .replace(/第[一二三四五六七八九十\d]+章\s*/, '')
    .split(/[\s\-_、，,]/)
    .filter(keyword => keyword.length > 1)
    .map(keyword => keyword.trim());

  return { number, keywords };
};

// Handle close
const handleClose = () => {
  clearAllData();
  dialogVisible.value = false;
  emit('close');
};
</script>

<style lang="scss" scoped>
.assign-resource-dialog {
  :deep(.el-dialog) {
    margin: 0 !important;
    max-height: 100vh;
    max-width: 100vw;
    display: flex;
    flex-direction: column;
  }

  :deep(.el-dialog__header) {
    flex-shrink: 0;
    padding: 20px 20px 0 20px;
  }

  :deep(.el-dialog__body) {
    padding: 20px;
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    min-height: 0;
  }

  :deep(.el-dialog__footer) {
    flex-shrink: 0;
    padding: 0 20px 20px 20px;
  }
}

.dialog-content {
  display: flex;
  flex: 1;
  min-height: 0;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  overflow: hidden;
}

.left-panel,
.right-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.left-panel {
  border-right: 1px solid #e4e7ed;
}

.panel-header {
  padding: 16px 20px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;

  h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #303133;
  }

  .subtitle {
    font-size: 12px;
    color: #909399;
  }

  :deep(.el-radio-group) {
    display: flex;
    gap: 12px;

    .el-radio {
      margin-right: 0;

      .el-radio__label {
        display: flex;
        align-items: center;
        gap: 4px;
        font-size: 12px;

        .el-icon {
          font-size: 14px;
        }
      }
    }
  }
}

.panel-content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 16px;
  min-height: 0;
  max-height: calc(100vh - 300px); /* 为header和footer留出空间 */
}

.chapter-tree,
.resource-tree {
  :deep(.el-tree-node__content) {
    height: auto;
    min-height: 32px;
    padding: 8px 0;
  }
}

.tree-node {
  display: flex;
  align-items: flex-start;
  margin-top:10px;
  justify-content: space-between;
  width: 100%;
  min-height: 32px;
  
  .node-content {
    display: flex;
    align-items: center;
    flex: 1;
    min-width: 0;
  }
  
  .node-icon {
    margin-right: 8px;
    font-size: 16px;
    color: #606266;
    
    &.dzs-icon {
      color: #67c23a;
    }
    
    &.video-icon {
      color: #409eff;
    }
    
    &.job-icon {
      color: #e6a23c;
    }
  }
  
  .node-label {
    flex: 1;
    font-size: 14px;
    color: #303133;
    word-break: break-all;
  }
  
  .resource-tags {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-left: 8px;

    .job-resource-card {
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 6px;
      padding: 8px;

      .job-card-header {
        display: flex;
        align-items: center;
        gap: 6px;
        margin-bottom: 6px;

        .job-icon {
          color: #409eff;
          font-size: 14px;
        }

        .job-title {
          flex: 1;
          font-size: 13px;
          color: #303133;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          cursor: pointer;

          &:hover {
            color: #409eff;
          }
        }

        .remove-btn {
          width: 20px;
          height: 20px;
          font-size: 10px;
          padding: 0;
        }
      }

      .job-card-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 4px;

        .el-tag {
          font-size: 10px;
          height: 18px;
          line-height: 16px;
          padding: 0 4px;
        }
      }
    }

    .resource-tag {
      display: flex;
      align-items: center;
      max-width: 180px; /* 适配10字符标题的宽度 */

      .tag-icon {
        margin-right: 4px;
        font-size: 12px;
      }

      .tag-title {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        max-width: 10em; /* 最多显示10个字符，超过显示省略号 */
      }
    }
  }
  
  .resource-type-tag {
    margin-left: 8px;
  }

  .resource-content {
    flex: 1;
    min-width: 0;

    .job-title {
      display: block;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      margin-bottom: 4px;
    }

    .job-extra-info {
      display: flex;
      flex-wrap: wrap;
      gap: 2px;

      .el-tag {
        font-size: 10px;
        height: 18px;
        line-height: 16px;
        padding: 0 4px;
      }
    }
  }
}

.resource-node {
  &.is-resource {
    cursor: move;

    &:hover {
      background-color: #f5f7fa;
      border-radius: 4px;
    }

    &.dragging {
      opacity: 0.5;
      transform: scale(0.95);
      transition: all 0.2s ease;
    }
  }
}

.chapter-drop-zone {
  transition: all 0.2s ease;

  &.drag-over {
    background-color: #e6f7ff;
    border: 2px dashed #1890ff;
    border-radius: 4px;

    .node-label {
      color: #1890ff;
      font-weight: 600;
    }
  }
}

.dialog-footer {
  padding: 16px 20px;
  text-align: right;
  border-top: 1px solid #e4e7ed;
}

.top-selectors {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;

  .left-selector {
    display: flex;
    align-items: center;
    gap: 8px;

    label {
      font-size: 14px;
      color: #606266;
      white-space: nowrap;
      margin-right: 8px;
    }
  }

  .right-selector {
    display: flex;
    align-items: center;
    gap: 16px;

    label {
      font-size: 14px;
      color: #606266;
      white-space: nowrap;
      margin-right: 8px;
    }
  }
}

.panel-filters {
  padding: 10px 20px 8px 20px;
  border-bottom: 1px solid #ebeef5;
  background-color: #fafafa;
}

.resource-header {
  margin-bottom: 10px;

  .resource-filters {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 8px;
    padding: 8px;
    background-color: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #e9ecef;
  }
}

.batch-add-content {
  .el-radio {
    width: 100%;
    margin-bottom: 16px;

    &:last-child {
      margin-left: -30px;
    }
  }

  .el-radio__label {
    width: 100%;
    padding-left: 8px;
  }
}

// 预览按钮样式
.job-actions, .resource-actions {
  display: flex;
  gap: 4px;
  margin-left: 8px;
}

.preview-btn, .preview-btn-right {
  width: 24px !important;
  height: 24px !important;
  min-height: 24px !important;
  padding: 0 !important;

  .el-icon {
    font-size: 12px;
  }
}

.other-resource-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.job-info-section {
  flex: 1;
}

.other-resource-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;

  .node-label {
    flex: 1;
  }
}

// 预览对话框样式
.preview-dialog {
  .job-preview-wrapper {
    // 使用原有PreviewQuestionBank组件的样式
    // 这里可以添加一些容器样式调整
    min-height: 400px;
  }

  .ksdg-preview-wrapper {
    width: 100%;
    height: calc(100vh - 120px);

    .ksdg-preview-content {
      width: 100%;
      height: 100%;
      padding: 20px;
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }

  .jc-preview-wrapper {
    width: 100%;
    height: calc(100vh - 120px);
    padding: 20px;

    .jc-preview-content {
      width: 100%;
      height: 100%;
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      overflow-y: auto;

      .textbook-display {
        display: flex;
        gap: 40px;
        padding: 30px;
        min-height: 100%;

        .textbook-cover {
          flex-shrink: 0;
          width: 300px;

          .cover-image {
            width: 100%;
            height: auto;
            max-height: 400px;
            object-fit: contain;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            background: #f5f5f5;
          }
        }

        .textbook-info {
          flex: 1;
          min-width: 0;

          .info-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 6px;
            border-left: 4px solid #409eff;

            .info-label {
              font-weight: 600;
              color: #606266;
              min-width: 100px;
              flex-shrink: 0;
              font-size: 14px;
            }

            .info-value {
              color: #303133;
              font-size: 14px;
              line-height: 1.5;
              word-break: break-all;
            }
          }

          .action-buttons {
            margin-top: 30px;
            padding: 20px;
            background: #f0f9ff;
            border-radius: 6px;
            border: 1px solid #e1f5fe;

            .el-button {
              font-size: 14px;
              padding: 10px 20px;
            }
          }
        }
      }

      // 响应式设计
      @media (max-width: 768px) {
        .textbook-display {
          flex-direction: column;
          gap: 20px;

          .textbook-cover {
            width: 100%;
            max-width: 250px;
            margin: 0 auto;
          }
        }
      }
    }
  }

  .video-preview-wrapper {
    width: 100%;
    height: calc(100vh - 120px);
    padding: 20px;

    .video-preview-content {
      width: 100%;
      height: 100%;
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      overflow-y: auto;

      .video-header {
        padding: 20px 30px;
        border-bottom: 1px solid #ebeef5;

        .video-title {
          margin: 0 0 15px 0;
          font-size: 18px;
          font-weight: 600;
          color: #303133;
        }

        .video-info {
          display: flex;
          gap: 8px;
          flex-wrap: wrap;
        }
      }

      .video-player-container {
        padding: 30px;
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 400px;

        .video-player {
          width: 100%;
          max-width: 800px;
          height: auto;
          border-radius: 8px;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
          background: #000;
        }

        .no-video {
          text-align: center;
          color: #909399;

          p {
            margin: 15px 0 0 0;
            font-size: 16px;
          }
        }
      }

      .video-details {
        padding: 0 30px 30px;
        border-top: 1px solid #f0f0f0;
        margin-top: 20px;

        .detail-item {
          display: flex;
          align-items: center;
          margin-bottom: 12px;
          padding: 10px 15px;
          background: #f8f9fa;
          border-radius: 4px;

          .detail-label {
            font-weight: 500;
            color: #606266;
            min-width: 80px;
            flex-shrink: 0;
            font-size: 13px;
          }

          .detail-value {
            color: #303133;
            font-size: 13px;
          }
        }
      }

      // 响应式设计
      @media (max-width: 768px) {
        .video-header {
          padding: 15px 20px;

          .video-title {
            font-size: 16px;
          }
        }

        .video-player-container {
          padding: 20px;
          min-height: 300px;
        }

        .video-details {
          padding: 0 20px 20px;
        }
      }
    }
  }

  .dzs-preview-wrapper {
    width: 100%;
    height: calc(100vh - 120px);
    padding: 20px;

    .dzs-preview-content {
      width: 100%;
      height: 100%;
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      overflow: hidden;
      display: flex;
      flex-direction: column;

      .dzs-header {
        padding: 20px 30px;
        border-bottom: 1px solid #ebeef5;
        flex-shrink: 0;

        .dzs-title {
          margin: 0 0 15px 0;
          font-size: 18px;
          font-weight: 600;
          color: #303133;
        }

        .dzs-info {
          display: flex;
          gap: 8px;
          flex-wrap: wrap;
        }
      }

      .dzs-content {
        flex: 1;
        padding: 20px 30px;
        overflow: hidden;

        // 确保markdown预览组件能够正确显示
        :deep(.v-md-editor-preview) {
          height: 100%;

          .v-md-editor-preview__wrapper {
            height: 100%;
            overflow-y: auto;
            padding: 0;
          }

          // 优化markdown内容样式
          .markdown-body {
            font-size: 14px;
            line-height: 1.6;

            h1, h2, h3, h4, h5, h6 {
              margin-top: 24px;
              margin-bottom: 16px;
              font-weight: 600;
              line-height: 1.25;
            }

            h1 {
              font-size: 2em;
              border-bottom: 1px solid #eaecef;
              padding-bottom: 10px;
            }

            h2 {
              font-size: 1.5em;
              border-bottom: 1px solid #eaecef;
              padding-bottom: 8px;
            }

            p {
              margin-bottom: 16px;
            }

            img {
              max-width: 100%;
              height: auto;
              border-radius: 6px;
              box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            }

            blockquote {
              padding: 0 1em;
              color: #6a737d;
              border-left: 0.25em solid #dfe2e5;
              margin: 0 0 16px 0;
            }

            code {
              padding: 0.2em 0.4em;
              margin: 0;
              font-size: 85%;
              background-color: rgba(27, 31, 35, 0.05);
              border-radius: 3px;
            }

            pre {
              padding: 16px;
              overflow: auto;
              font-size: 85%;
              line-height: 1.45;
              background-color: #f6f8fa;
              border-radius: 6px;
            }
          }
        }
      }

      // 响应式设计
      @media (max-width: 768px) {
        .dzs-header {
          padding: 15px 20px;

          .dzs-title {
            font-size: 16px;
          }
        }

        .dzs-content {
          padding: 15px 20px;
        }
      }
    }
  }

  .other-preview {
    pre {
      background-color: #f8f9fa;
      padding: 12px;
      border-radius: 4px;
      overflow-x: auto;
    }
  }
}
</style>

<script lang="ts">
export default {
  name: 'AssignResourceDialog'
}
</script>
