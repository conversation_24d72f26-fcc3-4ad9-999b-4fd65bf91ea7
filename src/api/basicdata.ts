import axios from "@/utils/axios"
import qs from "qs"

//获取用户列表  
export async function GetUserStuData(data: object) {
  return await axios.post('mgr_basic_data/getUserStuData?' + qs.stringify(data))
}
// 用户类型
export async function GetUserTypeData(data: object) {
  return await axios.post('mgr_basic_data/getUserTypeData?' + qs.stringify(data))
}

// 同步网教学生
export async function sync_wj(data: object) {
  return await axios.post('users/sync_wj?' + qs.stringify(data))
}

// 同步成教学生
export async function sync_cj(data: object) {
  return await axios.post('users/sync_cj?' + qs.stringify(data))
}