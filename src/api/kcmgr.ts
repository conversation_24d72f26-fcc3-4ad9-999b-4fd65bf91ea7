import axios from "@/utils/axios"
import qs from "qs"

// 获取课程节点数据
export async function getKcNodeList(data: any) {
  return await axios.get('kcmgr/getKcNodeList?' + qs.stringify(data))
}

// 保存课程节点数据
export async function saveKcNodeData(data: any) {
  return await axios.post('kcmgr/saveKcNodeData', data)
}

// 更新课程节点状态
export async function updateKcNodeStatus(data: any) {
  return await axios.post('kcmgr/updateKcNodeStatus', data)
}

// 获取课程节点关联的知识点
export async function getKcNodeKnowledgePoints(data: any) {
  return await axios.post('kcmgr/getKcNodeKnowledgePoints?' + data)
}

export async function getCourseJobbank() {
  let res: any = axios.post("kcmgr/getCourseJobbank", {})
  return res
}
export async function getCoursePlate(data:any) {
  let res: any = axios.post("kcmgr/getCoursePlate?"+qs.stringify(data))
  return res
}
export async function getJobbankSourse(data:any) {
  let res: any = axios.post("kcmgr/getJobbankSourse?"+qs.stringify(data))
  return res
}
export async function SaveCourseJobbank(data:any) {
  let res: any = axios.post("kcmgr/SaveCourseJobbank",data)
  return res
}
export async function deleteCourseJobbank(data:any) {
  let res: any = axios.post("kcmgr/deleteCourseJobbank?"+qs.stringify(data))
  return res
}

// 获取视频列表
export async function getVideoList(params: any) {
  let res: any = axios.post("kcmgr/getVideoList", params)
  return res
}

// 插入视频列表
export async function insertVideoList(params: any) {
let res: any = axios.post("kcmgr/insertVideoList", params)
return res
}

export async function getCourseList() {
  let res: any = axios.post("mgr_course/infolist")
  return res

}
  // 获取Jc
export async function getCourseJcList() {
    let res: any = axios.post("mgr_course/infojclist")
    return res
}
export function GetVideoPPTInfoData(data: any) {
  var res: any = axios.post("mgr_ppt/GetVideoPPTInfoData?" + qs.stringify(data))
  return res
}
export async function SaveVideoPPTInfoData(params: any) {
let res: any = axios.post("mgr_ppt/SaveVideoPPTInfoData", params)
return res
}
export async function uploadVideoPPTFile(params: any) {
  let res: any = axios.post("/file/upload_to_drive",params)
  return res
}
export async function SetVideoPPTFileList(params: any) {
let res: any = axios.post("mgr_ppt/SetVideoPPTFileList", params)
return res
}
export function Extract_PPT_content(data: any) {
  var res: any = axios.post("mgr_ppt/Extract_PPT_content?" + qs.stringify(data))
  return res
}

export async function getCourseBook() {
  let res: any = axios.post("kcmgr/getCourseBook", {})
  return res
}
export async function getBook(data:any) {
  let res: any = axios.post("kcmgr/getBook?"+qs.stringify(data))
  return res
}

export function GetVideoPPTFileImgsList(data: any) {
  var res: any = axios.post("mgr_ppt/GetVideoPPTFileImgsList?" + qs.stringify(data))
  return res
}

export async function sync_video_course(data: any) {
  return await axios.post('kcmgr/sync_video_course', data)
}

export async function getCourseChapterResource(data: object) {
  return await axios.post('kcmgr/getCourseChapterResource',data)
}

export async function getCourseChapterVideos(data: object) {
  return await axios.post('kcmgr/getCourseChapterVideos?' + qs.stringify(data))
}

export async function saveCourseChapterResource(params: object) {
  return await axios.post('kcmgr/saveCourseChapterResource' ,params)
}

export async function saveCourseResource(params: object) {
  return await axios.post('kcmgr/saveCourseResource' ,params)
}
export async function getCourseChapterDzs(data: object) {
  return await axios.post('kcmgr/getCourseChapterDzs?' + qs.stringify(data))
}
export async function getCourseChapterDzs_s(data: object) {
  return await axios.post('kcmgr/getCourseChapterDzs_s?' + qs.stringify(data))
}
export async function getCourseChapterJobs(data: object) {
  return await axios.post('kcmgr/getCourseChapterJobs?' + qs.stringify(data))
}
export async function getCourseJc(data: object) {
  return await axios.post('kcmgr/getCourseJc?' + qs.stringify(data))
}
export async function getCourseKsdg(data: object) {
  return await axios.post('kcmgr/getCourseKsdg?' + qs.stringify(data))
}

export async function getCourseJobSource(data: object) {
  return await axios.post('kcmgr/getCourseJobSource?' + qs.stringify(data))
}


export async function getVerificationConfigAll(params: object) {
  return await axios.post('vcation_mgr/get_verification_config_all', params)
}
export async function updateVerificationConfig(params: object) {
  return await axios.post('vcation_mgr/update_verification_config', params)
}