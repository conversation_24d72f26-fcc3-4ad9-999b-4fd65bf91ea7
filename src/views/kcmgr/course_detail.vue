<template>
  <div class="coursedetail" style="padding: 5px;">
    <div>
      <el-button type="primary" @click="initData" icon="RefreshRight"></el-button>
      <el-select v-model="learn_type" class="m-2" placeholder="类型" style="width: 100px;margin-left: 5px;" clearable
        filterable @change="initData">
        <el-option key="微课" label="微课" value="微课">
        </el-option>
        <el-option key="成人高考" label="成人高考" value="成人高考">
        </el-option>
        <el-option key="自考" label="自考" value="自考">
        </el-option>
      </el-select>
      <el-select v-model="course_info_id" class="m-2" placeholder="在线课程" style="width: 190px;margin-left: 5px;"
        clearable filterable @change="initData">
        <el-option v-for="item in courseData" :key="item.id" :label="item.mc" :value="item.id">
          <span style="float: left">{{ item.mc }}</span>
          <span style="
          float: right;
          color: var(--el-text-color-secondary);
          font-size: 13px;">
            版本：{{ item.ver }}
          </span>
        </el-option>
      </el-select>

    </div>
    <el-table v-loading="tableloading" :data="tableData" width="100%" height="calc(100vh - 163px)" ref="multipleTable"
      highlight-current-row size="small" border style="margin-top: 5px;">
      <el-table-column label="序号" v-if="tableData.length > 0" align="center" width="45" header-align="center"
        type="index">
      </el-table-column>
      <el-table-column v-for="column in columns.filter((x: any) => { return x.plate_id == 0 })" :label="column.title"
        :prop="column.prop" align="center" sortable header-align="center">
        <template #default="scope">
            <el-link v-if="column.prop=== 'num_ols_chapter'" @click="openResources(scope.row)" type="primary">{{ scope.row[column.prop] }}</el-link>
            <span v-else> {{ scope.row[column.prop] }} </span>
        </template>
      </el-table-column>
      <el-table-column label="模块">
        <el-table-column v-for="column in columns.filter((x: any) => { return x.plate_id > 0 })" :label="column.title"
          :prop="column.prop" align="center" sortable header-align="center">
          <template #default="scope">
            <el-link v-if="scope.row[column.prop] >= 0" @click="assignResources(scope.row,column.plate_id)" type="primary">{{ scope.row[column.prop] }}</el-link>
            <span v-else> </span>
          </template>
        </el-table-column>
      </el-table-column>
    </el-table>
        <AssignResourceDialog
    v-model="assignResourceDialog.visible"
    :courseInfoId="assignResourceDialog.courseInfoId"
    :courseBaseId="assignResourceDialog.courseBaseId"
    :plateId="assignResourceDialog.plate_id"
    :title="assignResourceDialog.title"
    @save="handleAssignResourceSave"
  />
  </div>
</template>

<script setup lang='ts'>
import { defineComponent, onMounted, reactive, ref, toRefs } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { getOnlineCourse } from '@/api/exam'
import { getCourseDetail } from '@/api/course'
import AssignResourceDialog from '@/components/course/AssignResourceDialog.vue'
import { useRouter } from 'vue-router'

const assignResourceDialog = ref({
  visible: false,
  courseInfoId: 0,
  courseBaseId: 0,
  title: '',
  plate_id:0
})
const courseData = ref<any[]>([])
const course_info_id = ref("")
const tableData = ref<any[]>([])
const tableloading = ref(false)
const columns: any = ref([])
const learn_type: any = ref("自考")
const router = useRouter()
const getOnlineCourseData = () => {
  getOnlineCourse({}).then((res: any) => {
    if (res.code == 200) {
      courseData.value = res.data
      courseData.value.forEach((e: any) => {
        e['mc'] = `【${e.kc_bm}】${e.kc_mc}`
      });
    } else {
      ElMessage.error(res.msg)
    }
  })
}
onMounted(() => {
  getOnlineCourseData()
  initData()
})


const openResources = (row:any) => {
  router.push({
    path: '/onlinecourse/online_chapter',
    query: {
      course_info_id: row.course_info_id
    }
  })
}

const initData = () => {
  tableloading.value = true
  getCourseDetail({}).then((res: any) => {
    console.log(res)
    if (course_info_id.value || learn_type.value) {
      tableData.value = course_info_id.value ? res.data.listdata.filter((x: any) => { return x.couse_info_id == course_info_id.value }) : res.data.listdata
      tableData.value = learn_type.value ? tableData.value.filter((x: any) => { return x.learn_type == learn_type.value }) : tableData.value
    } else {
      tableData.value = res.data.listdata
    }

    columns.value = res.data.columns
  }).finally(() => {
    tableloading.value = false
  })
}
const handleAssignResourceSave = () => {
  assignResourceDialog.value.visible = false
  ElMessage.success('资料分配成功')
}
const assignResources = (row:any,plate_id:any) => {
  assignResourceDialog.value.courseInfoId = row.course_info_id
  assignResourceDialog.value.courseBaseId = row.course_base_id
   assignResourceDialog.value.plate_id = plate_id
  assignResourceDialog.value.title = `指定资料 - ${row.kc_mc}`
  console.log(assignResourceDialog.value)
  assignResourceDialog.value.visible = true
}
</script>

<style></style>
