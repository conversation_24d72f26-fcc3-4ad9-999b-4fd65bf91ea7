<template>
  <div class="quesbank page">
    <div class="header"> 
      <el-button type="success" plain size="default" icon="refresh" round @click="getData"></el-button>
          <el-input v-model="userData.keySearch" placeholder="课程编码|课程名称" size="default" style="width: 260px;margin:0 10px;" clearable @change="getData">
            <template #append>
              <el-button icon="Search" @click="getData"/>
            </template>
          </el-input>
      <el-tag class="el_tag_5">课程类型</el-tag>
      <el-select v-model="userData.learn_type" clearable filterable style="width: 160px;" placeholder="课程类型" @change="getData">
          <el-option label="全部" :value="0"/> 
          <el-option label="成人高考" :value="1" /> 
          <el-option label="自考" :value="2" /> 
          <el-option label="微课" :value="3" /> 
      </el-select>
      
    </div>
    <div class="body">
      <el-table
        :data="userData.dataTable"
        border
        class="modTable"
        :height="userData.tableHeight"
        style="width: 100%;"
        v-loading="userData.loading"
        @selection-change="handleSelectionChange"
      >
        <!-- <el-table-column type="selection" width="50" align="center" />  -->
        <el-table-column prop="" type="index" label="序号"  align="center" width="60" header-align="center" />
        <el-table-column prop="learn_title"  label="课程类型" min-width="100" align="center" header-align="center" show-overflow-tooltip sortable/> 
        <el-table-column prop="kc_bm" label="课程编码" min-width="120" align="center" show-overflow-tooltip sortable/>
        <el-table-column prop="kc_mc" label="课程名称" min-width="120" align="left" header-align="center" sortable/>
        <el-table-column prop="num_total" label="题库总量" min-width="120" align="center" header-align="center" sortable show-overflow-tooltip/>
        <el-table-column prop="num_subjective" label="主观题量" min-width="100" align="center" header-align="center" sortable/>
        <el-table-column prop="num_objective" label="客观题量" min-width="100" align="center" header-align="center" sortable/>
        <el-table-column label="试题来源">
          <el-table-column prop="num_source_1" label="自编" min-width="100" align="center" header-align="center" sortable/>
          <el-table-column prop="num_source_3" label="历年真题" min-width="100" align="center" header-align="center" sortable/>
          <el-table-column prop="num_source_4" label="全真模拟" min-width="100" align="center" header-align="center" sortable/>
        </el-table-column>
      </el-table>
      <div style="margin-top: 16px; text-align: right;">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="userData.total"
          :page-size="userData.pageSize"
          :current-page="userData.currentPage"
          :page-sizes="[20, 50, 100, 500, userData.total]"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </div>


  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { GetQuestionBankPage} from '@/api/question'

const userData = reactive({
  loading: false,
  tableHeight: window.innerHeight - 200,
  keySearch: '',
  learn_type:0,
  dataTable: [] as any,
  total: 0,
  pageSize: 30,
  currentPage: 1
}) 
const handlecurrentRow = ref([]) as any
  
const getData = () => {
  userData.loading = true
  const pars = {
    keySearch: userData.keySearch,
    learn_type: userData.learn_type,
    currentPage: userData.currentPage,
    pageSize: userData.pageSize,
  }
  GetQuestionBankPage(pars).then((msg: any) => {
    userData.dataTable = msg.data || []
    userData.total = msg.total || userData.dataTable.length
    userData.loading = false
  }).catch((err: any) => {
    console.log(err)
    userData.loading = false
  })
}

const handleSelectionChange = (rows: any) => {
  handlecurrentRow.value = rows
}
const handleSizeChange = (size: number) => {
  userData.pageSize = size
  getData()
}

const handlePageChange = (page: number) => {
  userData.currentPage = page
  getData()
}


const calcTableHeight = () => {
  // 这里的220可根据实际页面头部、分页等高度调整
  userData.tableHeight = window.innerHeight - 200;
}

onMounted(() => {
  getData();
  calcTableHeight();
  window.addEventListener('resize', calcTableHeight);
});

onUnmounted(() => {
  window.removeEventListener('resize', calcTableHeight);
});


</script>

<style lang="scss"> 


</style>
