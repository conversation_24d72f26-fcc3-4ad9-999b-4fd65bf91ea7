<template>
  <div class="userManage page">
    <div class="header">
      <el-button type="success" plain size="default" icon="refresh" round @click="getData"></el-button>
      <el-button type="primary" plain size="default" icon="download" round @click="syncData" :loading="syncLoading">同步数据</el-button>
          <el-input v-model="userData.keySearch" placeholder="用户名|姓名|身份证|手机号" size="default" style="width: 260px;margin:0 10px;" clearable @change="getData">
            <template #append>
              <el-button icon="Search" @click="getData"/>
            </template>
          </el-input>
      <el-tag class="el_tag_5">用户类型</el-tag>
      <el-select v-model="userData.user_type_id" clearable filterable style="width: 160px;" placeholder="用户类型" @change="getData">
          <el-option v-for="item in user_type_title" :key="item.id" :label="item.title" :value="item.id" style="width: 260px;">
            <span style="float: left">{{ item.title}}</span>
            <span style="float: right; color: #67C23A; font-size: 13px;">{{ item.num }}</span>
          </el-option>
      </el-select>

    </div>
    <div class="body">
      <el-table
        :data="userData.dataTable"
        border
        class="modTable"
        :height="userData.tableHeight"
        style="width: 100%;"
        v-loading="userData.loading"
        @selection-change="handleSelectionChange"
      >
        <!-- <el-table-column type="selection" width="50" align="center" />  -->
        <el-table-column prop="user_type_title" label="用户类型" min-width="100" align="center" header-align="center" show-overflow-tooltip/>
        <el-table-column prop="xs_bm" label="学号" min-width="120" align="center" show-overflow-tooltip/>
        <el-table-column prop="xs_bmh" label="报名号" min-width="120" align="left" header-align="center"/>
        <el-table-column prop="xs_xm" label="姓名" min-width="120" align="center" header-align="center" show-overflow-tooltip/>
        <el-table-column prop="xb_mc" label="性别" min-width="100" align="center" header-align="center"/>
        <el-table-column prop="yddh" label="手机号" min-width="100" align="center" header-align="center"/>
        <el-table-column prop="xs_sfzh" label="身份证号" min-width="140" align="center" header-align="center"/>
        <el-table-column prop="xjzt_mc" label="学籍状态" min-width="100" align="center" header-align="center"/>
        <el-table-column prop="pc_bm" label="批次" min-width="100" align="center" header-align="center"/>
        <el-table-column prop="zy_bm" label="专业编码" min-width="100" align="center" header-align="center"/>
        <el-table-column prop="zy_mc" label="专业名称" min-width="100" align="center" header-align="center"/>
        <el-table-column prop="xlcc_mc" label="层次" min-width="100" align="center" header-align="center"/>
        <el-table-column prop="fee_flag_s" label="交费情况" min-width="100" align="left" header-align="center" show-overflow-tooltip/>
        <!-- <el-table-column prop="url_photo" label="照片" min-width="100" align="left" header-align="center" show-overflow-tooltip>
          <template #default="scope">
            <el-link :href="scope.row.url_photo" type="primary" style="font-size: 12px;" target="_blank">{{scope.row.url_photo}}</el-link>
          </template>
        </el-table-column> -->
        <el-table-column prop="zd_bm" label="站点编码" min-width="80" align="center" header-align="center"/>
        <el-table-column prop="zd_mc" label="站点名称" min-width="140" align="left" header-align="center" show-overflow-tooltip/>
        
      </el-table>
      <div style="margin-top: 16px; text-align: right;">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="userData.total"
          :page-size="userData.pageSize"
          :current-page="userData.currentPage"
          :page-sizes="[20, 50, 100, 500, userData.total]"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </div>


  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { GetUserStuData,GetUserTypeData,sync_cj,sync_wj} from '@/api/basicdata'

const userData = reactive({
  loading: false,
  tableHeight: window.innerHeight - 220,
  keySearch: '',
  user_type_id:2,
  dataTable: [] as any,
  total: 0,
  pageSize: 30,
  currentPage: 1
})
const user_type_title = ref([]) as any
const handlecurrentRow = ref([]) as any
const syncLoading = ref(false)
 

const getTypeData = () => {
  userData.loading = true
  GetUserTypeData({}).then((res:any) => { 
    user_type_title.value = res.data
    userData.user_type_id =res.data.length>0? res.data[0].id : ''
    getData()
  }).catch((err:any) => {
        ElMessage({
          type: 'error',
          message: '出错了!' + err
        })
    })
}

const getData = () => {
  userData.loading = true
  const pars = {
    keySearch: userData.keySearch,
    user_type_id: userData.user_type_id,
    currentPage: userData.currentPage,
    pageSize: userData.pageSize,
  }
  GetUserStuData(pars).then((msg: any) => {
    userData.dataTable = msg.data || []
    userData.total = msg.total || userData.dataTable.length
    userData.loading = false
  }).catch((err: any) => {
    console.log(err)
    userData.loading = false
  })
}

const handleSelectionChange = (rows: any) => {
  handlecurrentRow.value = rows
}
const handleSizeChange = (size: number) => {
  userData.pageSize = size
  getData()
}

const handlePageChange = (page: number) => {
  userData.currentPage = page
  getData()
}

// 同步数据方法
const syncData = async () => {
  if (!userData.user_type_id) {
    ElMessage.warning('请先选择用户类型')
    return
  }

  // 根据用户类型确定同步方法和提示信息
  let syncMethod
  let userTypeName = ''

  // 查找当前选中的用户类型信息
  const currentUserType = user_type_title.value.find((item: any) => item.id === userData.user_type_id)
  if (currentUserType) {
    userTypeName = currentUserType.title
  }

  // 根据用户类型ID选择对应的同步方法
  if (userData.user_type_id === 5) {
    // 成人高考学生 - 使用 sync_cj
    syncMethod = sync_cj
  } else if (userData.user_type_id === 2) {
    // 西财自考学生 - 使用 sync_wj
    syncMethod = sync_wj
  } else {
    ElMessage.warning('当前用户类型不支持同步数据功能')
    return
  }

  try {
    // 确认对话框
    await ElMessageBox.confirm(
      `确定要同步 "${userTypeName}" 的数据吗？此操作可能需要一些时间。`,
      '确认同步',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    syncLoading.value = true

    // 调用对应的同步方法
    const response: any = await syncMethod({})

    if (response.code === 200) {
      ElMessage.success(`${userTypeName} 数据同步成功！`)
      // 同步成功后刷新数据
      getData()
      // 刷新用户类型统计数据
      getTypeData()
    } else {
      ElMessage.error(`同步失败：${response.message || '未知错误'}`)
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('同步数据失败:', error)
      ElMessage.error(`同步失败：${error.message || '网络错误'}`)
    }
  } finally {
    syncLoading.value = false
  }
}

onMounted(() => {
  getTypeData()
})


</script>

<style lang="scss"> 


</style>
