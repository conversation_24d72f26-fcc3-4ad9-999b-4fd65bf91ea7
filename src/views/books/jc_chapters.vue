<template>
 <div class="Jc_chapter page" >
    <div class="header"> 
        <el-button type="success" plain size="default" icon="refresh" round @click="getJcData"></el-button>
        <el-tag class="el_tag_5">教材课程</el-tag>
        <el-select v-model="tableJcData.course_jc_id"
                  placeholder="请选择教材课程"
                  clearable
                  filterable
                  style="width: 260px;"
                  @change="getJcData">
            <el-option
              v-for="item in courseJcList"
              :key="item.id"
              :label="'[ '+item.course_code+' ] ' + item.course_name"
              :value="item.id">
              <span style="float: left">{{ '[ '+item.course_code+' ] '+ item.course_name }}</span>
              <span style="float: right; color: #67C23A; font-size: 13px;">{{ item.ver }}</span>
            </el-option>
        </el-select>
        <el-button type="primary" style="margin-left: 10px;" 
          :disabled="tableJcData.tableDataList.length>0" plain @click="SynJcChapter()" 
          size="default" round icon="Pointer" aria-label="">
          “知识点层级”同步生成“教材”章节
        </el-button>
    </div>
    <div class="body"> 
    <el-table
        border
        :height="tableJcData.tableHeight"
        :data="tableJcData.tableDataList"
        row-key="id"
        class="moduleTable"
        v-loading="tableJcData.loading" 
        :default-expand-all="false"
        highlight-current-row
        ref="tablexjcTree"
      >
        <el-table-column prop="" type="index" label="序号"  align="center" width="40" header-align="center" />
        <el-table-column prop="chapter_code" label="章编码" align="center" width="60" />
        <el-table-column prop="chapter_name" label="章名称" align="left" header-align="center" min-width="160" show-overflow-tooltip/>
        <el-table-column prop="node_code" label="节编码" align="center" width="60" />
        <el-table-column prop="node_name" label="节名称" align="left" header-align="center" min-width="160" show-overflow-tooltip/>
        <el-table-column prop="course_code" label="课程编码" width="80" align="center" header-align="center"/>
        <el-table-column prop="course_name" label="课程名称" width="140" align="left" header-align="center" show-overflow-tooltip/>
      </el-table>
    </div>
 </div>
</template>

<script lang="ts" setup>
    import { defineComponent, onMounted, reactive, ref, toRefs } from "vue";
    import { getSelectCourseJcList ,SynToJcChapter,getHierarchyJcData } from '@/api/dzs';
    import { ElMessage,ElMessageBox } from "element-plus";
    import { msgShow } from '@/utils/message'; 

    const tableJcData = reactive({
      tableDataList:[],
      tableHeight:window.innerHeight - 155,
      loading:false,
      course_jc_id:'' as any
    });

    const courseJcList = ref([]) as any
    // 获取教材课程列表
    const getCourseJcListData = async () => {
      try {
        const res = await getSelectCourseJcList()
        if (res.code === 200) {
          courseJcList.value = res.data
          tableJcData.course_jc_id=45
          getJcData()
        } else {
          msgShow(res.msg || '获取课程列表失败', 'warning')
        }
      } catch (error) {
        msgShow('获取课程列表失败', 'error')
      }
    }

    const getJcData=() => {
      tableJcData.loading=true
      var par = {
        course_jc_id: tableJcData.course_jc_id
      };
      getHierarchyJcData(par).then((res:any) => {
        tableJcData.tableDataList = res.data??[]
        tableJcData.loading=false
      })
    }

    const SynJcChapter=() => {
      if(tableJcData.course_jc_id==='' || tableJcData.course_jc_id===null || tableJcData.course_jc_id === undefined){
        ElMessage({
              message: '请先选择教材课程',
              type: 'error',
            })
            return
      }
      if (tableJcData.tableDataList.length>0) {
        ElMessage({
              message: '所选教材课程已有章节，不能重复同步！！！',
              type: 'error',
            })
        return
      }
      ElMessageBox.confirm(
          '此操作将批量提取“知识点层级”信息同步为“教材”章节, 是否继续?',
          '提示',
          {
            confirmButtonText: '确认同步',
            cancelButtonText: '取消',
            type: 'warning',
          }
        )
        .then(() => {  
          const pars = { 
            course_jc_id : tableJcData.course_jc_id
          }
          SynToJcChapter(pars).then((msg:any) => {
            if (msg.data > 0) {
              ElMessage({
                type: 'success',
                message: '同步成功!' + msg.data + '条'
              })
              getJcData()
            } else {
              ElMessage({
                type: 'info',
                message: '同步失败!' + msg.msg
              })
            }
          }).catch((err:any) => {
            ElMessage({
              type: 'info',
              message: '同步失败!' + err
            })
          })
        })
        .catch(() => {
          ElMessage({
            type: 'info',
            message: '取消同步!',
          })
        })

    }


const getJcCourseCode = (courseId: number | null,type:any) => {
  if (!courseId) return '-'
  const course = courseJcList.value.find((c:any) => Number(c.id) === Number(courseId))
  return course?.[type] || '-'
}


  onMounted(() => {
      getCourseJcListData()
  });
  





</script>

<style lang="scss">
 

</style>
