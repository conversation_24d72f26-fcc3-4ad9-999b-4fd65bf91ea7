import{G as e,e as l,S as a}from"./question-DWIQ7W1h.js";import{t}from"./tools-CvIkJR06.js";import{d as o,r as i,o as n,S as u,a as s,L as d,c as r,b as p,j as m,w as v,e as _,t as c,p as w,m as h,h as y,F as f,f as g,M as V,E as x,v as b,_ as k}from"./index-BERmxY3Y.js";const U={style:{"margin-bottom":"16px",display:"flex","justify-content":"space-between","align-items":"center"}},C={style:{height:"700px","overflow-y":"auto"}},A={style:{float:"left"}},Q={style:{float:"right",color:"#67C23A","font-size":"13px"}},R={style:{width:"740px",height:"410px"}},j={style:{width:"740px",height:"310px"}},z={key:0,style:{width:"740px",height:"310px"}},S={key:0,class:"options_div"},B={key:0,style:{height:"210px"}},N={style:{width:"100%"}},q={style:{display:"flex","justify-content":"space-between","align-items":"center",float:"left",margin:"10px"}},M={style:{display:"flex","justify-content":"space-between","align-items":"center",float:"left",margin:"10px"}},D={key:1,class:"options_div"},E={style:{width:"800px",height:"310px",margin:"10px"}},$={key:0,style:{width:"800px"}},I={key:0,style:{height:"210px"}},P={style:{width:"100%",margin:"10px"}},T={style:{width:"100%",margin:"10px"}},J={key:1},F={style:{width:"100%",margin:"10px"}},G={style:{height:"310px"}},L={style:{width:"100%",margin:"10px"}},O={style:{width:"100%",margin:"10px"}},H={style:{width:"100%",margin:"10px"}},K={style:{"margin-top":"16px","text-align":"right"}},W=k(o({__name:"up_question_job",props:{jobbankid:{}},setup(o){const k=o,W=i(k.jobbankid),X=i([]),Y=i([]),Z=i(!1),ee=i(!1);function le(){if(!X.value||!X.value.title)return;Array.isArray(X.value.title.options)||(X.value.title.options=[]);const e=X.value.title.options.length;let l="";if(e<26)l=String.fromCharCode(65+e);else{l=String.fromCharCode(65+Math.floor(e/26)-1)+String.fromCharCode(65+e%26)}X.value.title.options.push({listNo:l,answer:!1,option:""})}function ae(e,l){if(!X.value||!X.value.title||!Array.isArray(X.value.title.options))return;const a=X.value.title.options.map(((e,l)=>e.answer?l:-1)).filter((e=>-1!==e)).map((e=>String.fromCharCode(65+e))).join(",");X.value.title.answer=a,X.value.answer=a}function te(){X.value&&X.value.title&&(Array.isArray(X.value.title.subQuestions)||(X.value.title.subQuestions=[]),X.value.title.subQuestions.push({title:"",options:[{listNo:"A",answer:!1,option:""},{listNo:"B",answer:!1,option:""}],answer:"",answer_parsing:""}))}function oe(e,l,a){if(!X.value||!X.value.title||!Array.isArray(X.value.title.subQuestions))return;const t=X.value.title.subQuestions[e];if(!t||!Array.isArray(t.options))return;const o=t.options.map(((e,l)=>e.answer?l:-1)).filter((e=>-1!==e)).map((e=>String.fromCharCode(65+e))).join(",");t.answer=o,X.value.title.subQuestions[e].answer=o,ie()}function ie(){if(!X.value||!X.value.title||!Array.isArray(X.value.title.subQuestions))return;const e=X.value.title.subQuestions.filter((e=>e&&Array.isArray(e.options))).map(((e,l)=>({sn:l+1,answer:e.options.map(((e,l)=>e.answer?l:-1)).filter((e=>-1!==e)).map((e=>String.fromCharCode(65+e))).join(",")})));X.value.answer=JSON.stringify(e,null,0)}const ne=async()=>{Z.value=!0;try{const e={jobbankid:W.value};l(e).then((e=>{var l,a;X.value=e.data,(null==(a=null==(l=X.value)?void 0:l.title)?void 0:a.title)&&(X.value.title.title=t.format2Md(X.value.title.title)),(()=>{if(X.value&&X.value.title&&Array.isArray(X.value.title.options)){const e=X.value.title.answer;if(e){const l=e.split(",").map((e=>e.trim())).filter(Boolean);X.value.title.options.forEach(((e,a)=>{const t=String.fromCharCode(65+a);e.answer=l.includes(t)}))}}X.value&&X.value.title&&Array.isArray(X.value.title.subQuestions)&&X.value.title.subQuestions.forEach((e=>{if(Array.isArray(e.options)&&"string"==typeof e.answer){const l=e.answer.split(",").map((e=>e.trim())).filter(Boolean);e.options.forEach(((e,a)=>{const t=String.fromCharCode(65+a);e.answer=l.includes(t)}))}}))})(),Z.value=!1})).catch((e=>{Z.value=!1}))}catch(e){x.error("题库获取失败")}finally{Z.value=!1}},ue=async()=>{ee.value=!0;try{b.confirm("再次确认是否保存题库编号【"+W.value+"】的数据, 是否继续保存?","提示",{confirmButtonText:"确认保存",cancelButtonText:"取消",type:"warning"}).then((()=>{const e={data:X.value};a(e).then((e=>{e.data>0?(x.success("保存成功!"),ne()):x({type:"info",message:"保存失败!"+e.msg}),ee.value=!1})).catch((e=>{ee.value=!1}))})).catch((()=>{x({type:"info",message:"取消保存!"})}))}catch(e){x.error("保存失败")}finally{ee.value=!1}};return n((()=>{e({}).then((e=>{200==e.code?Y.value=e.data:x.error(e.msg)})),ne()})),u((()=>k.jobbankid),(e=>{W.value=e,ne()})),(e,l)=>{const a=s("el-tag"),t=s("el-input"),o=s("el-radio"),i=s("el-radio-group"),n=s("el-form-item"),u=s("el-option"),x=s("el-select"),b=s("el-input-number"),k=s("v-md-editor"),ne=s("el-form"),se=s("el-button"),de=s("el-table-column"),re=s("el-checkbox"),pe=s("el-table"),me=s("el-collapse-item"),ve=s("el-collapse"),_e=s("el-card"),ce=d("loading");return p(),r("div",null,[m(_e,null,{default:v((()=>[_("div",U,[_("div",null,[_("span",null,"题库维护（ Job ID: "+c(W.value)+" ） 课程："+c(X.value.kc_bm)+" "+c(X.value.kc_mc),1)]),_("div",null,[m(a,{class:"el_tag_5"},{default:v((()=>l[19]||(l[19]=[w("题型")]))),_:1,__:[19]}),m(t,{modelValue:X.value.questiontype,"onUpdate:modelValue":l[0]||(l[0]=e=>X.value.questiontype=e),placeholder:"题型",style:{width:"200px",margin:"0 10px"}},null,8,["modelValue"])])]),_("div",C,[m(ne,{model:X.value,inline:!0,"label-width":"100px"},{default:v((()=>[m(n,{label:"类型"},{default:v((()=>[m(i,{modelValue:X.value.is_autoscore,"onUpdate:modelValue":l[1]||(l[1]=e=>X.value.is_autoscore=e),style:{width:"200px"}},{default:v((()=>[m(o,{label:1},{default:v((()=>l[20]||(l[20]=[w("客观题")]))),_:1,__:[20]}),m(o,{label:0},{default:v((()=>l[21]||(l[21]=[w("主观题")]))),_:1,__:[21]})])),_:1},8,["modelValue"])])),_:1}),m(n,{label:"状态"},{default:v((()=>[m(i,{modelValue:X.value.status,"onUpdate:modelValue":l[2]||(l[2]=e=>X.value.status=e),style:{width:"200px"}},{default:v((()=>[m(o,{label:1},{default:v((()=>l[22]||(l[22]=[w("启用")]))),_:1,__:[22]}),m(o,{label:0},{default:v((()=>l[23]||(l[23]=[w("禁用")]))),_:1,__:[23]})])),_:1},8,["modelValue"])])),_:1}),m(n,{label:"markdown"},{default:v((()=>[m(i,{modelValue:X.value.in_markdown_section,"onUpdate:modelValue":l[3]||(l[3]=e=>X.value.in_markdown_section=e),style:{width:"200px"}},{default:v((()=>[m(o,{label:1},{default:v((()=>l[24]||(l[24]=[w("是")]))),_:1,__:[24]}),m(o,{label:0},{default:v((()=>l[25]||(l[25]=[w("否")]))),_:1,__:[25]})])),_:1},8,["modelValue"])])),_:1}),m(n,{label:"试题来源"},{default:v((()=>[m(x,{modelValue:X.value.jobbank_source_id,"onUpdate:modelValue":l[4]||(l[4]=e=>X.value.jobbank_source_id=e),clearable:"",filterable:"",style:{width:"200px"},placeholder:"试题来源"},{default:v((()=>[(p(!0),r(f,null,g(Y.value,(e=>(p(),y(u,{key:e.id,label:e.title,value:e.id,style:{width:"260px"}},{default:v((()=>[_("span",A,c(e.id),1),_("span",Q,c(e.title),1)])),_:2},1032,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),m(n,{label:"试题能力层次"},{default:v((()=>[m(x,{modelValue:X.value.competence_level,"onUpdate:modelValue":l[5]||(l[5]=e=>X.value.competence_level=e),clearable:"",filterable:"",style:{width:"200px"},placeholder:"试题能力层次"},{default:v((()=>[m(u,{label:"识记",value:"识记",style:{width:"200px"}}),m(u,{label:"领会",value:"领会",style:{width:"200px"}}),m(u,{label:"应用",value:"应用",style:{width:"200px"}})])),_:1},8,["modelValue"])])),_:1}),m(n,{label:"难度能力层次"},{default:v((()=>[m(x,{modelValue:X.value.difficulty_level,"onUpdate:modelValue":l[6]||(l[6]=e=>X.value.difficulty_level=e),clearable:"",filterable:"",style:{width:"200px"},placeholder:"难度能力层次"},{default:v((()=>[m(u,{label:"易",value:"易",style:{width:"200px"}}),m(u,{label:"较易",value:"较易",style:{width:"200px"}}),m(u,{label:"中等",value:"中等",style:{width:"200px"}}),m(u,{label:"较难",value:"较难",style:{width:"200px"}}),m(u,{label:"难",value:"难",style:{width:"200px"}})])),_:1},8,["modelValue"])])),_:1}),m(n,{label:"章节号"},{default:v((()=>[m(b,{modelValue:X.value.chapter_no,"onUpdate:modelValue":l[7]||(l[7]=e=>X.value.chapter_no=e),min:0,placeholder:"章节号",style:{width:"200px"}},null,8,["modelValue"])])),_:1}),m(n,{label:"章节名称"},{default:v((()=>[m(t,{modelValue:X.value.chapter_mc,"onUpdate:modelValue":l[8]||(l[8]=e=>X.value.chapter_mc=e),placeholder:"章节名称",style:{width:"520px"}},null,8,["modelValue"])])),_:1}),m(n,{label:"主题干"},{default:v((()=>[_("div",R,[m(k,{modelValue:X.value.title.title,"onUpdate:modelValue":l[9]||(l[9]=e=>X.value.title.title=e),height:"400px"},null,8,["modelValue"])])])),_:1}),"subQuestions"in X.value.title?h("",!0):(p(),y(n,{key:0,label:"主题答案"},{default:v((()=>[_("div",j,[m(k,{modelValue:X.value.answer,"onUpdate:modelValue":l[10]||(l[10]=e=>X.value.answer=e),height:"300px"},null,8,["modelValue"])])])),_:1})),m(n,{label:"主题答案解析"},{default:v((()=>[X.value.in_markdown_section?(p(),r("div",z,[m(k,{modelValue:X.value.answer_parsing,"onUpdate:modelValue":l[11]||(l[11]=e=>X.value.answer_parsing=e),height:"300px"},null,8,["modelValue"])])):(p(),y(t,{key:1,modelValue:X.value.answer_parsing,"onUpdate:modelValue":l[12]||(l[12]=e=>X.value.answer_parsing=e),type:"textarea",autosize:{minRows:4,maxRows:6},style:{width:"360px"}},null,8,["modelValue"]))])),_:1}),m(n,{label:"知识点"},{default:v((()=>[m(t,{modelValue:X.value.knowledge_point,"onUpdate:modelValue":l[13]||(l[13]=e=>X.value.knowledge_point=e),type:"textarea",autosize:{minRows:4,maxRows:6},style:{width:"360px"}},null,8,["modelValue"])])),_:1})])),_:1},8,["model"]),"options"in X.value.title?(p(),r("div",S,[m(se,{type:"primary",plain:"",size:"default",round:"",icon:"CirclePlus",onClick:le},{default:v((()=>l[26]||(l[26]=[w(" 新增选项")]))),_:1,__:[26]}),V((p(),y(pe,{ref:"questionTable",data:X.value.title.options,border:"",style:{width:"100%",margin:"10px"}},{default:v((()=>[m(de,{prop:"listNo",label:"选项名",align:"center","header-align":"center",width:"60"},{default:v((e=>[m(t,{modelValue:e.row.listNo,"onUpdate:modelValue":l=>e.row.listNo=l,placeholder:"请输入选项名"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),m(de,{prop:"answer",label:"是否为答案",align:"center","header-align":"center",width:"80"},{default:v((e=>[m(re,{modelValue:e.row.answer,"onUpdate:modelValue":l=>e.row.answer=l,onChange:l=>ae(e.$index,e.row)},null,8,["modelValue","onUpdate:modelValue","onChange"])])),_:1}),m(de,{prop:"option",label:"选项内容","min-width":"250"},{default:v((e=>[X.value.in_markdown_section?(p(),r("div",B,[m(k,{modelValue:e.row.option,"onUpdate:modelValue":l=>e.row.option=l,height:"200px"},null,8,["modelValue","onUpdate:modelValue"])])):(p(),y(t,{key:1,modelValue:e.row.option,"onUpdate:modelValue":l=>e.row.option=l,placeholder:"选项内容"},null,8,["modelValue","onUpdate:modelValue"]))])),_:1}),m(de,{label:"操作",align:"center","header-align":"center",width:"100"},{default:v((e=>[m(se,{type:"danger",size:"small",onClick:l=>{return a=e.$index,void(X.value&&X.value.title&&Array.isArray(X.value.title.options)&&(X.value.title.options.splice(a,1),ae()));var a}},{default:v((()=>l[27]||(l[27]=[w("删除")]))),_:2,__:[27]},1032,["onClick"])])),_:1})])),_:1},8,["data"])),[[ce,Z.value]]),_("div",N,[_("div",q,[m(a,{class:"el_tag_5"},{default:v((()=>l[28]||(l[28]=[w("选项答案(A,B)")]))),_:1,__:[28]}),"options"in X.value.title&&X.value.title.options.length>0?(p(),y(t,{key:0,modelValue:X.value.title.answer,"onUpdate:modelValue":l[14]||(l[14]=e=>X.value.title.answer=e),placeholder:"选项答案",type:"textarea",autosize:{minRows:2,maxRows:4},style:{width:"360px",margin:"0 10px"}},null,8,["modelValue"])):h("",!0)]),_("div",M,[m(a,{class:"el_tag_5"},{default:v((()=>l[29]||(l[29]=[w("选项答案解析")]))),_:1,__:[29]}),X.value.title.options.length>0?(p(),y(t,{key:0,modelValue:X.value.title.answer_parsing,"onUpdate:modelValue":l[15]||(l[15]=e=>X.value.title.answer_parsing=e),placeholder:"选项答案解析",type:"textarea",autosize:{minRows:2,maxRows:4},style:{width:"360px",margin:"0 10px"}},null,8,["modelValue"])):h("",!0)])])])):h("",!0),"subQuestions"in X.value.title?(p(),r("div",D,[m(se,{type:"primary",plain:"",size:"default",round:"",icon:"CirclePlus",onClick:te},{default:v((()=>l[30]||(l[30]=[w(" 新增子题")]))),_:1,__:[30]}),m(ve,{modelValue:X.value.title.subQuestions,"onUpdate:modelValue":l[16]||(l[16]=e=>X.value.title.subQuestions=e),style:{margin:"10px"}},{default:v((()=>[(p(!0),r(f,null,g(X.value.title.subQuestions,((e,o)=>(p(),y(me,{title:"第"+(o+1)+"题",key:o,name:o+1},{default:v((()=>[m(se,{type:"danger",plain:"",size:"default",round:"",icon:"Delete",onClick:e=>{return l=o,void(X.value&&X.value.title&&Array.isArray(X.value.title.subQuestions)&&(X.value.title.subQuestions.splice(l,1),ie()));var l}},{default:v((()=>l[31]||(l[31]=[w(" 删除本子题")]))),_:2,__:[31]},1032,["onClick"]),_("div",E,[m(k,{modelValue:e.title,"onUpdate:modelValue":l=>e.title=l,height:"300px"},null,8,["modelValue","onUpdate:modelValue"])]),"options"in e?(p(),r("div",$,[m(se,{type:"primary",plain:"",size:"default",round:"",icon:"CirclePlus",onClick:e=>function(e){if(!X.value||!X.value.title||!Array.isArray(X.value.title.subQuestions))return;const l=X.value.title.subQuestions[e];if(!l)return;Array.isArray(l.options)||(l.options=[]);const a=l.options.length;let t="";t=a<26?String.fromCharCode(65+a):String.fromCharCode(65+Math.floor(a/26)-1)+String.fromCharCode(65+a%26);l.options.push({listNo:t,answer:!1,option:""})}(o)},{default:v((()=>l[32]||(l[32]=[w(" 新增选项")]))),_:2,__:[32]},1032,["onClick"]),V((p(),y(pe,{data:e.options,border:"",style:{width:"100%",margin:"10px"}},{default:v((()=>[m(de,{prop:"listNo",label:"选项名",align:"center","header-align":"center",width:"80"},{default:v((e=>[m(t,{modelValue:e.row.listNo,"onUpdate:modelValue":l=>e.row.listNo=l,placeholder:"请输入选项名"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),m(de,{prop:"answer",label:"是否为答案",align:"center","header-align":"center",width:"80"},{default:v((e=>[m(re,{modelValue:e.row.answer,"onUpdate:modelValue":l=>e.row.answer=l,onChange:l=>oe(o,e.$index,e.row)},null,8,["modelValue","onUpdate:modelValue","onChange"])])),_:2},1024),m(de,{prop:"option",label:"选项内容",align:"center","header-align":"center","min-width":"250"},{default:v((e=>[X.value.in_markdown_section?(p(),r("div",I,[m(k,{modelValue:e.row.option,"onUpdate:modelValue":l=>e.row.option=l,height:"200px"},null,8,["modelValue","onUpdate:modelValue"])])):(p(),y(t,{key:1,modelValue:e.row.option,"onUpdate:modelValue":l=>e.row.option=l,placeholder:"选项内容"},null,8,["modelValue","onUpdate:modelValue"]))])),_:1}),m(de,{label:"操作",align:"center","header-align":"center",width:"100"},{default:v((e=>[m(se,{type:"danger",size:"small",onClick:l=>function(e,l){if(!X.value||!X.value.title||!Array.isArray(X.value.title.subQuestions))return;const a=X.value.title.subQuestions[e];a&&Array.isArray(a.options)&&(a.options.splice(l,1),oe(e))}(o,e.$index)},{default:v((()=>l[33]||(l[33]=[w("删除")]))),_:2,__:[33]},1032,["onClick"])])),_:2},1024)])),_:2},1032,["data"])),[[ce,Z.value]]),_("div",P,[m(a,{class:"el_tag_5"},{default:v((()=>l[34]||(l[34]=[w("选项答案(A,B,C,D)")]))),_:1,__:[34]}),e.options.length>0?(p(),y(t,{key:0,modelValue:e.answer,"onUpdate:modelValue":l=>e.answer=l,placeholder:"选项答案",type:"textarea",autosize:{minRows:2,maxRows:4},style:{width:"360px",margin:"0 10px"}},null,8,["modelValue","onUpdate:modelValue"])):h("",!0)]),_("div",T,[m(a,{class:"el_tag_5"},{default:v((()=>l[35]||(l[35]=[w("选项答案解析")]))),_:1,__:[35]}),e.options.length>0?(p(),y(t,{key:0,modelValue:e.answer_parsing,"onUpdate:modelValue":l=>e.answer_parsing=l,placeholder:"选项答案解析",type:"textarea",autosize:{minRows:2,maxRows:4},style:{width:"360px",margin:"0 10px"}},null,8,["modelValue","onUpdate:modelValue"])):h("",!0)])])):(p(),r("div",J,[_("div",F,[m(a,{class:"el_tag_5"},{default:v((()=>l[36]||(l[36]=[w("答案")]))),_:1,__:[36]}),_("div",G,[m(k,{modelValue:e.answer,"onUpdate:modelValue":l=>e.answer=l,height:"300px"},null,8,["modelValue","onUpdate:modelValue"])])]),_("div",L,[m(a,{class:"el_tag_5"},{default:v((()=>l[37]||(l[37]=[w("答案解析")]))),_:1,__:[37]}),m(t,{modelValue:e.answer_parsing,"onUpdate:modelValue":l=>e.answer_parsing=l,placeholder:"答案解析",type:"textarea",autosize:{minRows:2,maxRows:4},style:{width:"360px",margin:"0 10px"}},null,8,["modelValue","onUpdate:modelValue"])])]))])),_:2},1032,["title","name"])))),128))])),_:1},8,["modelValue"]),_("div",O,[m(a,{class:"el_tag_5",style:{margin:"10px"}},{default:v((()=>l[38]||(l[38]=[w(' 组合题选项答案([{"sn":1,"answer":"B,D"},{"sn":2,"answer":"B"},{"sn":3,"answer":"C"},{"sn":4,"answer":"B"},{"sn":5,"answer":"B"}]) ')]))),_:1,__:[38]}),l[39]||(l[39]=_("br",null,null,-1)),X.value.title.subQuestions.length>0?(p(),y(t,{key:0,modelValue:X.value.answer,"onUpdate:modelValue":l[17]||(l[17]=e=>X.value.answer=e),disabled:"",placeholder:"选项答案解析",type:"textarea",autosize:{minRows:2,maxRows:4},style:{width:"500px",margin:"0 10px"}},null,8,["modelValue"])):h("",!0)]),_("div",H,[m(a,{class:"el_tag_5",style:{margin:"10px"}},{default:v((()=>l[40]||(l[40]=[w(" 组合题选项答案解析 ")]))),_:1,__:[40]}),l[41]||(l[41]=_("br",null,null,-1)),X.value.title.subQuestions.length>0?(p(),y(t,{key:0,modelValue:X.value.answer_parsing,"onUpdate:modelValue":l[18]||(l[18]=e=>X.value.answer_parsing=e),placeholder:"选项答案解析",type:"textarea",autosize:{minRows:2,maxRows:4},style:{width:"500px",margin:"0 10px"}},null,8,["modelValue"])):h("",!0)])])):h("",!0)]),_("div",K,[m(se,{type:"primary",onClick:ue,loading:ee.value},{default:v((()=>l[42]||(l[42]=[w("保存题库")]))),_:1,__:[42]},8,["loading"])])])),_:1})])}}}),[["__scopeId","data-v-db1f4a06"]]);export{W as default};
