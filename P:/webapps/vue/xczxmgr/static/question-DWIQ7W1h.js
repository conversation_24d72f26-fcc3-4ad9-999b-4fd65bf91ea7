import{I as t,J as n}from"./index-BERmxY3Y.js";async function o(n){return await t.post("mgr_question/getQuestionBankPage",n)}async function s(o){return await t.post("mgr_question/getQuestionsJobPage?"+n.stringify(o))}function e(o){return t.post("mgr_question/getBaseKcData?"+n.stringify(o))}function r(o){return t.post("mgr_question/GetJobVerData?"+n.stringify(o))}function u(n){return t.post("mgr_question/getJobIdQuestions",n)}function i(o){return t.post("mgr_question/getJobSource?"+n.stringify(o))}async function a(n){return await t.post("mgr_question/SaveJobbankSourceData",n)}function g(n){return t.post("mgr_question/getTempJobbank")}function c(n){return t.post("mgr_question/SaveTempJobbank",n)}function f(n){return t.post("mgr_question/saveJobBankIdQuestions",n)}function m(n){return t.post("mgr_question/getTempJobbankCount",n)}function p(n){return t.post("mgr_question/getJobQuestionsPreview",n)}function b(n){return t.post("mgr_question/setExtract_word_content",n)}export{i as G,f as S,o as a,e as b,r as c,s as d,u as e,a as f,p as g,g as h,c as i,m as j,b as s};
