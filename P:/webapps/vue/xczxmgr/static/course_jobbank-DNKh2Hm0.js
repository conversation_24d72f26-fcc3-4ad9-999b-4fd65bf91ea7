import{m as e,a as l,n as a,S as t,d as i}from"./kcmgr-_69jTmcm.js";import{C as o}from"./course-DUm3UmHR.js";import{d as n,r as u,o as d,a as s,c as r,b as c,e as p,j as _,w as v,p as m,t as f,h,m as g,F as b,f as y,E as x,v as w}from"./index-BERmxY3Y.js";const k={style:{padding:"5px"}},q={style:{"text-align":"right",width:"100%"}},C={style:{height:"auto",display:"flex","justify-content":"center",width:"100%"}},V={style:{padding:"10px 10px"}},$={style:{width:"550px",border:"1px solid #fff","border-radius":"4px"}},E={style:{padding:"10px 0px",height:"300px","overflow-y":"auto"}},z={class:"dialog-footer"},j=n({__name:"course_jobbank",setup(n){const j=u([]);u({});const U=u(),F=u(window.innerHeight-200),H=u(!1),N=u([]),S=u([]),B=u([]),J=u(0),O={multiple:!0},T=u({id:0,course_info_id:"",plate_id:"",question_source:[]});d((()=>{D()}));const D=()=>{e().then((e=>{j.value=e.data}))},L=()=>{o().then((e=>{var l=[];e.data.forEach((e=>{j.value&&j.value.findIndex((l=>l.course_info_id==e.id));var a={id:e.id,title:`【${e.kc_bm}】${e.kc_mc} ：${e.ver}`,disabled:!1};l.push(a)})),N.value=l}))},M=()=>{l({id:T.value.course_info_id,type:3}).then((e=>{S.value=e.data,S.value.forEach((e=>{j.value.filter((l=>l.course_info_id==T.value.course_info_id&&l.plate_id==e.id)).length>0&&(e.disabled=!0)}))}))},R=()=>{a({id:T.value.course_info_id}).then((e=>{var l=[];e.data&&(e.data.forEach((e=>{e.label=`${e.title}【题目数量：${e.question_num}】`,e.value=e.id,e.question_num=e.question_num,e.disabled=!1;var a=[];e.children.forEach((e=>{e.label=`${e.ver}【${e.ver_num}】`,e.value=e.ver,e.question_num=e.ver_num,a.push(e)})),e.children=a,l.push(e)})),B.value=l),setTimeout((()=>{K()}),0)}))},A=e=>{H.value=!0,T.value=j.value[e],T.value.question_source=T.value.question_source?JSON.parse(T.value.question_source):[],L(),R(),M()},I=()=>{R(),M()},P=e=>{U.value=e},G=e=>{T.question_source=e,K()},K=()=>{var e=0;B.value.forEach((l=>{l.children.forEach((l=>{T.value.question_source.filter((e=>{if(e[1]==l.ver)return e})).length>0&&(e+=l.question_num)}))})),J.value=e};return(e,l)=>{const a=s("el-button"),o=s("el-table-column"),n=s("EditPen"),u=s("el-icon"),d=s("el-link"),K=s("el-tag"),Q=s("el-popover"),W=s("el-table"),X=s("el-option"),Y=s("el-select"),Z=s("el-form-item"),ee=s("el-cascader-panel"),le=s("el-input"),ae=s("el-divider"),te=s("el-alert"),ie=s("el-form"),oe=s("el-dialog");return c(),r("div",k,[p("div",null,[_(a,{type:"primary",onClick:D,icon:"RefreshRight"}),_(a,{type:"primary",onClick:l[0]||(l[0]=e=>(H.value=!0,T.value={id:0,course_info_id:"",plate_id:"",question_source:[]},R(),M(),void L())),plain:"",icon:"DocumentAdd"},{default:v((()=>l[9]||(l[9]=[m("添加")]))),_:1,__:[9]}),_(a,{type:"danger",onClick:l[1]||(l[1]=e=>{U.value?w.confirm("此操作将永久删除该数据, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{i({id:U.value.id}).then((e=>{200==e.code?(x.success(e.msg),D()):x.error(e.msg)}))})).catch((()=>{x({type:"info",message:"已取消删除"})})):x.warning("请先选择要删除的课程")}),plain:"",icon:"DeleteFilled"},{default:v((()=>l[10]||(l[10]=[m("删除")]))),_:1,__:[10]})]),_(W,{tableHeight:F.value,data:j.value,size:"mini",stripe:"",style:{width:"100%","margin-top":"10px"},"highlight-current-row":"",onCurrentChange:P},{default:v((()=>[_(o,{type:"index",width:"50",align:"center"}),_(o,{prop:"kc_bm",label:"课程编码",width:"180",align:"center"}),_(o,{prop:"kc_mc",label:"课程名称",width:"180",align:"center"},{default:v((e=>[_(d,{onClick:l=>A(e.$index),type:"primary"},{default:v((()=>[m(f(e.row.kc_mc)+"  ",1),_(u,{style:{"font-size":"13px"}},{default:v((()=>[_(n)])),_:1})])),_:2},1032,["onClick"])])),_:1}),_(o,{prop:"done_standard",label:"完成标准(道题)",width:"180",align:"center"}),_(o,{prop:"ver",label:"在线课程版本",align:"center"}),_(o,{prop:"plate_name",label:"属于板块",align:"center"},{default:v((e=>[_(K,{type:"success"},{default:v((()=>[m(f(e.row.plate_name),1)])),_:2},1024)])),_:1}),_(o,{prop:"question_source",label:"题库来源",align:"center"},{default:v((e=>[e.row.question_source?(c(),h(Q,{key:0,placement:"right",width:400,trigger:"click"},{reference:v((()=>[_(a,{size:"small",type:"success"},{default:v((()=>[m(f(e.row.question_source_mc.length),1)])),_:2},1024)])),default:v((()=>[p("div",null,[p("div",q,[_(d,{onClick:l=>A(e.$index),type:"primary"},{default:v((()=>[_(u,{style:{"font-size":"13px"}},{default:v((()=>[_(n)])),_:1}),l[11]||(l[11]=m("编辑 "))])),_:2,__:[11]},1032,["onClick"])]),(c(!0),r(b,null,y(e.row.question_source_mc,((e,a)=>(c(),r("div",null,[(c(),r("div",{key:a,style:{margin:"5px 0px",width:"100%","text-align":"left",padding:"10px 5px","border-bottom":"1px solid #F2F6FC",display:"flex","align-items":"center"}},[m(f(a+1)+"、 "+f(e.title)+" ",1),l[12]||(l[12]=p("svg",{t:"1747969416047",class:"icon",viewBox:"0 0 1024 1024",width:"25",height:"25"},[p("path",{d:"M67.89847538 487.58400192l582.47994502 0L650.37842039 541.5173303 67.89847538 541.5173303l1e-8-53.93332838z",fill:"","p-id":"3388"}),p("path",{d:"M647.09938542 326.19222268L960.7412301 510.39457617l-313.64184468 189.18079521 0-373.38314871z",fill:"","p-id":"3389"})],-1)),m("【"+f(e.ver)+"】 ",1)]))])))),256))])])),_:2},1024)):g("",!0)])),_:1}),_(o,{prop:"create_name",label:"创建人",align:"center"}),_(o,{prop:"create_date",label:"创建时间",align:"center"})])),_:1},8,["tableHeight","data"]),_(oe,{modelValue:H.value,"onUpdate:modelValue":l[8]||(l[8]=e=>H.value=e),title:"添加课程",width:"720",top:"6vh","append-to-body":!0,"close-on-click-modal":!1,"close-on-press-escape":!1},{footer:v((()=>[p("span",z,[_(a,{onClick:l[6]||(l[6]=e=>H.value=!1)},{default:v((()=>l[15]||(l[15]=[m("取消")]))),_:1,__:[15]}),_(a,{type:"primary",onClick:l[7]||(l[7]=e=>(()=>{const e=T.value.question_source.map((([e,l])=>({source_id:e,ver:l})));T.value.question_source=JSON.stringify(T.value.question_source),T.value.question_source_b=JSON.stringify(e),t(T.value).then((e=>{200==e.code?(D(),H.value=!1,x.success(e.msg)):x.error(e.msg)}))})()),disabled:!T.value.course_info_id||0==T.value.question_source.length},{default:v((()=>l[16]||(l[16]=[m(" 确认 ")]))),_:1,__:[16]},8,["disabled"])])])),default:v((()=>[p("div",C,[p("div",V,[_(ie,{inline:!0,size:"large",model:T.value,class:"demo-form-inline",style:{width:"650px"}},{default:v((()=>[_(Z,{label:"选择课程"},{default:v((()=>[_(Y,{modelValue:T.value.course_info_id,"onUpdate:modelValue":l[2]||(l[2]=e=>T.value.course_info_id=e),class:"m-2",placeholder:"选择课程",style:{width:"270px"},onChange:I},{default:v((()=>[(c(!0),r(b,null,y(N.value,(e=>(c(),h(X,{key:e.id,label:e.title,value:e.id,disabled:e.disabled},null,8,["label","value","disabled"])))),128))])),_:1},8,["modelValue"])])),_:1}),l[13]||(l[13]=p("br",null,null,-1)),_(Z,{label:"选择板块"},{default:v((()=>[_(Y,{modelValue:T.value.plate_id,"onUpdate:modelValue":l[3]||(l[3]=e=>T.value.plate_id=e),class:"m-2",placeholder:"选择板块",style:{width:"130px"}},{default:v((()=>[(c(!0),r(b,null,y(S.value,(e=>(c(),h(X,{disabled:e.disabled,key:e.id,label:e.title,value:e.id},null,8,["disabled","label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),l[14]||(l[14]=p("br",null,null,-1)),_(Z,{label:"题库来源"},{default:v((()=>[p("div",$,[p("div",E,[_(ee,{modelValue:T.value.question_source,"onUpdate:modelValue":l[4]||(l[4]=e=>T.value.question_source=e),props:O,style:{width:"100%",height:"100%"},onChange:G,options:B.value},null,8,["modelValue","options"])])])])),_:1}),_(Z,{label:"完成标准"},{default:v((()=>[_(le,{modelValue:T.value.done_standard,"onUpdate:modelValue":l[5]||(l[5]=e=>T.value.done_standard=e),type:"Number",placeholder:"请输入完成标准",style:{width:"150px"}},null,8,["modelValue"]),_(ae,{direction:"vertical"}),m(" 总题量："+f(J.value)+" ",1),_(te,{style:{"margin-top":"5px"},closable:!1,title:"根据情况设置学生学习完成标准，如果是练习题可输入最低完成题目数，如果是电子书可以设置最低浏览时长按分钟",type:"info"})])),_:1})])),_:1,__:[13,14]},8,["model"])])])])),_:1},8,["modelValue"])])}}});export{j as default};
