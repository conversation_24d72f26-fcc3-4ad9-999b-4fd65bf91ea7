import{n as e,o as l,q as a,g as t,s as o,f as d}from"./exam-jF1-Sa8S.js";import{d as u,r as s,o as n,E as i,a as r,c,b as p,e as _,j as m,w as v,p as f,F as b,f as y,h as g,t as h,v as k}from"./index-BERmxY3Y.js";const x={class:"examplan",style:{padding:"5px"}},V={style:{}},w={style:{padding:"10px 20px"}},C={style:{color:"green"}},U={style:{color:"green"}},z={class:"dialog-footer"},F={class:"m-2",style:{display:"flex","justify-content":"center"}},j={style:{padding:"10px 20px"}},E={style:{padding:"10px 0px"}},H={style:{color:"#409EFF","font-weight":"bold"}},S={class:"dialog-footer",style:{padding:"10px 20px","text-align":"center","border-top":"1px solid #F2F6FC"}},$=u({__name:"public_ksplan",setup(u){const $=s([]);s({});const B=s(),D=s(window.innerHeight-200);n((()=>{L()}));const R=s({id:"",zy_bm:"",zy_mc:"",status:"0"}),T=s("");s("");const q=s(!1),A=s([]),K=s([]),P=s([]),Z=s([]),G=s({}),I=s(!1),J=s([]),L=()=>{e({filterZy:T.value}).then((e=>{200==e.code?$.value=e.data:i.error(e.msg)}))},M=e=>{B.value=e},N=()=>{t({}).then((e=>{200==e.code?(K.value=e.data.filter((e=>99==e.learn_type)),K.value.forEach((e=>{e.kc_mcs=`(${e.kc_bm})${e.kc_mc}`}))):i.error(e.msg)}))},O=e=>{var l=K.value.find((l=>l.id==e));R.value.kc_mc=l.kc_mc,R.value.kc_bm=l.kc_bm,Q()},Q=()=>{d({id:R.value.course_base_id}).then((e=>{200==e.code?(J.value=e.data,J.value.forEach((e=>{e.mc="版本："+e.ver+"【"+(e.is_online="在线课程")+"】【"+(1==e.is_pub?"已发布":"未发布")+"】"}))):i.error(e.msg)}))};return(e,t)=>{const d=r("el-button"),u=r("el-option"),s=r("el-select"),n=r("el-table-column"),W=r("EditPen"),X=r("el-icon"),Y=r("el-link"),ee=r("el-tag"),le=r("el-table"),ae=r("el-form-item"),te=r("el-input"),oe=r("el-radio"),de=r("el-radio-group"),ue=r("el-form"),se=r("el-tab-pane"),ne=r("el-tabs"),ie=r("el-dialog"),re=r("el-transfer");return p(),c("div",x,[_("div",null,[m(d,{type:"primary",onClick:L,icon:"RefreshRight"}),m(d,{type:"primary",onClick:t[0]||(t[0]=e=>(R.value={id:"",course_base_id:"",course_info_id:"",ks_lx:"",kc_bm:"",kc_mc:"",ks_sj:"",remark:"",xf:0,is_yy2_tk:"0",status:"0"},N(),void(q.value=!0))),plain:"",icon:"DocumentAdd"},{default:v((()=>t[18]||(t[18]=[f("添加")]))),_:1,__:[18]}),m(d,{type:"danger",onClick:t[1]||(t[1]=e=>{B.value?k.confirm("此操作将永久删除该计划, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{a(B.value.id).then((e=>{200==e.code?(i.success(e.msg),L()):i.error(e.msg)}))})).catch((()=>{i({type:"info",message:"已取消删除"})})):i.warning("请先选择要删除的课程")}),plain:"",icon:"DeleteFilled"},{default:v((()=>t[19]||(t[19]=[f("删除")]))),_:1,__:[19]}),m(s,{modelValue:T.value,"onUpdate:modelValue":t[2]||(t[2]=e=>T.value=e),class:"m-2",placeholder:"请选择专业",style:{width:"190px","margin-left":"5px"},onChange:t[3]||(t[3]=e=>L())},{default:v((()=>[(p(!0),c(b,null,y(A.value,(e=>(p(),g(u,{key:e.zy_bm,label:e.zy_mcs,value:e.zy_bm},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])]),m(le,{tableHeight:D.value,data:$.value,size:"mini",stripe:"",style:{width:"100%","margin-top":"10px"},"highlight-current-row":"",onCurrentChange:M},{default:v((()=>[m(n,{type:"index",width:"50",align:"center"}),m(n,{prop:"kc_bm",label:"课程编码",align:"center"}),m(n,{prop:"kc_mc",label:"课程名称",align:"center"},{default:v((e=>[m(Y,{onClick:l=>{return a=e.$index,R.value=$.value[a],R.value.status=R.value.status.toString(),Q(),q.value=!0,void N();var a},type:"primary"},{default:v((()=>[f(h(e.row.kc_mc)+"  ",1),m(X,{style:{"font-size":"13px"}},{default:v((()=>[m(W)])),_:1})])),_:2},1032,["onClick"])])),_:1}),m(n,{prop:"status",label:"状态",align:"center"},{default:v((e=>[1==e.row.status?(p(),g(ee,{key:0,type:"success"},{default:v((()=>t[20]||(t[20]=[f("正常")]))),_:1,__:[20]})):(p(),g(ee,{key:1,type:"danger"},{default:v((()=>t[21]||(t[21]=[f("禁用")]))),_:1,__:[21]}))])),_:1}),m(n,{prop:"remark",label:"备注",align:"center"}),m(n,{prop:"create_name",label:"创建人",align:"center"}),m(n,{prop:"create_date",label:"创建时间",align:"center"})])),_:1},8,["tableHeight","data"]),m(ie,{modelValue:q.value,"onUpdate:modelValue":t[13]||(t[13]=e=>q.value=e),title:"添加专业信息",width:"750",top:"6vh","append-to-body":!0,"close-on-click-modal":!1,"close-on-press-escape":!1},{footer:v((()=>[_("span",z,[m(d,{onClick:t[11]||(t[11]=e=>q.value=!1)},{default:v((()=>t[30]||(t[30]=[f("取消")]))),_:1,__:[30]}),m(d,{type:"primary",onClick:t[12]||(t[12]=e=>(R.value.id=R.value.id?R.value.id:0,void l(R.value).then((e=>{"200"==e.code?(i.success(e.msg),q.value=!1,L()):i.error(e.msg)})))),disabled:!R.value.kc_bm},{default:v((()=>t[31]||(t[31]=[f("保存 ")]))),_:1,__:[31]},8,["disabled"])])])),default:v((()=>[_("div",V,[m(ne,{"tab-position":"top",style:{},class:"demo-tabs"},{default:v((()=>[m(se,{label:"基本设置"},{default:v((()=>[_("div",w,[m(ue,{inline:!0,model:R.value,class:"demo-form-inline","label-position":"right","label-width":"110px"},{default:v((()=>[m(ae,{label:"选择课程"},{default:v((()=>[m(s,{modelValue:R.value.course_base_id,"onUpdate:modelValue":t[4]||(t[4]=e=>R.value.course_base_id=e),class:"m-2",placeholder:"Select",style:{width:"190px"},onChange:O},{default:v((()=>[(p(!0),c(b,null,y(K.value,(e=>(p(),g(u,{key:e.id,label:e.kc_mcs,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),t[28]||(t[28]=_("p",null,null,-1)),m(ae,{label:"在线课程版本"},{default:v((()=>[m(s,{modelValue:R.value.course_info_id,"onUpdate:modelValue":t[5]||(t[5]=e=>R.value.course_info_id=e),class:"m-2",placeholder:"Select",style:{width:"190px"}},{default:v((()=>[(p(!0),c(b,null,y(J.value,(e=>(p(),g(u,{key:e.id,label:e.mc,value:e.id},{default:v((()=>[_("span",null,[f("版本："+h(e.ver)+"【",1),_("span",C,h(e.is_online="在线课程"),1),t[22]||(t[22]=f("】 【")),_("span",U,h(1==e.is_pub?"已发布":"未发布"),1),t[23]||(t[23]=f("】"))])])),_:2},1032,["label","value"])))),128))])),_:1},8,["modelValue"]),t[25]||(t[25]=f("  没有在线课程？")),m(Y,{type:"primary",onClick:t[6]||(t[6]=e=>{window.open("/examplan/onlinecourse")})},{default:v((()=>t[24]||(t[24]=[f("前往添加")]))),_:1,__:[24]})])),_:1,__:[25]}),t[29]||(t[29]=_("p",null,null,-1)),m(ae,{label:"课程编码"},{default:v((()=>[m(te,{modelValue:R.value.kc_bm,"onUpdate:modelValue":t[7]||(t[7]=e=>R.value.kc_bm=e),disabled:!0,placeholder:"请输入课程名称",style:{width:"190px"}},null,8,["modelValue"])])),_:1}),m(ae,{label:"课程名称"},{default:v((()=>[m(te,{modelValue:R.value.kc_mc,"onUpdate:modelValue":t[8]||(t[8]=e=>R.value.kc_mc=e),disabled:!0,placeholder:"请输入课程名称",style:{width:"190px"}},null,8,["modelValue"])])),_:1}),m(ae,{label:"状态"},{default:v((()=>[m(de,{modelValue:R.value.status,"onUpdate:modelValue":t[9]||(t[9]=e=>R.value.status=e)},{default:v((()=>[m(oe,{label:"1"},{default:v((()=>t[26]||(t[26]=[f("启用")]))),_:1,__:[26]}),m(oe,{label:"0"},{default:v((()=>t[27]||(t[27]=[f("禁用")]))),_:1,__:[27]})])),_:1},8,["modelValue"])])),_:1}),m(ae,{label:"备注"},{default:v((()=>[m(te,{modelValue:R.value.remark,"onUpdate:modelValue":t[10]||(t[10]=e=>R.value.remark=e),rows:2,type:"textarea",style:{width:"500px"}},null,8,["modelValue"])])),_:1})])),_:1,__:[28,29]},8,["model"])])])),_:1})])),_:1})])])),_:1},8,["modelValue"]),m(ie,{modelValue:I.value,"onUpdate:modelValue":t[17]||(t[17]=e=>I.value=e),width:"700","close-on-click-modal":!1,"close-on-press-escape":!1,"append-to-body":!0,top:"6vh"},{default:v((()=>[_("div",F,[m(ne,{"tab-position":"top",style:{},class:"demo-tabs"},{default:v((()=>[m(se,{label:"板块设置"},{default:v((()=>[_("div",j,[_("div",E,[t[32]||(t[32]=f(" 为【")),_("span",H,h(G.value.kc_mc),1),t[33]||(t[33]=f("】课程指定板块 "))]),m(re,{modelValue:Z.value,"onUpdate:modelValue":t[14]||(t[14]=e=>Z.value=e),data:P.value,titles:["未开通板块","已开通板块"]},null,8,["modelValue","data"])])])),_:1})])),_:1})]),_("div",S,[m(d,{onClick:t[15]||(t[15]=e=>I.value=!1)},{default:v((()=>t[34]||(t[34]=[f("取消")]))),_:1,__:[34]}),m(d,{type:"primary",onClick:t[16]||(t[16]=e=>(()=>{var e=[];if(Z.value.forEach((l=>{e.push(l)})),0!=e.length){var l={id:G.value.id,plateids:e.join(",")};o(l).then((e=>{"200"==e.code?(i.success(e.msg),L()):i.error(e.msg)}))}else i.warning("请至少选择一个板块")})())},{default:v((()=>t[35]||(t[35]=[f("保存 ")]))),_:1,__:[35]})])])),_:1},8,["modelValue"])])}}});export{$ as default};
