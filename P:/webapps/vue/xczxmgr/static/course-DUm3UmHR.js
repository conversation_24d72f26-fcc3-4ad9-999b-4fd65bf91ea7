import{I as s,J as r}from"./index-BERmxY3Y.js";async function t(){return s.post("mgr_course/progress_stat")}async function n(){return s.post("mgr_course/info")}async function e(r,t,n,e){return s.post("mgr_course/info_upd",{id:r,is_online:t,is_pub:n,video_ids:e})}async function o(r){return s.post("mgr_course/info_add",{course_base_id:r})}async function a(r){return s.post("mgr_course/info_del",{id:r})}async function u(r){return s.post("examplan/getQuestion?guid="+r)}async function i(r,t){return s.post("examplan/importJobbank",r,{headers:{"Content-Type":"multipart/form-data"}})}function c(r){return s.post("mgr_course/getOlsCourseChapterData",r)}function p(r){return s.post("mgr_course/getJcHierarchyData",r)}function g(r){return s.post("mgr_course/SynCjToOlsChapter",r)}async function f(r){return await s.post("mgr_course/SaveOlsCourseChapterData",r)}async function _(t){return await s.post("/mgr_course/DelOlsCourseChapterData?"+r.stringify(t))}async function m(t){return await s.post("/mgr_course/getOlsCourseChapterJob?"+r.stringify(t))}async function l(t){return await s.post("/mgr_course/getOlsCourseChapter?"+r.stringify(t))}async function y(t){return await s.post("/mgr_course/getOlsChapterQuestion?"+r.stringify(t))}function C(){return s.post("mgr_course/getSelectOlsCourseList")}function d(r){return s.post("mgr_course/saveOlsChapterQuestion",r)}function O(t){return s.post("mgr_course/getPlateDataForOlsCourseById?"+r.stringify(t))}function h(t){return s.post("mgr_course/getPlateDataForOlsCourseById_op?"+r.stringify(t))}function D(r){return s.post("mgr_course/saveOlsCoursePlateByPlanid",r)}function b(r){return s.post("mgr_course/saveOlsCourse",r)}function v(r){return s.post("mgr_course/getCourseDetail",r)}export{o as A,n as C,a as D,f as S,e as U,v as a,O as b,b as c,h as d,t as e,m as f,u as g,l as h,i,y as j,d as k,C as l,c as m,p as n,_ as o,g as p,D as s};
