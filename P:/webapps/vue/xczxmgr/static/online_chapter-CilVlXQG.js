import{d as e,r as a,K as l,l as t,N as o,o as i,a as r,L as d,c as n,b as s,e as u,j as c,w as p,p as _,t as m,F as h,f,h as g,M as v,m as y,E as b,v as w}from"./index-BERmxY3Y.js";import{l as k,m as x,n as V,S as C,o as j,p as z}from"./course-DUm3UmHR.js";import{d as D}from"./dzs-DZN_gNG7.js";import{m as L}from"./message-Df6PNwXY.js";const U={class:"online_chapter"},N={class:"page",style:{flex:"1",padding:"1px"}},T={class:"header"},H={style:{float:"left"}},E={style:{float:"right",color:"#67C23A","font-size":"13px"}},B={class:"body"},q={key:0,style:{color:"blue"}},P={key:1,style:{color:"Red"}},R={class:"page",style:{flex:"1",padding:"1px"}},A={class:"header"},F={style:{float:"left"}},J={style:{float:"right",color:"#67C23A","font-size":"13px"}},K={class:"body"},M={style:{width:"100%","text-align":"center"}},S={style:{width:"510px"}},G={style:{color:"red"}},I={class:"dialog-footer"},O=e({__name:"online_chapter",setup(e){const O=a(!1),Q=a(!1),W=a(!1),X=a(""),Y=a(),Z=a(!1),$=a({}),ee=l({tableDataList:[],tableHeight:window.innerHeight-155,loading:!1,course_info_id:""}),ae=l({tableDataList:[],tableHeight:window.innerHeight-155,loading:!1,course_jc_id:""}),le=a("展开全部"),te=a([]),oe=a([]),ie=()=>{ee.loading=!0;var e={course_info_id:ee.course_info_id,status:O.value?"":"1"};x(e).then((e=>{ee.tableDataList=e.data,ee.loading=!1}))},re=()=>{ae.loading=!0;var e={course_jc_id:ae.course_jc_id};V(e).then((e=>{ae.tableDataList=e.data,ae.loading=!1}))},de=e=>{$.value={id:e.id,pid:e.pid,chapter_code:e.chapter_code,chapter_name:e.chapter_name,sn:e.sn,reamrk:e.reamrk,status:e.status,type:0===e.pid?"menu_dir":"menu"},X.value=0===e.pid?"编辑“章”信息":"编辑“节”信息",W.value=0===e.pid,Q.value=!0},ne=()=>{0===$.value.status&&"menu_dir"===$.value.type&&b({message:"你正在禁用父节点，如果禁用其下所有子节点将会同时被禁用！！",type:"warning"})},se=e=>0===e.status?"success-row":"",ue=(e,a)=>{e.forEach((e=>{Y.value.toggleRowExpansion(e,a),e.children&&ce(e.children,a)}))},ce=(e,a)=>{e.forEach((e=>{Y.value.toggleRowExpansion(e,a),e.children&&ce(e.children,a)}))},pe=(e,a)=>{if(!e)return"-";const l=oe.value.find((a=>Number(a.id)===Number(e)));return(null==l?void 0:l[a])||"-"},_e=(e,a)=>{if(!e)return"-";const l=te.value.find((a=>Number(a.id)===Number(e)));return(null==l?void 0:l[a])||"-"},me=t(),he=o();return i((()=>{(async()=>{try{k().then((e=>{200===e.code?(oe.value=e.data,me.query.course_info_id?me.query.course_info_id&&(ee.course_info_id=Number(me.query.course_info_id),ie(),he.replace({path:me.path})):(ee.course_info_id=6,ie())):L(e.msg||"获取课程列表失败","warning")}))}catch(e){L("获取课程列表失败","error")}})(),(async()=>{try{const e=await D();200===e.code?te.value=e.data:L(e.msg||"获取课程列表失败","warning")}catch(e){L("获取课程列表失败","error")}})(),re()})),(e,a)=>{const l=r("el-button"),t=r("el-tag"),o=r("el-option"),i=r("el-select"),k=r("el-checkbox"),x=r("el-table-column"),V=r("Edit"),D=r("el-icon"),L=r("Delete"),ce=r("CirclePlusFilled"),me=r("el-table"),he=r("el-form-item"),fe=r("el-radio"),ge=r("el-radio-group"),ve=r("el-input"),ye=r("el-input-number"),be=r("el-form"),we=r("el-dialog"),ke=d("loading");return s(),n("div",U,[u("div",N,[u("div",T,[c(l,{type:"success",plain:"",size:"default",icon:"refresh",round:"",onClick:ie}),c(l,{type:"primary",style:{"margin-right":"10px"},plain:"",onClick:a[0]||(a[0]=e=>{Z.value?(Z.value=!1,le.value="展开全部",ue(ee.tableDataList,!1)):(Z.value=!0,le.value="全部折叠",ue(ee.tableDataList,!0))}),size:"default",round:"",icon:"View","aria-label":""},{default:p((()=>[_(m(le.value),1)])),_:1}),c(t,{class:"el_tag_5"},{default:p((()=>a[18]||(a[18]=[_("在线课程")]))),_:1,__:[18]}),c(i,{modelValue:ee.course_info_id,"onUpdate:modelValue":a[1]||(a[1]=e=>ee.course_info_id=e),placeholder:"请选择在线课程",clearable:"",filterable:"",style:{width:"200px"},onChange:ie},{default:p((()=>[(s(!0),n(h,null,f(oe.value,(e=>(s(),g(o,{key:e.id,label:"[ "+e.kc_bm+" ] "+e.kc_mc,value:e.id},{default:p((()=>[u("span",H,m("("+e.id+")"+e.kc_mc),1),u("span",E,m(e.kc_bm),1)])),_:2},1032,["label","value"])))),128))])),_:1},8,["modelValue"]),c(k,{style:{"margin-left":"10px"},modelValue:O.value,"onUpdate:modelValue":a[2]||(a[2]=e=>O.value=e),size:"default",onChange:a[3]||(a[3]=e=>ie())},{default:p((()=>a[19]||(a[19]=[_("显示全部")]))),_:1,__:[19]},8,["modelValue"]),c(l,{type:"success",plain:"",onClick:a[4]||(a[4]=e=>($.value={id:0,pid:0,sn:0,reamrk:"",status:1,type:"menu_dir"},X.value="添加“章”信息",Q.value=!0,void(W.value=!0))),icon:"CirclePlus",round:"",size:"default"},{default:p((()=>a[20]||(a[20]=[_("添加“章”")]))),_:1,__:[20]})]),u("div",B,[v((s(),g(me,{border:"",height:ee.tableHeight,data:ee.tableDataList,"row-class-name":se,"row-key":"id",class:"moduleTable","default-expand-all":!1,"highlight-current-row":"",ref_key:"tablexTree",ref:Y},{default:p((()=>[c(x,{prop:"",label:"#",align:"center",width:"40"}),c(x,{prop:"id",label:"编号(id)",align:"center",width:"60"}),c(x,{prop:"pid",label:"上级编号(pid)",align:"center",width:"60"}),c(x,{prop:"chapter_code",label:"章节编码",align:"center",width:"60"}),c(x,{prop:"chapter_name",label:"章节名称",align:"left","header-align":"center","min-width":"160","show-overflow-tooltip":""}),c(x,{prop:"status",label:"操作",align:"center","min-width":"160"},{default:p((e=>[0===e.row.pid?(s(),g(l,{key:0,link:"",type:"primary",size:"small",onClick:a=>de(e.row)},{default:p((()=>[c(D,null,{default:p((()=>[c(V)])),_:1}),a[21]||(a[21]=_("编辑"))])),_:2,__:[21]},1032,["onClick"])):y("",!0),0!==e.row.pid?(s(),g(l,{key:1,link:"",type:"primary",size:"small",onClick:a=>de(e.row)},{default:p((()=>[c(D,null,{default:p((()=>[c(V)])),_:1}),a[22]||(a[22]=_("编辑"))])),_:2,__:[22]},1032,["onClick"])):y("",!0),(0===e.row.pid&&0===e.row.children.length||0!==e.row.pid)&&1!==e.row.status?(s(),g(l,{key:2,link:"",type:"danger",size:"small",onClick:a=>{return l=e.row,void w.confirm("此操作将永久删除【"+l.title+"】节点功能, 是否继续?","提示",{confirmButtonText:"删除",cancelButtonText:"取消",type:"warning"}).then((()=>{l.children.length>0&&0===l.pid&&b({type:"error",message:"存在节点信息!不能删除本章信息！！！请确认"});const e={id:l.id};j(e).then((e=>{e.data>0?(b({type:"success",message:"成功删除!"+e.data+"条"}),ie()):b({type:"error",message:"删除失败!"+e.msg})})).catch((e=>{b({type:"error",message:"删除失败!"+e})}))})).catch((()=>{b({type:"info",message:"取消删除!"})}));var l}},{default:p((()=>[c(D,null,{default:p((()=>[c(L)])),_:1}),a[23]||(a[23]=_("删除"))])),_:2,__:[23]},1032,["onClick"])):y("",!0),0===e.row.pid?(s(),g(l,{key:3,link:"",type:"primary",size:"small",onClick:a=>{return l=e.row,$.value={id:0,pid:l.id,sn:0,reamrk:"",status:1,type:"menu",old_chaptername:l.chapter_name},X.value="添加“节”信息",W.value=!0,void(Q.value=!0);var l}},{default:p((()=>[c(D,null,{default:p((()=>[c(ce)])),_:1}),a[24]||(a[24]=_("添加“节”"))])),_:2,__:[24]},1032,["onClick"])):y("",!0)])),_:1}),c(x,{prop:"sn",label:"排序","header-align":"center",align:"center",width:"50"}),c(x,{prop:"status",label:"状态",align:"center",width:"60","show-overflow-tooltip":""},{default:p((e=>[1===e.row.status?(s(),n("span",q,"启用")):(s(),n("span",P,"禁用"))])),_:1}),c(x,{prop:"kc_bm",label:"课程编码",width:"80",align:"center","header-align":"center"},{default:p((e=>[_(m(pe(e.row.course_info_id,"kc_bm")),1)])),_:1}),c(x,{prop:"kc_mc",label:"课程名称",width:"140",align:"left","header-align":"center","show-overflow-tooltip":""},{default:p((e=>[_(m(pe(e.row.course_info_id,"kc_mc")),1)])),_:1})])),_:1},8,["height","data"])),[[ke,ee.loading]])])]),u("div",R,[u("div",A,[c(l,{type:"success",plain:"",size:"default",icon:"refresh",round:"",onClick:re}),c(t,{class:"el_tag_5"},{default:p((()=>a[25]||(a[25]=[_("教材课程")]))),_:1,__:[25]}),c(i,{modelValue:ae.course_jc_id,"onUpdate:modelValue":a[5]||(a[5]=e=>ae.course_jc_id=e),placeholder:"请选择教材课程",clearable:"",filterable:"",style:{width:"260px"},onChange:re},{default:p((()=>[(s(!0),n(h,null,f(te.value,(e=>(s(),g(o,{key:e.id,label:"[ "+e.course_code+" ] "+e.course_name,value:e.id},{default:p((()=>[u("span",F,m("[ "+e.course_code+" ] "+e.course_name),1),u("span",J,m(e.ver),1)])),_:2},1032,["label","value"])))),128))])),_:1},8,["modelValue"]),c(l,{type:"primary",style:{"margin-left":"10px"},plain:"",onClick:a[6]||(a[6]=e=>(()=>{if(""!==ae.course_jc_id&&null!==ae.course_jc_id&&void 0!==ae.course_jc_id)if(""!==ee.course_info_id&&null!==ee.course_info_id&&void 0!==ee.course_info_id){var e=pe(ee.course_info_id,"kc_bm"),a=_e(ae.course_jc_id,"course_code");null!=ae.tableDataList&&0!==ae.tableDataList.length?ee.tableDataList.length>0?b({message:"所选在线课程已有章节，不能批量重复同步！！！",type:"error"}):e===a?w.confirm("此操作将批量同步教材章节为在线章节, 是否继续?","提示",{confirmButtonText:"确认同步",cancelButtonText:"取消",type:"warning"}).then((()=>{const e={course_jc_id:ae.course_jc_id,course_info_id:ee.course_info_id};z(e).then((e=>{e.data>0?(b({type:"success",message:"同步成功!"+e.data+"条"}),ie()):b({type:"info",message:"同步失败!"+e.msg})})).catch((e=>{b({type:"info",message:"同步失败!"+e})}))})).catch((()=>{b({type:"info",message:"取消同步!"})})):b({message:"左右所选课程代码不一致！请确认所需同步课程下拉选择，“在线”课程与“教材”课程一致！！！",type:"error"}):b({message:"所选教材课程无章节，不能同步！请先同步生成教材章节！！",type:"error"})}else b({message:"请先选择教材课程",type:"error"});else b({message:"请先选择教材课程",type:"error"})})()),size:"default",round:"",icon:"Pointer","aria-label":""},{default:p((()=>a[26]||(a[26]=[_("同步“教材”章节为“在线”章节")]))),_:1,__:[26]})]),u("div",K,[v((s(),g(me,{border:"",height:ae.tableHeight,data:ae.tableDataList,"row-key":"id",class:"moduleTable","default-expand-all":!1,"highlight-current-row":"",ref:"tablexjcTree"},{default:p((()=>[c(x,{prop:"",type:"index",label:"序号",align:"center",width:"40","header-align":"center"}),c(x,{prop:"chapter_code",label:"章编码",align:"center",width:"60"}),c(x,{prop:"chapter_name",label:"章名称",align:"left","header-align":"center","min-width":"160","show-overflow-tooltip":""}),c(x,{prop:"node_code",label:"节编码",align:"center",width:"60"}),c(x,{prop:"node_name",label:"节名称",align:"left","header-align":"center","min-width":"160","show-overflow-tooltip":""}),c(x,{prop:"course_code",label:"课程编码",width:"80",align:"center","header-align":"center"}),c(x,{prop:"course_name",label:"课程名称",width:"140",align:"left","header-align":"center","show-overflow-tooltip":""})])),_:1},8,["height","data"])),[[ke,ae.loading]])])]),c(we,{modelValue:Q.value,"onUpdate:modelValue":a[17]||(a[17]=e=>Q.value=e),title:X.value,"custom-class":"parentNodeclass",draggable:"",width:"800","close-on-press-escape":!1,"close-on-click-modal":!1},{footer:p((()=>[u("span",I,[c(l,{onClick:a[15]||(a[15]=e=>Q.value=!1)},{default:p((()=>a[31]||(a[31]=[_("关闭")]))),_:1,__:[31]}),c(l,{type:"success",onClick:a[16]||(a[16]=e=>{""!==$.value.chapter_code&&null!==$.value.chapter_code&&void 0!==$.value.chapter_code?""!==$.value.chapter_name&&null!==$.value.chapter_name&&void 0!==$.value.chapter_name?C($.value).then((e=>{e.data>0&&("menu_dir"===$.value.type&&$.value.status,Q.value=!1,b({message:"保存成功！",type:"success"}),ie())})):b({message:"章节名称不能为空",type:"info"}):b({message:"章节号不能为空",type:"info"})})},{default:p((()=>a[32]||(a[32]=[_(" 保存 ")]))),_:1,__:[32]})])])),default:p((()=>[u("div",M,[c(be,{model:$.value,inline:!0,"label-width":"80px"},{default:p((()=>["menu"===$.value.type?(s(),g(he,{key:0,label:"章名称"},{default:p((()=>[u("div",S,[u("span",G,m($.value.old_chaptername),1)])])),_:1})):y("",!0),c(he,{label:"类型"},{default:p((()=>[c(ge,{modelValue:$.value.type,"onUpdate:modelValue":a[7]||(a[7]=e=>$.value.type=e),disabled:W.value,style:{width:"200px"}},{default:p((()=>[c(fe,{label:"menu"},{default:p((()=>a[27]||(a[27]=[_("节")]))),_:1,__:[27]}),c(fe,{label:"menu_dir"},{default:p((()=>a[28]||(a[28]=[_("章")]))),_:1,__:[28]})])),_:1},8,["modelValue","disabled"])])),_:1}),c(he,{label:"是否启用"},{default:p((()=>[c(ge,{modelValue:$.value.status,"onUpdate:modelValue":a[10]||(a[10]=e=>$.value.status=e),style:{width:"200px"}},{default:p((()=>[c(fe,{label:1,onChange:a[8]||(a[8]=e=>ne())},{default:p((()=>a[29]||(a[29]=[_("启用")]))),_:1,__:[29]}),c(fe,{label:0,onChange:a[9]||(a[9]=e=>ne())},{default:p((()=>a[30]||(a[30]=[_("禁用")]))),_:1,__:[30]})])),_:1},8,["modelValue"])])),_:1}),c(he,{label:"menu"===$.value.type?"节号":"章号"},{default:p((()=>[c(ve,{modelValue:$.value.chapter_code,"onUpdate:modelValue":a[11]||(a[11]=e=>$.value.chapter_code=e),placeholder:"序号",style:{width:"200px"}},null,8,["modelValue"])])),_:1},8,["label"]),c(he,{label:"排序"},{default:p((()=>[c(ye,{modelValue:$.value.sn,"onUpdate:modelValue":a[12]||(a[12]=e=>$.value.sn=e),min:0,placeholder:"排序",style:{width:"200px"}},null,8,["modelValue"])])),_:1}),c(he,{label:"menu"===$.value.type?"节名称":"章名称"},{default:p((()=>[c(ve,{modelValue:$.value.chapter_name,"onUpdate:modelValue":a[13]||(a[13]=e=>$.value.chapter_name=e),placeholder:"名称",style:{width:"510px"}},null,8,["modelValue"])])),_:1},8,["label"]),c(he,{label:"备注"},{default:p((()=>[c(ve,{modelValue:$.value.reamrk,"onUpdate:modelValue":a[14]||(a[14]=e=>$.value.reamrk=e),type:"textarea",style:{width:"510px"}},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])])])),_:1},8,["modelValue","title"])])}}});export{O as default};
