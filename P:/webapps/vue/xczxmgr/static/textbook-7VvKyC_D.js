import{r as e,t as l,u as a}from"./exam-jF1-Sa8S.js";import{d as t,r as o,o as d,E as u,a as i,c as n,b as s,e as r,j as c,w as p,p as v,t as m,h as _,v as f}from"./index-BERmxY3Y.js";const h={class:"kcmgr",style:{padding:"5px"}},g={style:{padding:"5px 50px!important"}},b={style:{padding:"5px 50px!important"}},y={style:{height:"500px"}},x={style:{padding:"10px 20px"}},V=["src"],w={style:{height:"500px"}},k={class:"dialog-footer"},C=t({__name:"textbook",setup(t){const C=o([]);o({});const U=o(),z=o(window.innerHeight-200);d((()=>{F()}));const H=o({id:"",title:"",type:"",teacher:"",status:"0",version:"",cover:"",introduction:""});o("");const j=o(!1),F=()=>{e({}).then((e=>{200==e.code?C.value=e.data:u.error(e.msg)}))},O=e=>{U.value=e},S=(e,l)=>{200==e.code?(u.success(e.msg),H.value.cover=e.data):u.error(e.msg)};return(e,t)=>{const o=i("el-button"),d=i("H3"),A=i("mavon-editor"),B=i("el-table-column"),D=i("EditPen"),E=i("el-icon"),P=i("el-link"),R=i("el-tag"),T=i("el-table"),K=i("el-input"),$=i("el-form-item"),q=i("el-option"),G=i("el-select"),I=i("el-radio"),J=i("el-radio-group"),L=i("Plus"),M=i("el-upload"),N=i("el-form"),Q=i("el-tab-pane"),W=i("el-tabs"),X=i("el-dialog");return s(),n("div",h,[r("div",null,[c(o,{type:"primary",onClick:F,icon:"RefreshRight"}),c(o,{type:"primary",onClick:t[0]||(t[0]=e=>(H.value={id:"",title:"",type:"",teacher:"",status:"0",version:"",cover:"",introduction:""},void(j.value=!0))),plain:"",icon:"DocumentAdd"},{default:p((()=>t[11]||(t[11]=[v("添加")]))),_:1,__:[11]}),c(o,{type:"danger",onClick:t[1]||(t[1]=e=>{U.value?f.confirm("此操作将永久删除该教材, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{a(U.value.id).then((e=>{200==e.code?(u.success(e.msg),F()):u.error(e.msg)}))})).catch((()=>{u({type:"info",message:"已取消删除"})})):u.warning("请先选择要删除的课程")}),plain:"",icon:"DeleteFilled"},{default:p((()=>t[12]||(t[12]=[v("删除")]))),_:1,__:[12]})]),c(T,{tableHeight:z.value,data:C.value,size:"mini",stripe:"",style:{width:"100%","margin-top":"10px"},"highlight-current-row":"",onCurrentChange:O},{default:p((()=>[c(B,{type:"expand"},{default:p((e=>[r("div",g,[c(d,null,{default:p((()=>t[13]||(t[13]=[v("课程简介")]))),_:1,__:[13]})]),r("div",b,[c(A,{subfield:!1,defaultOpen:"preview",editable:!1,toolbarsFlag:!1,boxShadow:!1,xssOptions:{},html:!1,modelValue:e.row.introduction,"onUpdate:modelValue":l=>e.row.introduction=l},null,8,["modelValue","onUpdate:modelValue"])])])),_:1}),c(B,{type:"index",width:"50",align:"center"}),c(B,{prop:"title",label:"教材名称",width:"180",align:"center"},{default:p((e=>[c(P,{onClick:l=>{return a=e.$index,H.value=C.value[a],H.value.status=H.value.status.toString(),void(j.value=!0);var a},type:"primary"},{default:p((()=>[v(m(e.row.title)+"  ",1),c(E,{style:{"font-size":"13px"}},{default:p((()=>[c(D)])),_:1})])),_:2},1032,["onClick"])])),_:1}),c(B,{prop:"type",label:"教材类型",width:"180",align:"center"}),c(B,{prop:"teacher",label:"教师",align:"center"}),c(B,{prop:"version",label:"版本",align:"center"}),c(B,{prop:"status",label:"状态",align:"center"},{default:p((e=>[1==e.row.status?(s(),_(R,{key:0,type:"success"},{default:p((()=>t[14]||(t[14]=[v("正常")]))),_:1,__:[14]})):(s(),_(R,{key:1,type:"danger"},{default:p((()=>t[15]||(t[15]=[v("禁用")]))),_:1,__:[15]}))])),_:1}),c(B,{prop:"create_name",label:"创建人",align:"center"}),c(B,{prop:"create_date",label:"创建时间",align:"center"})])),_:1},8,["tableHeight","data"]),c(X,{modelValue:j.value,"onUpdate:modelValue":t[10]||(t[10]=e=>j.value=e),title:"添加教材信息",width:"800",top:"6vh","append-to-body":!0,"close-on-click-modal":!1,"close-on-press-escape":!1},{footer:p((()=>[r("span",k,[c(o,{onClick:t[8]||(t[8]=e=>j.value=!1)},{default:p((()=>t[18]||(t[18]=[v("取消")]))),_:1,__:[18]}),c(o,{type:"primary",onClick:t[9]||(t[9]=e=>(H.value.id=H.value.id?H.value.id:0,void l(H.value).then((e=>{"200"==e.code?(u.success(e.msg),j.value=!1,F()):u.error(e.msg)})))),disabled:!H.value.title||!H.value.type||!H.value.version},{default:p((()=>t[19]||(t[19]=[v(" 保存 ")]))),_:1,__:[19]},8,["disabled"])])])),default:p((()=>[r("div",y,[c(W,{"tab-position":"left",style:{height:"500px"},class:"demo-tabs"},{default:p((()=>[c(Q,{label:"基本设置"},{default:p((()=>[r("div",x,[c(N,{inline:!0,model:H.value,class:"demo-form-inline"},{default:p((()=>[c($,{label:"教材名称"},{default:p((()=>[c(K,{modelValue:H.value.title,"onUpdate:modelValue":t[2]||(t[2]=e=>H.value.title=e),placeholder:"教材名称",clearable:""},null,8,["modelValue"])])),_:1}),c($,{label:"类型"},{default:p((()=>[c(G,{modelValue:H.value.type,"onUpdate:modelValue":t[3]||(t[3]=e=>H.value.type=e),style:{width:"150px"},placeholder:"Activity zone",clearable:""},{default:p((()=>[c(q,{label:"电子教材",value:"电子教材"}),c(q,{label:"实体教材",value:"实体教材"})])),_:1},8,["modelValue"])])),_:1}),c($,{label:"编写教师"},{default:p((()=>[c(K,{modelValue:H.value.teacher,"onUpdate:modelValue":t[4]||(t[4]=e=>H.value.teacher=e),placeholder:"编写教师",clearable:""},null,8,["modelValue"])])),_:1}),c($,{label:"版本"},{default:p((()=>[c(K,{modelValue:H.value.version,"onUpdate:modelValue":t[5]||(t[5]=e=>H.value.version=e),placeholder:"版本",clearable:""},null,8,["modelValue"])])),_:1}),c($,{label:"教材状态"},{default:p((()=>[c(J,{modelValue:H.value.status,"onUpdate:modelValue":t[6]||(t[6]=e=>H.value.status=e)},{default:p((()=>[c(I,{label:"1",border:""},{default:p((()=>t[16]||(t[16]=[v("启用")]))),_:1,__:[16]}),c(I,{label:"0",border:""},{default:p((()=>t[17]||(t[17]=[v("禁用")]))),_:1,__:[17]})])),_:1},8,["modelValue"])])),_:1}),c($,{label:"封面",style:{"align-items":"center","font-weight":"bold"}},{default:p((()=>[c(M,{class:"avatar-uploader",action:"https://xczx7.swufe.edu.cn/oc/xczk/examplan/upload","show-file-list":!1,"on-success":S},{default:p((()=>[H.value.cover?(s(),n("img",{key:0,style:{width:"108px",height:"108px"},src:"https://xczx7.swufe.edu.cn/oc/xczk/examplan/getFile?path="+H.value.cover,class:"avatar"},null,8,V)):(s(),_(E,{key:1,class:"avatar-uploader-icon"},{default:p((()=>[c(L)])),_:1}))])),_:1})])),_:1})])),_:1},8,["model"])])])),_:1}),c(Q,{label:"课程简介"},{default:p((()=>[r("div",w,[c(A,{boxShadow:!1,xssOptions:{},html:!1,modelValue:H.value.introduction,"onUpdate:modelValue":t[7]||(t[7]=e=>H.value.introduction=e)},null,8,["modelValue"])])])),_:1})])),_:1})])])),_:1},8,["modelValue"])])}}});export{C as default};
