import{d as e,r as l,o as a,a as i,L as t,c as o,b as r,e as d,j as n,w as u,p as s,F as c,f as p,h as v,M as m,t as g,m as f,q as _,O as h,ae as w,P as b,ac as x,_ as y}from"./index-BERmxY3Y.js";import{q as k,r as $,t as V,v as z}from"./kcmgr-_69jTmcm.js";import{m as C}from"./message-Df6PNwXY.js";const I={class:"page"},M={class:"header"},L={class:"body"},U=["src","onClick"],j={key:0,class:"upload-file-info"},S={class:"dialog-footer"},F=["src"],q=["src"],J={class:"dialog-footer"},O=y(e({__name:"course_video",setup(e){const y=l([]),O=l({pageLoading:!1,pageLoadingText:"获取数据中，请稍后...",pageLoadingSvg:'\n        <path class="path" d="\n          M 30 15\n          L 28 17\n          M 25.61 25.61\n          A 15 15, 0, 0, 1, 15 30\n          A 15 15, 0, 1, 1, 27.99 7.5\n          L 15 15\n        " style="stroke-width: 4px; fill: rgba(0, 0, 0, 0)"/>\n      ',selected_course_id:null}),N=l({visible:!1,progress:0,uploading:!1,form:{title:"",file:null,course_id:null}}),T=l({visible:!1,url:""}),A=l({visible:!1,url:""}),D=l({visible:!1,loading:!1,course_id:null}),P=l([]),B=async()=>{try{const e=await V();200===e.code?P.value=e.data:C(e.msg||"获取课程列表失败","warning")}catch(e){C("获取课程列表失败","error")}},E=()=>({height:"40px"}),G=()=>({padding:"4px 0"}),Y=e=>{if(!e)return"-";const l=P.value.find((l=>Number(l.id)===Number(e)));return(null==l?void 0:l.course_name)||"-"},Q=async()=>{O.value.pageLoading=!0;try{await B();const e={course_id:O.value.selected_course_id},l=await k(e);200===l.code?y.value=l.data:C(l.msg||"获取视频列表失败","warning")}catch(e){C("获取数据失败","error")}finally{O.value.pageLoading=!1}},W=()=>{Q()},X=async()=>{await B(),N.value.visible=!0,N.value.form={title:"",file:null,course_id:null}},Z=async()=>{await B(),D.value.visible=!0,D.value.course_id=null},H=async()=>{if(D.value.course_id)try{D.value.loading=!0;const e=await $({course_jc_id:D.value.course_id});"200"===e.code.toString()?(C("同步成功","success"),Q(),D.value.visible=!1):C(e.msg||"同步失败","error")}catch(e){C("同步过程中发生错误","error")}finally{D.value.loading=!1}else C("请选择要同步的课程","warning")},R=e=>{const l=e.raw||e.file;return"video/mp4"===l.type?(N.value.form.file=l,N.value.form.title=(a=l.name).slice(0,a.lastIndexOf(".")),!1):(C("只能上传 MP4 格式的视频文件！","warning"),!1);var a},K=l(),ee=e=>{K.value.clearFiles();const l=e[0];l.uid=b(),K.value.handleStart(l)},le=async e=>{try{const i="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI1IiwiZXhwIjoyMzQ2NzYwMDY3fQ.APtOpmmdNvGGoBhuTdwFuSijPphU4WJVyt5BoIZF6tA",t="https://m1.swufe-online.com/video",o=5242880,r=Math.ceil(e.size/o),d="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){const l=16*Math.random()|0;return("x"===e?l:3&l|8).toString(16)})),n=N.value.form.title,u=`${d}.${(l=e.name).slice((l.lastIndexOf(".")-1>>>0)+2)}`;N.value.uploading=!0,N.value.progress=0;try{const l=await x.post(`${t}/init?token=${i}`,{filename:u,total_chunks:r,file_id:d,title:n});if(l.data.error)throw new Error(l.data.error);for(let n=0;n<r;n++){const l=n*o,a=Math.min(e.size,l+o),s=e.slice(l,a),c=new FormData;c.append("file",s),c.append("file_id",d),c.append("chunk_index",n.toString()),c.append("filename",u);const p=await x.post(`${t}/chunk?token=${i}`,c,{headers:{"Content-Type":"multipart/form-data"}});if(p.data.error)throw new Error(p.data.error);N.value.progress=Math.floor((n+1)/r*100)}const a=await x.post(`${t}/complete?token=${i}`,{file_id:d,title:n,filename:u});if(a.data.error)throw new Error(a.data.error);const s=new Date,c=(e,l=2)=>String(e).padStart(l,"0"),p=`${s.getFullYear()}-${c(s.getMonth()+1)}-${c(s.getDate())}`,v=`${c(s.getHours())}:${c(s.getMinutes())}:${c(s.getSeconds())}`,m=c(s.getMilliseconds(),3)+"000";z({title:n,mp4_filename:a.data.path.replace("http://m1.swufe-online.com/",""),duration:a.data.duration,duration_s:a.data.duration_s,size:a.data.size,size_s:a.data.size_s,width:a.data.width,height:a.data.height,date_created:`${p} ${v}.${m}`,url:"http://m1.swufe-online.com/",image:a.data.frame_path.replace("http://m1.swufe-online.com/",""),course_jc_id:N.value.form.course_id}),C("上传成功","success"),N.value.visible=!1,Q()}catch(a){C(a.message||"上传失败","error")}finally{N.value.uploading=!1}}catch(a){C(a.message||"上传失败","error"),N.value.uploading=!1}var l},ae=async()=>{if(!N.value.form.title)return void C("请输入视频标题","warning");if(!N.value.form.file)return void C("请选择要上传的视频文件","warning");if(!N.value.form.course_id)return void C("请选择所属课程","warning");const e=N.value.form.file;return le(e)};return a((()=>{Q()})),(e,l)=>{const a=i("el-button"),b=i("el-option"),x=i("el-select"),k=i("el-table-column"),$=i("el-table"),V=i("el-form-item"),z=i("el-input"),C=i("el-icon"),B=i("el-upload"),le=i("el-form"),ie=i("el-dialog"),te=t("loading");return r(),o("div",I,[d("div",M,[n(a,{plain:"",type:"success",size:"small",onClick:Q,icon:"refresh"},{default:u((()=>l[10]||(l[10]=[s("刷新 ")]))),_:1,__:[10]}),n(x,{modelValue:O.value.selected_course_id,"onUpdate:modelValue":l[0]||(l[0]=e=>O.value.selected_course_id=e),placeholder:"请选择课程",clearable:"",filterable:"",size:"small",style:{width:"260px"},onChange:W},{default:u((()=>[(r(!0),o(c,null,p(P.value,(e=>(r(),v(b,{key:e.id,label:`${e.course_code} ${e.course_name}`,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"]),n(a,{type:"primary",size:"small",onClick:X,icon:"upload"},{default:u((()=>l[11]||(l[11]=[s("上传视频 ")]))),_:1,__:[11]}),n(a,{type:"warning",size:"small",onClick:Z,icon:"refresh"},{default:u((()=>l[12]||(l[12]=[s("同步视频 ")]))),_:1,__:[12]})]),d("div",L,[m((r(),v($,{data:y.value,width:"100%",height:"calc(100vh - 135px)",ref:"multipleTable","highlight-current-row":"",size:"small",border:"","element-loading-text":O.value.pageLoadingText,"element-loading-spinner":O.value.pageLoadingSvg,"element-loading-svg-view-box":"-10, -10, 50, 50","element-loading-background":"rgba(122, 122, 122, 0.8)","row-style":E,"cell-style":G},{default:u((()=>[n(k,{prop:"",type:"index",label:"序号",align:"center",width:"60","header-align":"center"}),n(k,{label:"课程名称",align:"left","min-width":"150","header-align":"center","show-overflow-tooltip":""},{default:u((e=>[s(g(Y(e.row.course_jc_id)),1)])),_:1}),n(k,{label:"视频标题",align:"left","min-width":"100","header-align":"center","show-overflow-tooltip":"",prop:"title"}),n(k,{label:"时长",align:"center",width:"100","header-align":"center",prop:"duration_s"}),n(k,{label:"文件大小",align:"center",width:"100","header-align":"center",prop:"size_s"}),n(k,{label:"分辨率",align:"center",width:"120","header-align":"center"},{default:u((e=>[s(g(e.row.width)+"x"+g(e.row.height),1)])),_:1}),n(k,{label:"预览图",align:"center",width:"120","header-align":"center"},{default:u((e=>[d("img",{src:`${e.row.url}${e.row.image}`,style:{width:"100px",height:"60px","object-fit":"cover",cursor:"pointer"},onClick:l=>{return a=e.row,A.value.url=`${a.url}/${a.image}`,void(A.value.visible=!0);var a}},null,8,U)])),_:1}),n(k,{label:"上传时间",align:"center",width:"200","header-align":"center",prop:"date_created"}),n(k,{label:"操作",align:"center",width:"180","header-align":"center"},{default:u((e=>[n(a,{size:"small",type:"primary",icon:"video-play",onClick:l=>{return a=e.row,T.value.url=`${a.url}/${a.mp4_filename}`,void(T.value.visible=!0);var a}},{default:u((()=>l[13]||(l[13]=[s("预览 ")]))),_:2,__:[13]},1032,["onClick"])])),_:1})])),_:1},8,["data","element-loading-text","element-loading-spinner"])),[[te,O.value.pageLoading]])]),n(ie,{modelValue:N.value.visible,"onUpdate:modelValue":l[4]||(l[4]=e=>N.value.visible=e),title:"上传视频",width:"500px","close-on-click-modal":!1,"close-on-press-escape":!N.value.uploading},{footer:u((()=>[d("span",S,[n(a,{onClick:l[3]||(l[3]=e=>N.value.visible=!1),disabled:N.value.uploading},{default:u((()=>l[17]||(l[17]=[s("取消")]))),_:1,__:[17]},8,["disabled"]),n(a,{type:"primary",onClick:ae,loading:N.value.uploading,disabled:N.value.uploading},{default:u((()=>[s(g(N.value.uploading?"上传中...":"确定"),1)])),_:1},8,["loading","disabled"])])])),default:u((()=>[n(le,{model:N.value.form,"label-width":"100px"},{default:u((()=>[n(V,{label:"所属课程",required:""},{default:u((()=>[n(x,{modelValue:N.value.form.course_id,"onUpdate:modelValue":l[1]||(l[1]=e=>N.value.form.course_id=e),placeholder:"请选择课程",filterable:"",clearable:"",style:{width:"100%"}},{default:u((()=>[(r(!0),o(c,null,p(P.value,(e=>(r(),v(b,{key:e.id,label:`${e.course_code} ${e.course_name}`,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),n(V,{label:"视频标题",required:""},{default:u((()=>[n(z,{modelValue:N.value.form.title,"onUpdate:modelValue":l[2]||(l[2]=e=>N.value.form.title=e),placeholder:"请输入视频标题"},null,8,["modelValue"])])),_:1}),n(V,{label:"视频文件",required:""},{default:u((()=>[n(B,{class:"upload-demo",drag:"",ref_key:"upload",ref:K,"show-file-list":!1,action:"#","auto-upload":!1,"on-change":R,limit:1,multiple:!1,disabled:N.value.uploading,"on-exceed":ee,accept:"video/mp4"},{tip:u((()=>l[14]||(l[14]=[d("div",{class:"el-upload__tip"}," 只能上传 mp4 文件 ",-1)]))),default:u((()=>[n(C,{class:"el-icon--upload"},{default:u((()=>[n(_(h))])),_:1}),l[15]||(l[15]=d("div",{class:"el-upload__text"},[s(" 将文件拖到此处，或"),d("em",null,"点击上传")],-1))])),_:1,__:[15]},8,["disabled"]),N.value.form.file?(r(),o("div",j," 已选择: "+g(N.value.form.file.name)+" ("+g((N.value.form.file.size/1048576).toFixed(2))+"MB) ",1)):f("",!0)])),_:1}),N.value.uploading?(r(),v(V,{key:0},{default:u((()=>[n(_(w),{percentage:N.value.progress,format:e=>e+"%"},null,8,["percentage","format"]),l[16]||(l[16]=d("div",{class:"upload-status"},"正在上传中，请勿关闭窗口...",-1))])),_:1,__:[16]})):f("",!0)])),_:1},8,["model"])])),_:1},8,["modelValue","close-on-press-escape"]),n(ie,{modelValue:T.value.visible,"onUpdate:modelValue":l[5]||(l[5]=e=>T.value.visible=e),title:"视频预览",width:"800px","destroy-on-close":!0,"close-on-click-modal":!1},{default:u((()=>[T.value.url?(r(),o("video",{key:0,src:T.value.url,controls:"",style:{width:"100%"}},null,8,F)):f("",!0)])),_:1},8,["modelValue"]),n(ie,{modelValue:A.value.visible,"onUpdate:modelValue":l[6]||(l[6]=e=>A.value.visible=e),title:"图片预览",width:"800px","close-on-click-modal":!1},{default:u((()=>[d("img",{src:A.value.url,style:{width:"100%"}},null,8,q),l[18]||(l[18]=s(" /> "))])),_:1,__:[18]},8,["modelValue"]),n(ie,{modelValue:D.value.visible,"onUpdate:modelValue":l[9]||(l[9]=e=>D.value.visible=e),title:"同步视频",width:"400px","close-on-click-modal":!1},{footer:u((()=>[d("span",J,[n(a,{onClick:l[8]||(l[8]=e=>D.value.visible=!1),disabled:D.value.loading},{default:u((()=>l[19]||(l[19]=[s("取消")]))),_:1,__:[19]},8,["disabled"]),n(a,{type:"primary",onClick:H,loading:D.value.loading,disabled:!D.value.course_id},{default:u((()=>l[20]||(l[20]=[s(" 确定 ")]))),_:1,__:[20]},8,["loading","disabled"])])])),default:u((()=>[n(le,{"label-width":"100px"},{default:u((()=>[n(V,{label:"选择课程",required:""},{default:u((()=>[n(x,{modelValue:D.value.course_id,"onUpdate:modelValue":l[7]||(l[7]=e=>D.value.course_id=e),placeholder:"请选择要同步的课程",style:{width:"100%"},filterable:""},{default:u((()=>[(r(!0),o(c,null,p(P.value,(e=>(r(),v(b,{key:e.id,label:`${e.course_code} ${e.course_name}`,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1})])),_:1})])),_:1},8,["modelValue"])])}}}),[["__scopeId","data-v-98011d65"]]);export{O as default};
