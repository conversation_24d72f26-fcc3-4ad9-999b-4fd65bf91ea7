import{G as e,a as l,S as a}from"./settings-CpWN36lq.js";import{d as t,K as i,r,o as s,a as n,L as o,c as d,b as c,e as u,j as h,w as p,p as g,t as f,F as m,f as v,h as b,M as w,m as y,E as k}from"./index-BERmxY3Y.js";const _={class:"moduleAuth page"},x={class:"header"},L={style:{float:"left"}},V={style:{float:"right",color:"#8492a6","font-size":"13px","margin-right":"10px","margin-left":"10px"}},z={class:"body"},C={key:0},j=t({__name:"module-auth",setup(t){const j=i({loading:!1,tableHeight:window.innerHeight-180,keySearch:""}),E=r("展开全部"),H=r(!1),S=r(),U=r([]),A=r(""),O=r([]),R=()=>{j.loading=!0;const e={role_id:A.value};l(e).then((e=>{F(e.data),j.loading=!1})).catch((e=>{j.loading=!1}))},F=e=>{for(var l=e,a=0;a<l.length;a++){l[a].permsList=[],l[a].checkList=l[a].hasperms.split(",");for(var t=0;t<l[a].children.length;t++){var i=l[a].children[t].perms;l[a].children[t].permsList=i.split(","),l[a].children[t].checkList=l[a].children[t].hasperms.split(",")}}U.value=l},G=({row:e,rowIndex:l})=>0===e.pid?"warning-row":"",I=(e,l)=>{e.forEach((e=>{S.value.toggleRowExpansion(e,l),e.children&&J(e.children,l)}))},J=(e,l)=>{e.forEach((e=>{S.value.toggleRowExpansion(e,l),e.children&&J(e.children,l)}))};return s((()=>{e({key:""}).then((e=>{O.value=e.data,A.value=e.data[0].id,R()})).catch((e=>{}))})),(e,l)=>{const t=n("el-button"),i=n("el-tag"),r=n("el-option"),s=n("el-select"),F=n("el-table-column"),J=n("el-checkbox"),K=n("el-checkbox-group"),M=n("el-table"),N=o("loading");return c(),d("div",_,[u("div",x,[h(t,{type:"success",plain:"",size:"default",icon:"refresh",round:"",onClick:R}),h(t,{type:"primary",plain:"",onClick:l[0]||(l[0]=e=>{H.value?(H.value=!1,E.value="展开全部",I(U.value,!1)):(H.value=!0,E.value="全部折叠",I(U.value,!0))}),round:"",icon:"View",size:"default","aria-label":""},{default:p((()=>[g(f(E.value),1)])),_:1}),h(i,{size:"large",style:{"margin-left":"10px","margin-right":"5px"}},{default:p((()=>l[3]||(l[3]=[g("角色:")]))),_:1,__:[3]}),h(s,{modelValue:A.value,"onUpdate:modelValue":l[1]||(l[1]=e=>A.value=e),size:"default",clearable:"",filterable:"",style:{width:"200px"},onChange:R,placeholder:"专业编码及名称"},{default:p((()=>[(c(!0),d(m,null,v(O.value,(e=>(c(),b(r,{key:e.id,label:e.title,value:e.id},{default:p((()=>[u("span",L,f(e.id),1),u("span",V,f(e.title),1)])),_:2},1032,["label","value"])))),128))])),_:1},8,["modelValue"]),h(t,{type:"success",plain:"",size:"default",round:"",style:{"margin-left":"10px"},icon:"CircleCheck",onClick:l[2]||(l[2]=e=>(()=>{for(var e=[],l=U.value,t=0;t<l.length;t++)for(var i=l[t].children,r=0;r<i.length;r++)if(0!==i[r].checkList.length){var s=i[r].checkList.join(",");""!==s&&(0===s.indexOf(",")&&(s=s.replace(",","")),e.push({role_id:A.value,module_id:i[r].puid,perms:s}))}var n=JSON.stringify(e);a({json:n}).then((e=>{"1"===e.data?(k({message:"保存成功！",type:"success"}),R()):k({message:"保存失败！"+e.msg,type:"error"})}))})())},{default:p((()=>l[4]||(l[4]=[g("保存")]))),_:1,__:[4]})]),u("div",z,[w((c(),b(M,{border:"",data:U.value,height:j.tableHeight,"row-key":"id",class:"modTable",ref_key:"tableAuth",ref:S,"row-class-name":G},{default:p((()=>[h(F,{prop:"",label:"#",align:"center",width:"60"}),h(F,{prop:"id",label:"节点编码",align:"center",width:"80"}),h(F,{prop:"pid",label:"父节点",align:"center",width:"80"}),h(F,{prop:"title",label:"中文名称","header-align":"center",align:"center",width:"160","show-overflow-tooltip":""}),h(F,{prop:"address",label:"操作","header-align":"left",align:"left"},{default:p((e=>[0!==e.row.permsList.length?(c(),d("div",C,[h(K,{modelValue:e.row.checkList,"onUpdate:modelValue":l=>e.row.checkList=l},{default:p((()=>[(c(!0),d(m,null,v(e.row.permsList,(e=>(c(),b(J,{key:e,label:e},null,8,["label"])))),128))])),_:2},1032,["modelValue","onUpdate:modelValue"])])):y("",!0)])),_:1})])),_:1},8,["data","height"])),[[N,j.loading]])])])}}});export{j as default};
