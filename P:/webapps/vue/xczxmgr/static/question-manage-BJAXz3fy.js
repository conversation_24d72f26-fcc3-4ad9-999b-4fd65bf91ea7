import{g as e,G as a,b as l,c as t,d as s}from"./question-DWIQ7W1h.js";import{_ as i,a as n}from"./preview_question_bank.vue_vue_type_style_index_0_lang-DU1g_tNm.js";import{d as o,r as d,o as r,S as u,Y as c,a as _,L as p,M as g,c as b,b as v,e as h,F as w,f as m,m as k,j as f,t as y,w as x,p as j,h as q,E as V,N as S,K as z,x as T,z as C}from"./index-BERmxY3Y.js";import"./tools-CvIkJR06.js";const N=["v-loading","element-loading-text"],U={class:"preview-question-bank"},A={key:0,class:"main-content"},R=["data-question-id"],L={class:"question-header"},I={class:"question-jobid"},M={class:"question-type"},E={class:"question-jobid"},P={class:"question-jobid"},D={class:"question-header"},H={class:"el_tag_5"},O={class:"jobbank-title"},$={key:0,class:"question-header"},B={class:"el_tag_5"},J={class:"jobbank-title"},Q={key:1},W={class:"question-options"},F={key:0},G={class:"opt_options"},K={class:"option-label"},Y={key:1},X={style:{display:"none"}},Z={class:"opt_options"},ee={class:"option-label"},ae={key:2},le={style:{display:"none"}},te={class:"opt_options"},se={class:"option-label"},ie={class:"question-header"},ne={class:"el_tag_5"},oe={class:"question-title"},de={class:"question-header"},re={class:"el_tag_5"},ue={class:"question-title"},ce={key:2},_e={class:"question-header"},pe={class:"el_tag_5"},ge={class:"question-title"},be={key:0},ve={key:1},he={key:2,style:{color:"red"}},we={class:"question-header"},me={class:"el_tag_5"},ke={class:"question-title"},fe={key:3},ye={class:"question-header"},xe={class:"el_tag_5"},je={class:"jobbank-title"},qe={key:0},Ve={class:"question-options"},Se={key:0},ze={class:"opt_options"},Te={class:"option-label"},Ce={key:1},Ne={style:{display:"none"}},Ue={class:"opt_options"},Ae={class:"option-label"},Re={key:2},Le={style:{display:"none"}},Ie={class:"opt_options"},Me={class:"option-label"},Ee={class:"question-header"},Pe={class:"el_tag_5"},De={class:"question-title"},He={class:"question-header"},Oe={class:"el_tag_5"},$e={class:"question-title"},Be={key:1},Je={class:"question-header"},Qe={class:"el_tag_5"},We={class:"question-title"},Fe={key:0},Ge={key:1},Ke={key:2,style:{color:"red"}},Ye={class:"question-header"},Xe={class:"el_tag_5"},Ze={class:"question-title"},ea={key:2},aa={class:"question-header"},la={class:"el_tag_5"},ta={class:"question-title"},sa={class:"question-header"},ia={class:"el_tag_5"},na={class:"question-title"},oa={key:4},da={class:"question-header"},ra={class:"el_tag_5"},ua={class:"question-title"},ca={class:"question-header"},_a={class:"el_tag_5"},pa={class:"question-title"},ga={class:"question-header"},ba={class:"question-header"},va={class:"question-header"},ha={class:"question-header"},wa={class:"question-header"},ma={key:5,class:"ai-test-result"},ka={class:"ai-test-header"},fa={class:"ai-results-container"},ya={class:"ai-result-column"},xa={class:"ai-bot-header"},ja={class:"ai-reasoning"},qa={class:"ai-section"},Va={class:"ai-content"},Sa={class:"ai-section"},za={class:"ai-answer"},Ta={class:"ai-result-column"},Ca={class:"ai-bot-header"},Na={class:"ai-reasoning"},Ua={class:"ai-section"},Aa={class:"ai-content"},Ra={class:"ai-section"},La={class:"ai-answer"},Ia={key:1,class:"main-content"},Ma={key:0},Ea={key:1},Pa=o({__name:"question_ai_test",props:{coursebaseid:{},jobbanksourceid:{},jobbankver:{},jobbankid:{}},setup(a){const l=a,t=d(l.coursebaseid),s=d(l.jobbanksourceid),i=d(l.jobbankver),n=d(l.jobbankid),o=d([]),S=d([]),z=d([]),T=d(!1),C=d(!1),Pa=d("题库渲染中，请稍后...");d(!1);const Da=d([]),Ha=async()=>{T.value=!0,C.value=!0;try{const a={course_base_id:t.value,jobbank_source_id:s.value,jobbank_ver:i.value,jobbank_id:n.value};e(a).then((e=>{o.value=e.data||[],S.value=e,o.value.length>0?((()=>{const e={};o.value.forEach((a=>{const l=a.questiontype;e[l]||(e[l]=[]),e[l].push({id:a.id})})),Da.value=e})(),setTimeout((()=>{C.value=!1,T.value=!1}),2e3)):(C.value=!1,T.value=!1)})).catch((e=>{T.value=!1}))}catch(a){V.error("题库获取失败")}finally{T.value=!1}};r((()=>{Ha()})),u((()=>l.coursebaseid),(e=>{t.value=e,Ha()})),c((()=>{const e={};let a=1;for(const l in Da.value)e[l]=a,a+=Da.value[l].length;return e}));const Oa=(()=>{const e={},a={};let l=!1;const t=async()=>{if(!l){for(l=!0;Object.values(e).some((e=>e.length>0));){for(const l in e)if(e[l].length>0){const{type:t,char:s,question:i}=e[l].shift();a[l][t]+=s,i.aiResult={...i.aiResult,[l.toLowerCase()]:{...a[l]}}}await new Promise((e=>setTimeout(e,20)))}l=!1}};return{push:(l,s,i,n)=>{e[l]||(e[l]=[]),a[l]||(a[l]={reasoning:"",answer:""});for(const a of i)e[l].push({type:s,char:a,question:n});t()}}})(),$a=async e=>{try{e.aiLoading=!0,e.aiResult=e.aiResult||{},await(async(e,a)=>{var l;Date.now();try{const t={title:e.title.title,options:e.title.options||[],optionNum:(null==(l=e.title.options)?void 0:l.length)||0,questionType:e.questiontype,bot_ids:a},s=await fetch("https://xczx3.swufe.edu.cn/cz_api/chat",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)});if(!s.body)throw new Error("No response body");const i=s.body.getReader(),n=new TextDecoder;return await async function(){for(;;){const{done:l,value:t}=await i.read();if(l)break;const s=n.decode(t,{stream:!0}).split("\n").filter((e=>e.startsWith("data: ")));for(const i of s)try{const a=i.replace("data: ","").trim();if("[DONE]"===a)continue;const l=JSON.parse(a),t=l.botName;if(!t)continue;"reasoning"===l.type&&l.content?Oa.push(t,"reasoning",l.content,e):"content"===l.type&&l.content?Oa.push(t,"answer",l.content,e):"error"===l.type&&V.error(`${t} AI测试出错: ${l.content}`)}catch(a){}}}(),e.aiResult}catch(t){const e=t instanceof Error?t.message:"未知错误";return V.error(`AI测试失败: ${e}`),null}})(e,["7516459709579739147","7516740396136022056"])}catch(a){const e=a instanceof Error?a.message:"未知错误";V.error("AI测试过程中出错: "+e)}finally{e.aiLoading=!1}};return(e,a)=>{const l=_("el-button"),t=_("el-tag"),s=_("v-md-preview"),i=_("el-radio"),n=_("el-radio-group"),d=_("el-checkbox"),r=_("el-checkbox-group"),u=p("loading");return g((v(),b("div",{class:"preview_bank_global","v-loading":T.value,"element-loading-text":Pa.value,"element-loading-spinner":"el-icon-loading","element-loading-svg-view-box":"-10, -10, 50, 50","element-loading-background":"rgba(122, 122, 122, 0.8)"},[h("div",U,[o.value.length>0?(v(),b("div",A,[(v(!0),b(w,null,m(o.value,((e,o)=>{var u,c,_,p,g,V,S,T,C;return v(),b("div",{key:o,class:"question-group"},[h("div",{class:"question-item","data-question-id":e.id},[h("div",L,[h("span",I,y(o+1)+"、",1),h("span",M,"【"+y(e.questiontype)+"】 ",1),h("span",E,"题编号： ( "+y(e.id)+" ) ",1),h("span",P,"题序号： "+y(e.sn),1),f(l,{type:"primary",size:"small",onClick:a=>$a(e),loading:e.aiLoading,disabled:!!e.aiResult,class:"ai-test-btn"},{default:x((()=>a[4]||(a[4]=[j(y("AI测试"))]))),_:2,__:[4]},1032,["onClick","loading","disabled"])]),h("div",D,[h("div",H,[f(t,{class:"el_tag_5"},{default:x((()=>a[5]||(a[5]=[j("题干")]))),_:1,__:[5]})]),h("div",O,[f(s,{text:e.title.title},null,8,["text"])])]),""!=e.score_explain&&null===e.score_explain&&void 0===e.score_explain?(v(),b("div",$,[h("div",B,[f(t,{class:"el_tag_5"},{default:x((()=>a[6]||(a[6]=[j("得分说明")]))),_:1,__:[6]})]),h("div",J,[f(s,{text:e.score_explain},null,8,["text"])])])):k("",!0),"options"in e.title?(v(),b("div",Q,[h("div",W,[e.title.questionType.includes("单")?(v(),b("div",F,[f(n,{modelValue:e.title.answer,"onUpdate:modelValue":a=>e.title.answer=a,disabled:""},{default:x((()=>[(v(!0),b(w,null,m(e.title.options,((e,a)=>(v(),q(i,{value:e.listNo,size:"large",key:a},{default:x((()=>[h("div",G,[h("span",K,y(e.listNo),1),f(s,{text:e.option},null,8,["text"])])])),_:2},1032,["value"])))),128))])),_:2},1032,["modelValue","onUpdate:modelValue"])])):e.title.questionType.includes("多")?(v(),b("div",Y,[h("div",X,y(z.value=e.title.answer?e.title.answer.split(","):[]),1),f(r,{modelValue:z.value,"onUpdate:modelValue":a[0]||(a[0]=e=>z.value=e),disabled:""},{default:x((()=>[(v(!0),b(w,null,m(e.title.options,((e,a)=>(v(),q(d,{value:e.listNo,size:"large",key:a},{default:x((()=>[h("div",Z,[h("span",ee,y(e.listNo),1),f(s,{text:e.option},null,8,["text"])])])),_:2},1032,["value"])))),128))])),_:2},1032,["modelValue"])])):(v(),b("div",ae,[h("div",le,y(z.value=e.title.answer?e.title.answer.split(","):[]),1),f(r,{modelValue:z.value,"onUpdate:modelValue":a[1]||(a[1]=e=>z.value=e),disabled:""},{default:x((()=>[(v(!0),b(w,null,m(e.title.options,((e,a)=>(v(),q(d,{value:e.listNo,size:"large",key:a},{default:x((()=>[h("div",te,[h("span",se,y(e.listNo),1),f(s,{text:e.option},null,8,["text"])])])),_:2},1032,["value"])))),128))])),_:2},1032,["modelValue"])]))]),h("div",ie,[h("div",ne,[f(t,{class:"el_tag_5"},{default:x((()=>a[7]||(a[7]=[j("选项答案")]))),_:1,__:[7]})]),h("div",oe,[f(s,{text:e.title.answer},null,8,["text"])])]),h("div",de,[h("div",re,[f(t,{class:"el_tag_5"},{default:x((()=>a[8]||(a[8]=[j("答案解析")]))),_:1,__:[8]})]),h("div",ue,[f(s,{text:e.title.answer_parsing},null,8,["text"])])])])):e.title.questionType.includes("判")&&(e.title.answer.includes("0")||e.title.answer.includes("1"))?(v(),b("div",ce,[f(n,{modelValue:e.title.answer,"onUpdate:modelValue":a=>e.title.answer=a,disabled:""},{default:x((()=>[f(i,{value:"1"},{default:x((()=>a[9]||(a[9]=[j("A、正确")]))),_:1,__:[9]}),f(i,{value:"0"},{default:x((()=>a[10]||(a[10]=[j("B、错误")]))),_:1,__:[10]})])),_:2},1032,["modelValue","onUpdate:modelValue"]),h("div",_e,[h("div",pe,[f(t,{class:"el_tag_5"},{default:x((()=>a[11]||(a[11]=[j("选项答案")]))),_:1,__:[11]})]),h("div",ge,[e.answer.includes("0")?(v(),b("span",be,"错误")):k("",!0),e.answer.includes("1")?(v(),b("span",ve,"正确")):(v(),b("span",he,y(e.answer),1))])]),h("div",we,[h("div",me,[f(t,{class:"el_tag_5"},{default:x((()=>a[12]||(a[12]=[j("答案解析")]))),_:1,__:[12]})]),h("div",ke,[f(s,{text:e.answer_parsing},null,8,["text"])])])])):"subQuestions"in e.title?(v(),b("div",fe,[(v(!0),b(w,null,m(e.title.subQuestions,((l,o)=>(v(),b("div",{key:o},[h("div",ye,[h("div",xe,[f(t,{class:"el_tag_5"},{default:x((()=>a[13]||(a[13]=[j("小题干")]))),_:1,__:[13]})]),h("div",je,[f(s,{text:l.title},null,8,["text"])])]),"options"in l?(v(),b("div",qe,[h("div",Ve,[l.questionType.includes("单")?(v(),b("div",Se,[f(n,{modelValue:l.answer,"onUpdate:modelValue":e=>l.answer=e,disabled:""},{default:x((()=>[(v(!0),b(w,null,m(l.options,((e,a)=>(v(),q(i,{value:e.listNo,size:"large",key:a},{default:x((()=>[h("div",ze,[h("span",Te,y(e.listNo),1),f(s,{text:e.option},null,8,["text"])])])),_:2},1032,["value"])))),128))])),_:2},1032,["modelValue","onUpdate:modelValue"])])):l.questionType.includes("多")?(v(),b("div",Ce,[h("div",Ne,y(z.value=l.answer?l.answer.split(","):[]),1),f(r,{modelValue:z.value,"onUpdate:modelValue":a[2]||(a[2]=e=>z.value=e),disabled:""},{default:x((()=>[(v(!0),b(w,null,m(l.options,((e,a)=>(v(),q(d,{value:e.listNo,size:"large",key:a},{default:x((()=>[h("div",Ue,[h("span",Ae,y(e.listNo),1),f(s,{text:e.option},null,8,["text"])])])),_:2},1032,["value"])))),128))])),_:2},1032,["modelValue"])])):(v(),b("div",Re,[h("div",Le,y(z.value=e.title.answer?e.title.answer.split(","):[]),1),f(r,{modelValue:z.value,"onUpdate:modelValue":a[3]||(a[3]=e=>z.value=e),disabled:""},{default:x((()=>[(v(!0),b(w,null,m(e.title.options,((e,a)=>(v(),q(d,{value:e.listNo,size:"large",key:a},{default:x((()=>[h("div",Ie,[h("span",Me,y(e.listNo),1),f(s,{text:e.option},null,8,["text"])])])),_:2},1032,["value"])))),128))])),_:2},1032,["modelValue"])]))]),h("div",Ee,[h("div",Pe,[f(t,{class:"el_tag_5"},{default:x((()=>a[14]||(a[14]=[j("选项答案")]))),_:1,__:[14]})]),h("div",De,[f(s,{text:l.answer},null,8,["text"])])]),h("div",He,[h("div",Oe,[f(t,{class:"el_tag_5"},{default:x((()=>a[15]||(a[15]=[j("答案解析")]))),_:1,__:[15]})]),h("div",$e,[f(s,{text:l.answer_parsing},null,8,["text"])])])])):l.questionType.includes("判")&&(l.answer.includes("0")||l.answer.includes("1"))?(v(),b("div",Be,[f(n,{modelValue:l.answer,"onUpdate:modelValue":e=>l.answer=e,disabled:""},{default:x((()=>[f(i,{value:"1"},{default:x((()=>a[16]||(a[16]=[j("A、正确")]))),_:1,__:[16]}),f(i,{value:"0"},{default:x((()=>a[17]||(a[17]=[j("B、错误")]))),_:1,__:[17]})])),_:2},1032,["modelValue","onUpdate:modelValue"]),h("div",Je,[h("div",Qe,[f(t,{class:"el_tag_5"},{default:x((()=>a[18]||(a[18]=[j("选项答案")]))),_:1,__:[18]})]),h("div",We,[l.answer.includes("0")?(v(),b("span",Fe,"错误")):k("",!0),l.answer.includes("1")?(v(),b("span",Ge,"正确")):(v(),b("span",Ke,y(l.answer),1))])]),h("div",Ye,[h("div",Xe,[f(t,{class:"el_tag_5"},{default:x((()=>a[19]||(a[19]=[j("答案解析")]))),_:1,__:[19]})]),h("div",Ze,[f(s,{text:l.answer_parsing},null,8,["text"])])])])):(v(),b("div",ea,[h("div",aa,[h("div",la,[f(t,{class:"el_tag_5"},{default:x((()=>a[20]||(a[20]=[j("答案")]))),_:1,__:[20]})]),h("div",ta,[f(s,{text:l.answer},null,8,["text"])])]),h("div",sa,[h("div",ia,[f(t,{class:"el_tag_5"},{default:x((()=>a[21]||(a[21]=[j("答案解析")]))),_:1,__:[21]})]),h("div",na,[f(s,{text:l.answer_parsing},null,8,["text"])])])]))])))),128))])):(v(),b("div",oa,[h("div",da,[h("div",ra,[f(t,{class:"el_tag_5"},{default:x((()=>a[22]||(a[22]=[j("答案")]))),_:1,__:[22]})]),h("div",ua,[f(s,{text:e.answer},null,8,["text"])])]),h("div",ca,[h("div",_a,[f(t,{class:"el_tag_5"},{default:x((()=>a[23]||(a[23]=[j("答案解析")]))),_:1,__:[23]})]),h("div",pa,[f(s,{text:e.answer_parsing},null,8,["text"])])])])),h("div",ga,[f(t,{class:"el_tag_5"},{default:x((()=>a[24]||(a[24]=[j("知识点")]))),_:1,__:[24]}),h("span",null,y(e.knowledge_point),1)]),h("div",ba,[f(t,{class:"el_tag_5"},{default:x((()=>a[25]||(a[25]=[j("试题来源")]))),_:1,__:[25]}),h("span",null,y(e.jobbank_source_title),1)]),h("div",va,[f(t,{class:"el_tag_5"},{default:x((()=>a[26]||(a[26]=[j("试题能力层次")]))),_:1,__:[26]}),h("span",null,y(e.competence_level),1)]),h("div",ha,[f(t,{class:"el_tag_5"},{default:x((()=>a[27]||(a[27]=[j("难度能力层次")]))),_:1,__:[27]}),h("span",null,y(e.difficulty_level),1)]),h("div",wa,[f(t,{class:"el_tag_5"},{default:x((()=>a[28]||(a[28]=[j("版本(套题)")]))),_:1,__:[28]}),h("span",null,y(e.ver),1)]),(C=e,C.aiResult&&(C.aiResult.doubao||C.aiResult.deepseek)?(v(),b("div",ma,[h("div",ka,[f(t,{type:"success",effect:"dark",size:"small"},{default:x((()=>a[29]||(a[29]=[j("AI 解析")]))),_:1,__:[29]})]),h("div",fa,[h("div",ya,[h("div",xa,[f(t,{type:"success",size:"small",effect:"dark"},{default:x((()=>a[30]||(a[30]=[j("豆包")]))),_:1,__:[30]})]),h("div",ja,[h("div",qa,[a[31]||(a[31]=h("div",{class:"ai-label"},"思考过程：",-1)),h("div",Va,y((null==(c=null==(u=e.aiResult)?void 0:u.doubao)?void 0:c.reasoning)||"无"),1)]),h("div",Sa,[a[32]||(a[32]=h("div",{class:"ai-label"},"最终答案：",-1)),h("div",za,y((null==(p=null==(_=e.aiResult)?void 0:_.doubao)?void 0:p.answer)||"无"),1)])])]),h("div",Ta,[h("div",Ca,[f(t,{type:"primary",size:"small",effect:"dark"},{default:x((()=>a[33]||(a[33]=[j("Deepseek")]))),_:1,__:[33]})]),h("div",Na,[h("div",Ua,[a[34]||(a[34]=h("div",{class:"ai-label"},"思考过程：",-1)),h("div",Aa,y((null==(V=null==(g=e.aiResult)?void 0:g.deepseek)?void 0:V.reasoning)||"无"),1)]),h("div",Ra,[a[35]||(a[35]=h("div",{class:"ai-label"},"最终答案：",-1)),h("div",La,y((null==(T=null==(S=e.aiResult)?void 0:S.deepseek)?void 0:T.answer)||"无"),1)])])])])])):k("",!0))],8,R)])})),128))])):(v(),b("div",Ia,[200==S.value.code?(v(),b("span",Ma,"所选套题，无题库信息！！！请确认")):(v(),b("span",Ea,y(S.value.msg),1))]))])],8,N)),[[u,C.value]])}}}),Da={class:"questionManage page"},Ha={style:{float:"left"}},Oa={style:{float:"right",color:"#67C23A","font-size":"13px"}},$a={style:{float:"left"}},Ba={style:{float:"right",color:"#67C23A","font-size":"13px"}},Ja={style:{"margin-top":"10px"}},Qa={class:"body"},Wa={style:{"margin-top":"16px","text-align":"right"}},Fa=["element-loading-text","element-loading-spinner"],Ga=["element-loading-text","element-loading-spinner"],Ka=["element-loading-text","element-loading-spinner"],Ya=o({__name:"question-manage",setup(e){S();const o=z({loading:!1,tableHeight:window.innerHeight-240,keySearch:"",course_base_id:3,jobbank_source_id:"",job_ver:"",dataTable:[],total:0,pageSize:30,currentPage:1}),u=d([]),c=d([]),N=d([]),U=d([]),A=z({dialogShow:!1,title:"AI 测试",loading:!1,loadingText:"初始化中，请稍后...",loadingSvg:'\n    <path class="path" d="\n      M 30 15\n      L 28 17\n      M 25.61 25.61\n      A 15 15, 0, 0, 1, 15 30\n      A 15 15, 0, 1, 1, 27.99 7.5\n      L 15 15\n    " style="stroke-width: 4px; fill: rgba(0, 0, 0, 0)"/>\n  ',course_base_id:"",jobbank_source_id:"",jobbank_ver:"",jobbank_id:""}),R=z({dialogShow:!1,title:"",loading:!1,loadingText:"获取数据中，请稍后...",loadingSvg:'\n        <path class="path" d="\n          M 30 15\n          L 28 17\n          M 25.61 25.61\n          A 15 15, 0, 0, 1, 15 30\n          A 15 15, 0, 1, 1, 27.99 7.5\n          L 15 15\n        " style="stroke-width: 4px; fill: rgba(0, 0, 0, 0)"/>\n      ',course_base_id:"",jobbank_source_id:"",jobbank_ver:"",jobbank_id:""}),L=z({dialogShow:!1,title:"",loading:!1,loadingText:"获取数据中，请稍后...",loadingSvg:'\n        <path class="path" d="\n          M 30 15\n          L 28 17\n          M 25.61 25.61\n          A 15 15, 0, 0, 1, 15 30\n          A 15 15, 0, 1, 1, 27.99 7.5\n          L 15 15\n        " style="stroke-width: 4px; fill: rgba(0, 0, 0, 0)"/>\n      ',jobbankid:0,data:[]}),I=()=>{E(),M()},M=()=>{const e={course_base_id:o.course_base_id,jobbank_source_id:void 0===o.jobbank_source_id?"":o.jobbank_source_id};t(e).then((e=>{200==e.code?U.value=e.data:V.error(e.msg)}))},E=()=>{o.loading=!0;const e={keySearch:o.keySearch,course_base_id:o.course_base_id,jobbank_source_id:void 0===o.jobbank_source_id?"":o.jobbank_source_id,ver:void 0===o.job_ver?"":o.job_ver,currentPage:o.currentPage,pageSize:o.pageSize};s(e).then((e=>{o.dataTable=e.data||[],o.total=e.total||o.dataTable.length,o.loading=!1})).catch((e=>{o.loading=!1}))},P=e=>{if("all"===e){if(!o.course_base_id)return void V.warning("请选择课程编码");if(""===o.jobbank_source_id)return void V.warning("请选择题库来源");if(""===o.job_ver)return void V.warning("请选择版本");R.title="预览套题",R.course_base_id=o.course_base_id,R.jobbank_source_id=o.jobbank_source_id,R.jobbank_ver=o.job_ver,R.jobbank_id=""}else R.title="预览题目",R.course_base_id=o.course_base_id,R.jobbank_source_id=o.jobbank_source_id,R.jobbank_ver=o.job_ver,R.jobbank_id=e.id;R.dialogShow=!0},D=e=>{u.value=e},H=e=>{o.pageSize=e,E()},O=e=>{o.currentPage=e,E()},$=()=>{o.tableHeight=window.innerHeight-240};return r((()=>{l({}).then((e=>{200==e.code?c.value=e.data:V.error(e.msg)})),a({}).then((e=>{200==e.code?N.value=e.data:V.error(e.msg)})),I(),$(),window.addEventListener("resize",$)})),T((()=>{window.removeEventListener("resize",$)})),(e,a)=>{const l=_("el-tag"),t=_("el-option"),s=_("el-select"),d=_("el-button"),r=_("el-input"),S=_("el-table-column"),z=_("el-link"),T=_("el-table"),M=_("el-pagination"),$=_("el-dialog"),B=p("loading");return v(),b("div",Da,[h("div",null,[h("div",null,[f(l,{class:"el_tag_5"},{default:x((()=>a[9]||(a[9]=[j("课程编码")]))),_:1,__:[9]}),f(s,{modelValue:o.course_base_id,"onUpdate:modelValue":a[0]||(a[0]=e=>o.course_base_id=e),clearable:"",filterable:"",style:{width:"237px",margin:"0 10px"},placeholder:"课程编码",onChange:I},{default:x((()=>[(v(!0),b(w,null,m(c.value,(e=>(v(),q(t,{key:e.id,label:"[ "+e.kc_bm+" ] "+e.kc_mc,value:e.id},{default:x((()=>[h("span",Ha,[j(y(e.kc_mc)+" ",1),h("span",null,y("("+e.id+")"),1)]),h("span",Oa,y(e.kc_bm),1)])),_:2},1032,["label","value"])))),128))])),_:1},8,["modelValue"]),f(l,{class:"el_tag_5"},{default:x((()=>a[10]||(a[10]=[j("试题来源")]))),_:1,__:[10]}),f(s,{modelValue:o.jobbank_source_id,"onUpdate:modelValue":a[1]||(a[1]=e=>o.jobbank_source_id=e),clearable:"",filterable:"",style:{width:"160px",margin:"0 10px"},placeholder:"试题来源",onChange:I},{default:x((()=>[(v(!0),b(w,null,m(N.value,(e=>(v(),q(t,{key:e.id,label:e.title,value:e.id},{default:x((()=>[h("span",$a,y(e.id),1),h("span",Ba,y(e.title),1)])),_:2},1032,["label","value"])))),128))])),_:1},8,["modelValue"]),f(l,{class:"el_tag_5"},{default:x((()=>a[11]||(a[11]=[j("版本(套题)")]))),_:1,__:[11]}),f(s,{modelValue:o.job_ver,"onUpdate:modelValue":a[2]||(a[2]=e=>o.job_ver=e),clearable:"",filterable:"",style:{width:"120px",margin:"0 10px"},placeholder:"版本",onChange:E},{default:x((()=>[(v(!0),b(w,null,m(U.value,(e=>(v(),q(t,{key:e.ver,label:e.ver,value:e.ver},null,8,["label","value"])))),128))])),_:1},8,["modelValue"]),f(d,{type:"success",plain:"",size:"default",round:"",icon:"View",onClick:a[3]||(a[3]=e=>P("all"))},{default:x((()=>a[12]||(a[12]=[j("预览套题")]))),_:1,__:[12]})]),h("div",Ja,[f(d,{type:"success",plain:"",size:"default",icon:"refresh",round:"",onClick:E}),f(r,{modelValue:o.keySearch,"onUpdate:modelValue":a[4]||(a[4]=e=>o.keySearch=e),placeholder:"试题编号ID",size:"default",style:{width:"260px",margin:"0 10px"},clearable:"",onChange:E},{append:x((()=>[f(d,{icon:"Search",onClick:E})])),_:1},8,["modelValue"]),f(d,{type:"primary",plain:"",size:"default",round:"",icon:"edit",onClick:a[5]||(a[5]=e=>(()=>{if(1!==u.value.length)return V({message:"请勾选一条数据进行编辑！",type:"error"}),!1;L.jobbankid=u.value[0].id,L.dialogShow=!0})())},{default:x((()=>a[13]||(a[13]=[j("编辑")]))),_:1,__:[13]})])]),h("div",Qa,[g((v(),q(T,{data:o.dataTable,border:"",class:"modTable",height:o.tableHeight,style:{width:"100%"},onSelectionChange:D},{default:x((()=>[f(S,{prop:"",type:"index",label:"序号",align:"center",width:"60","header-align":"center"}),f(S,{type:"selection",width:"50",align:"center"}),f(S,{prop:"id",label:"编码(ID)","min-width":"80",align:"center","header-align":"center","show-overflow-tooltip":""}),f(S,{prop:"questiontype",label:"题型","min-width":"120",align:"center","header-align":"center","show-overflow-tooltip":""}),f(S,{prop:"question_title",label:"试题内容","min-width":"200",align:"left","header-align":"center","show-overflow-tooltip":""}),f(S,{prop:"answer",label:"答案","min-width":"200",align:"left","header-align":"center","show-overflow-tooltip":""}),f(S,{label:"操作","min-width":"80",align:"center","header-align":"center"},{default:x((e=>[f(z,{type:"primary",onClick:a=>P(e.row)},{default:x((()=>a[14]||(a[14]=[j("预览")]))),_:2,__:[14]},1032,["onClick"]),f(z,{type:"primary",onClick:a=>{var l;(l=e.row)?(A.title="AI 测试 - "+l.questiontype,A.course_base_id=o.course_base_id,A.jobbank_source_id=o.jobbank_source_id,A.jobbank_ver=o.job_ver,A.jobbank_id=l.id,A.dialogShow=!0):V.warning("请选择要测试的题目")}},{default:x((()=>a[15]||(a[15]=[j("AI测试")]))),_:2,__:[15]},1032,["onClick"])])),_:1}),f(S,{prop:"sn",label:"题序","min-width":"80",align:"center","header-align":"center","show-overflow-tooltip":""}),f(S,{prop:"answer_parsing",label:"答案解析","min-width":"200",align:"left","header-align":"center","show-overflow-tooltip":""}),f(S,{prop:"knowledge_point",label:"知识点","min-width":"120",align:"left","header-align":"center","show-overflow-tooltip":""}),f(S,{prop:"jobbank_source_title",label:"试题来源","min-width":"120",align:"left","header-align":"center","show-overflow-tooltip":""}),f(S,{prop:"difficulty_level",label:"难易度","min-width":"120",align:"center","header-align":"center","show-overflow-tooltip":""}),f(S,{prop:"chapter_mc",label:"章节","min-width":"120",align:"left","header-align":"center","show-overflow-tooltip":""}),f(S,{prop:"is_autoscore",label:"题类","min-width":"80",align:"center","header-align":"center"},{default:x((e=>[h("span",{style:C({color:1===e.row.is_autoscore?"red":"#222"})},y(1===e.row.is_autoscore?"客观题":"主观题"),5)])),_:1}),f(S,{prop:"status",label:"状态","min-width":"80",align:"center","header-align":"center"},{default:x((e=>[h("span",{style:C({color:1===e.row.status?"green":"red"})},y(1===e.row.status?"启用":"禁用"),5)])),_:1}),f(S,{prop:"in_markdown_section",label:"markdown","min-width":"80",align:"center","header-align":"center"},{default:x((e=>[h("span",{style:C({color:1===e.row.in_markdown_section?"red":"#222"})},y(1===e.row.in_markdown_section?"是":"否"),5)])),_:1}),f(S,{prop:"created_user",label:"创建人","min-width":"80",align:"center","header-align":"center","show-overflow-tooltip":""}),f(S,{prop:"created_date",label:"创建时间","min-width":"140",align:"center","header-align":"center","show-overflow-tooltip":""}),f(S,{prop:"modify_user_name",label:"修改人","min-width":"80",align:"center","header-align":"center","show-overflow-tooltip":""}),f(S,{prop:"modify_date",label:"修改时间","min-width":"140",align:"center","header-align":"center","show-overflow-tooltip":""})])),_:1},8,["data","height"])),[[B,o.loading]]),h("div",Wa,[f(M,{background:"",layout:"total, sizes, prev, pager, next, jumper",total:o.total,"page-size":o.pageSize,"current-page":o.currentPage,"page-sizes":[20,50,100,500,o.total],onSizeChange:H,onCurrentChange:O},null,8,["total","page-size","current-page","page-sizes"])])]),R.dialogShow?(v(),q($,{key:0,modelValue:R.dialogShow,"onUpdate:modelValue":a[6]||(a[6]=e=>R.dialogShow=e),"align-center":"",draggable:"","show-close":!0,fullscreen:!0,"close-on-click-modal":!1,"close-on-press-escape":!1},{default:x((()=>[g((v(),b("div",{class:"dialog_content","element-loading-text":R.loadingText,"element-loading-spinner":R.loadingSvg,"element-loading-svg-view-box":"-10, -10, 50, 50","element-loading-background":"rgba(122, 122, 122, 0.8)"},[f(i,{coursebaseid:R.course_base_id,jobbanksourceid:R.jobbank_source_id,jobbankver:R.jobbank_ver,jobbankid:R.jobbank_id},null,8,["coursebaseid","jobbanksourceid","jobbankver","jobbankid"])],8,Fa)),[[B,R.loading]])])),_:1},8,["modelValue"])):k("",!0),L.dialogShow?(v(),q($,{key:1,modelValue:L.dialogShow,"onUpdate:modelValue":a[7]||(a[7]=e=>L.dialogShow=e),"align-center":"",draggable:"","show-close":!0,fullscreen:!0,"close-on-click-modal":!1,"close-on-press-escape":!1},{default:x((()=>[g((v(),b("div",{class:"dialog_content","element-loading-text":L.loadingText,"element-loading-spinner":L.loadingSvg,"element-loading-svg-view-box":"-10, -10, 50, 50","element-loading-background":"rgba(122, 122, 122, 0.8)"},[f(n,{jobbankid:L.jobbankid},null,8,["jobbankid"])],8,Ga)),[[B,L.loading]])])),_:1},8,["modelValue"])):k("",!0),A.dialogShow?(v(),q($,{key:2,modelValue:A.dialogShow,"onUpdate:modelValue":a[8]||(a[8]=e=>A.dialogShow=e),"align-center":"",draggable:"",title:A.title,"show-close":!0,fullscreen:!0,"close-on-click-modal":!1,"close-on-press-escape":!1},{default:x((()=>[g((v(),b("div",{class:"dialog_content","element-loading-text":A.loadingText,"element-loading-spinner":A.loadingSvg,"element-loading-svg-view-box":"-10, -10, 50, 50","element-loading-background":"rgba(122, 122, 122, 0.8)"},[f(Pa,{coursebaseid:A.course_base_id,jobbanksourceid:A.jobbank_source_id,jobbankver:A.jobbank_ver,jobbankid:A.jobbank_id},null,8,["coursebaseid","jobbanksourceid","jobbankver","jobbankid"])],8,Ka)),[[B,A.loading]])])),_:1},8,["modelValue","title"])):k("",!0)])}}});export{Ya as default};
