import{f as e,h as a,j as l,k as t}from"./course-DUm3UmHR.js";import{d as n,o as i,r as o,a as d,L as u,c as r,b as s,j as p,M as c,w as v,p as h,h as y,e as f,F as m,f as b,B as g,m as x,t as _,z as k,E as w,v as j}from"./index-BERmxY3Y.js";const V={class:"chapter-homework"},C={style:{width:"100%",height:"calc(100vh - 80px)",display:"flex",gap:"10px"}},O={style:{width:"300px",height:"100%","overflow-y":"auto"}},E={style:{"margin-top":"15px"}},U=["onClick"],S={key:0,style:{"font-size":"18px","margin-top":"5px"}},J={style:{width:"calc(100% - 300px)",height:"100%","overflow-y":"auto"}},N={style:{padding:"5px 0px",display:"flex","justify-content":"space-between","align-items":"center"}},D={style:{display:"flex","align-items":"center",gap:"10px"}},z={style:{display:"flex"}},B={style:{height:"80vh","overflow-y":"auto"}},H={style:{width:"100%","text-align":"center",display:"flex","align-items":"center","justify-content":"space-between"}},T={style:{"margin-top":"15px"}},q={class:"questiontype_item"},F={style:{"max-height":"100px","overflow-y":"auto","border-bottom":"1px solid #E4E7ED",flex:"1"}},K={style:{"margin-bottom":"5px"}},L={style:{padding:"5px 0px"}},M=n({__name:"chapter_homework",setup(n){i((()=>{ie()}));const M=o({}),R=window.innerHeight-200,W=o([]),A=o([]),G=o(!1),I=o(""),P=o(""),Q=o("ols"),X=o(0),Y=o({}),Z=o(!1),$=o(!1),ee=o(!0),ae=o([{children:[{children:[{jobbank_id:0}]}]}]),le=o({}),te=o({}),ne=o(!1),ie=()=>{ne.value=!0,e({}).then((e=>{W.value=e.data})).catch((e=>{})).finally((()=>{ne.value=!1}))},oe=e=>{$.value=!0,P.value=e,a({course_info_id:e}).then((e=>{var a=e.data.filter((e=>0==e.pid));A.value=a.sort(((e,a)=>e.sn-a.sn)),de(I.value?I.value:A.value[0].id,A.value[0])})).catch((e=>{})).finally((()=>{$.value=!1}))},de=(e,a)=>{I.value=e,te.value=a,ue()},ue=()=>{Z.value=!0,l({course_info_id:P.value,chapter_id:I.value,source:Q.value}).then((e=>{ae.value=e.data.listData;var a=e.data.data?e.data.data[0].jobbank_ids:[];ae.value.forEach(((e,l)=>{Y.value["index"+l]=-1,M.value["index"+l]=[];var t=[];a.forEach((a=>{e.answerData.indexOf(a.id)>-1&&(t.push(a.id),M.value["index"+l].push(a.id))}))})),le.value=JSON.parse(JSON.stringify(M.value))})).catch((e=>{})).finally((()=>{Z.value=!1}))},re=e=>{},se=e=>{pe(le.value,M.value)?e():j.confirm("数据已修改，还未保存!是否离开？","Warning",{confirmButtonText:"OK",cancelButtonText:"Cancel",type:"warning"}).then((()=>{e()})).catch((()=>{}))},pe=(e,a)=>{if(e===a)return!0;if("object"!=typeof e||null===e||"object"!=typeof a||null===a)return!1;const l=Object.keys(e),t=Object.keys(a);if(l.length!==t.length)return!1;var n=0;return l.forEach((l=>{JSON.stringify(e[l])!=JSON.stringify(a[l])&&n++})),0===n};return(e,a)=>{const l=d("el-button"),n=d("el-table-column"),i=d("el-table"),o=d("el-badge"),j=d("CircleCheckFilled"),le=d("el-icon"),pe=d("Refresh"),ce=d("el-radio"),ve=d("el-radio-group"),he=d("el-checkbox"),ye=d("el-checkbox-group"),fe=d("el-tab-pane"),me=d("el-tabs"),be=d("el-dialog"),ge=u("loading");return s(),r("div",V,[p(l,{type:"primary",onClick:ie},{default:v((()=>a[5]||(a[5]=[h("刷新")]))),_:1,__:[5]}),c((s(),y(i,{data:W.value,Height:R,stripe:"",style:{width:"100%","margin-top":"5px"}},{default:v((()=>[p(n,{type:"index",label:"#",align:"center",width:"60"}),p(n,{prop:"kc_bm",label:"课程编码",width:"180",align:"center"}),p(n,{prop:"kc_mc",label:"课程名称",width:"180",align:"center"}),p(n,{prop:"chapter_num",width:"100",label:"章节数",align:"center"}),p(n,{prop:"jobbank_count",width:"100",label:"章节作业数",align:"center"}),p(n,{prop:"chapter_num",label:"",align:"center"},{default:v((e=>[p(l,{type:"primary",onClick:a=>{return l=e.row.course_info_id,oe(l),void(G.value=!0);var l}},{default:v((()=>a[6]||(a[6]=[h("添加章节作业")]))),_:2,__:[6]},1032,["onClick"])])),_:1})])),_:1},8,["data"])),[[ge,ne.value]]),p(be,{modelValue:G.value,"onUpdate:modelValue":a[4]||(a[4]=e=>G.value=e),title:"指定章节作业",fullscreen:!0,"before-close":se},{default:v((()=>[f("div",C,[c((s(),r("div",O,[a[7]||(a[7]=f("div",{style:{color:"#909399","font-weight":"bold","font-size":"17px"}},"章节信息",-1)),f("div",E,[(s(!0),r(m,null,b(A.value,((e,a)=>(s(),r("div",{class:g("chapter-item "+(I.value==e.id?"chapter-item-active":"")),key:a,onClick:a=>de(e.id,e)},[f("span",null,[h(" 第 "+_(a+1)+" 章."+_(e.chapter_name)+" ",1),e.jobbank_count?(s(),y(o,{key:0,value:e.jobbank_count,type:"primary"},null,8,["value"])):x("",!0)]),I.value==e.id?(s(),r("span",S,[p(le,null,{default:v((()=>[p(j)])),_:1})])):x("",!0)],10,U)))),128))])])),[[ge,$.value]]),c((s(),r("div",J,[f("div",N,[f("div",D,[p(l,{type:"primary",onClick:a[0]||(a[0]=e=>ue())},{default:v((()=>[p(le,null,{default:v((()=>[p(pe)])),_:1})])),_:1}),a[10]||(a[10]=h(" 题库来源： ")),p(ve,{modelValue:Q.value,"onUpdate:modelValue":a[1]||(a[1]=e=>Q.value=e),onChange:ue},{default:v((()=>[p(ce,{label:"ols",border:""},{default:v((()=>a[8]||(a[8]=[h("在线章节")]))),_:1,__:[8]}),p(ce,{label:"jobbank",border:""},{default:v((()=>a[9]||(a[9]=[h("题库")]))),_:1,__:[9]})])),_:1},8,["modelValue"])]),f("div",null,[p(l,{type:"primary",onClick:a[2]||(a[2]=e=>(()=>{var e=[];ae.value.forEach(((a,l)=>{M.value["index"+l].length>0&&e.push(...M.value["index"+l])}));var a={chapter_id:I.value,jobbankids:JSON.stringify(e)};t(a).then((e=>{e.data>0?(w.success("保存成功"),ue(),oe(P.value)):w.error("保存失败")})).catch((e=>{w.error("保存失败")}))})())},{default:v((()=>[h("保存《"+_(te.value.chapter_name)+"》作业配置",1)])),_:1})])]),ae.value[0].chapter_name?(s(),y(me,{key:0,modelValue:X.value,"onUpdate:modelValue":a[3]||(a[3]=e=>X.value=e),type:"border-card",class:"demo-tabs"},{default:v((()=>[(s(!0),r(m,null,b(ae.value,((e,l)=>(s(),y(fe,{label:"第"+(l+1)+"章",name:l},{label:v((()=>[f("div",z,[h(" 第"+_(l+1)+"章   ",1),M.value["index"+l].length>0?(s(),y(o,{key:0,value:M.value["index"+l].length,style:{"margin-top":"1px"}},null,8,["value"])):x("",!0)])])),default:v((()=>[f("div",B,[f("div",H,[f("span",null,_(e.chapter_name),1),f("span",null,[p(he,{modelValue:Y.value["index"+l],"onUpdate:modelValue":e=>Y.value["index"+l]=e,indeterminate:ee.value,onChange:e=>((e,a)=>{M.value["index"+a]=e?ae.value[a].answerData:[],ee.value=!1})(e,l)},{default:v((()=>a[11]||(a[11]=[h("选择全部")]))),_:2,__:[11]},1032,["modelValue","onUpdate:modelValue","indeterminate","onChange"])])]),(s(!0),r(m,null,b(e.children,((e,a)=>(s(),r("div",T,[f("div",q,_(a+1)+"、"+_(e.questionType),1),(s(!0),r(m,null,b(e.children,((e,a)=>(s(),r("div",{key:a,style:{padding:"10px 5px",display:"flex","align-items":"center",gap:"15px"}},[f("div",null,[p(ye,{modelValue:M.value["index"+l],"onUpdate:modelValue":e=>M.value["index"+l]=e,onChange:re},{default:v((()=>[p(he,{label:"选择",value:e.jobbank_id,border:""},null,8,["value"])])),_:2},1032,["modelValue","onUpdate:modelValue"])]),f("div",F,[f("div",K,_(a+1)+"."+_(e.title.title),1),f("div",L,[e.title.options?(s(!0),r(m,{key:0},b(e.title.options,((e,a)=>(s(),r("span",{key:a,style:{padding:"0px 15px"}},[f("span",{style:k("color:"+(e.answer?"green":"#333"))},_(String.fromCharCode(65+a))+"."+_(e.option),5)])))),128)):x("",!0)])])])))),128))])))),256))])])),_:2},1032,["label","name"])))),256))])),_:1},8,["modelValue"])):x("",!0)])),[[ge,Z.value]])])])),_:1},8,["modelValue"])])}}});export{M as default};
