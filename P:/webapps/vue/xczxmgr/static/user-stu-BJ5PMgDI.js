import{I as e,J as a,d as t,K as l,r as n,o as i,a as r,L as o,c as s,b as d,e as c,j as p,w as g,p as u,F as h,f as _,h as m,t as y,M as f,E as w,v as b}from"./index-BERmxY3Y.js";async function v(t){return await e.post("users/sync_wj?"+a.stringify(t))}async function z(t){return await e.post("users/sync_cj?"+a.stringify(t))}const x={class:"userManage page"},S={class:"header"},k={style:{float:"left"}},C={style:{float:"right",color:"#67C23A","font-size":"13px"}},T={class:"body"},j={style:{"margin-top":"16px","text-align":"right"}},V=t({__name:"user-stu",setup(t){const V=l({loading:!1,tableHeight:window.innerHeight-220,keySearch:"",user_type_id:2,dataTable:[],total:0,pageSize:30,currentPage:1}),P=n([]),U=n([]),$=n(!1),H=()=>{V.loading=!0,async function(t){return await e.post("mgr_basic_data/getUserTypeData?"+a.stringify(t))}({}).then((e=>{P.value=e.data,V.user_type_id=e.data.length>0?e.data[0].id:"",B()})).catch((e=>{w({type:"error",message:"出错了!"+e})}))},B=()=>{V.loading=!0;(async function(t){return await e.post("mgr_basic_data/getUserStuData?"+a.stringify(t))})({keySearch:V.keySearch,user_type_id:V.user_type_id,currentPage:V.currentPage,pageSize:V.pageSize}).then((e=>{V.dataTable=e.data||[],V.total=e.total||V.dataTable.length,V.loading=!1})).catch((e=>{V.loading=!1}))},D=e=>{U.value=e},M=e=>{V.pageSize=e,B()},A=e=>{V.currentPage=e,B()},E=async()=>{if(!V.user_type_id)return void w.warning("请先选择用户类型");let e,a="";const t=P.value.find((e=>e.id===V.user_type_id));if(t&&(a=t.title),5===V.user_type_id)e=z;else{if(2!==V.user_type_id)return void w.warning("当前用户类型不支持同步数据功能");e=v}try{await b.confirm(`确定要同步 "${a}" 的数据吗？此操作可能需要一些时间。`,"确认同步",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),$.value=!0;const t=await e({});200===t.code?(w.success(`${a} 数据同步成功！`),B(),H()):w.error(`同步失败：${t.message||"未知错误"}`)}catch(l){"cancel"!==l&&w.error(`同步失败：${l.message||"网络错误"}`)}finally{$.value=!1}};return i((()=>{H()})),(e,a)=>{const t=r("el-button"),l=r("el-input"),n=r("el-tag"),i=r("el-option"),w=r("el-select"),b=r("el-table-column"),v=r("el-table"),z=r("el-pagination"),U=o("loading");return d(),s("div",x,[c("div",S,[p(t,{type:"success",plain:"",size:"default",icon:"refresh",round:"",onClick:B}),p(t,{type:"primary",plain:"",size:"default",icon:"download",round:"",onClick:E,loading:$.value},{default:g((()=>a[2]||(a[2]=[u("同步数据")]))),_:1,__:[2]},8,["loading"]),p(l,{modelValue:V.keySearch,"onUpdate:modelValue":a[0]||(a[0]=e=>V.keySearch=e),placeholder:"用户名|姓名|身份证|手机号",size:"default",style:{width:"260px",margin:"0 10px"},clearable:"",onChange:B},{append:g((()=>[p(t,{icon:"Search",onClick:B})])),_:1},8,["modelValue"]),p(n,{class:"el_tag_5"},{default:g((()=>a[3]||(a[3]=[u("用户类型")]))),_:1,__:[3]}),p(w,{modelValue:V.user_type_id,"onUpdate:modelValue":a[1]||(a[1]=e=>V.user_type_id=e),clearable:"",filterable:"",style:{width:"160px"},placeholder:"用户类型",onChange:B},{default:g((()=>[(d(!0),s(h,null,_(P.value,(e=>(d(),m(i,{key:e.id,label:e.title,value:e.id,style:{width:"260px"}},{default:g((()=>[c("span",k,y(e.title),1),c("span",C,y(e.num),1)])),_:2},1032,["label","value"])))),128))])),_:1},8,["modelValue"])]),c("div",T,[f((d(),m(v,{data:V.dataTable,border:"",class:"modTable",height:V.tableHeight,style:{width:"100%"},onSelectionChange:D},{default:g((()=>[p(b,{prop:"user_type_title",label:"用户类型","min-width":"100",align:"center","header-align":"center","show-overflow-tooltip":""}),p(b,{prop:"xs_bm",label:"学号","min-width":"120",align:"center","show-overflow-tooltip":""}),p(b,{prop:"xs_bmh",label:"报名号","min-width":"120",align:"left","header-align":"center"}),p(b,{prop:"xs_xm",label:"姓名","min-width":"120",align:"center","header-align":"center","show-overflow-tooltip":""}),p(b,{prop:"xb_mc",label:"性别","min-width":"100",align:"center","header-align":"center"}),p(b,{prop:"yddh",label:"手机号","min-width":"100",align:"center","header-align":"center"}),p(b,{prop:"xs_sfzh",label:"身份证号","min-width":"140",align:"center","header-align":"center"}),p(b,{prop:"xjzt_mc",label:"学籍状态","min-width":"100",align:"center","header-align":"center"}),p(b,{prop:"pc_bm",label:"批次","min-width":"100",align:"center","header-align":"center"}),p(b,{prop:"zy_bm",label:"专业编码","min-width":"100",align:"center","header-align":"center"}),p(b,{prop:"zy_mc",label:"专业名称","min-width":"100",align:"center","header-align":"center"}),p(b,{prop:"xlcc_mc",label:"层次","min-width":"100",align:"center","header-align":"center"}),p(b,{prop:"fee_flag_s",label:"交费情况","min-width":"100",align:"left","header-align":"center","show-overflow-tooltip":""}),p(b,{prop:"zd_bm",label:"站点编码","min-width":"80",align:"center","header-align":"center"}),p(b,{prop:"zd_mc",label:"站点名称","min-width":"140",align:"left","header-align":"center","show-overflow-tooltip":""})])),_:1},8,["data","height"])),[[U,V.loading]]),c("div",j,[p(z,{background:"",layout:"total, sizes, prev, pager, next, jumper",total:V.total,"page-size":V.pageSize,"current-page":V.currentPage,"page-sizes":[20,50,100,500,V.total],onSizeChange:M,onCurrentChange:A},null,8,["total","page-size","current-page","page-sizes"])])])])}}});export{V as default};
