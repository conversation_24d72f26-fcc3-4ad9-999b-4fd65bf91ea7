import{g as e,i as l}from"./course-DUm3UmHR.js";import{G as t}from"./question-DWIQ7W1h.js";import{m as a}from"./message-Df6PNwXY.js";import{d as o,r as s,l as n,o as i,E as d,n as u,a as r,c as p,b as m,e as c,j as v,h as g,m as h,F as x,f,t as y,p as w,w as V,q as _,v as b}from"./index-BERmxY3Y.js";import{g as q}from"./exam-jF1-Sa8S.js";import{l as k}from"./loading-Bz4tfNWn.js";const U={style:{display:"flex","justify-content":"center"}},S={style:{width:"800px",padding:"0px 0px 0px 0px",height:"calc(100vh - 50px)","overflow-y":"auto"}},N=["src"],j={style:{width:"800px",padding:"0px 0px 100px 0px",height:"calc(100vh - 50px)","overflow-y":"auto"}},O={class:"question-title",style:{"margin-top":"15px"}},z={key:0},F={key:0,class:"question-content",style:{"background-color":"antiquewhite",padding:"10px"}},T={class:"question-answer",style:{"margin-top":"15px"}},A={key:1},D={key:0,class:"question-content",style:{"background-color":"antiquewhite",padding:"10px"}},E={class:"question-answer",style:{"margin-top":"15px"}},L={class:"question-answer",style:{"margin-top":"15px"}},R={style:{position:"fixed",width:"100%",bottom:"0px","background-color":"#fafafa",height:"60px","z-index":"9999",display:"flex","align-items":"center","justify-content":"center"}},J={style:{"text-align":"center"}},B={style:{"margin-top":"10px","text-align":"center"}},C={style:{"margin-top":"10px","text-align":"center"}},G={class:"dialog-footer",style:{"text-align":"center"}},I={style:{display:"flex","flex-direction":"column","justify-content":"center","align-items":"center"}},H=["src"],K=o({__name:"temp_question",setup(o){s(1);const K=s([]),Q=s(!1),W=s([]),P=s(""),$=s("2"),M=s(""),X=n(),Y=s(""),Z=s(!1),ee=s([]),le=s("");i((()=>{Y.value=X.query.guid,X.query.guid?(ae(),se(),oe()):d.error("缺少guid参数")})),s({ALLOW_UNKNOWN_PROTOCOLS:!0,FORBID_TAGS:"style",ADD_ATTR_ON_TAGS:["a"],ADD_TAG_HANDLER:{a(e){e.attribs.target="_blank",e.attribs.rel="noopener noreferrer"}}});const te=s(""),ae=()=>{Z.value=!0,e(Y.value).then((e=>{if(200==e.code){const{b:o,q:s}=e.data;if(s.length>0){var l=[];s.forEach((e=>{e.ques=JSON.parse(e.title),l.push(e)})),K.value=l}if(o.length>0){var t=o[0];P.value=t.fileurl,te.value=t.markdown}a("success",e.msg)}else a("error",e.msg)})).catch((e=>{a("error",e)})).finally((()=>{u((()=>{Z.value=!1}))}))},oe=()=>{q({page:1,size:99999}).then((e=>{200==e.code?(W.value=e.data.list,W.value.forEach((e=>{e.id=e.id.toString()}))):a("error",e.msg)})).catch((e=>{a("error",e)}))},se=()=>{t({}).then((e=>{ee.value=e.data}))};return(e,t)=>{const o=r("el-skeleton"),s=r("mavon-editor"),n=r("el-input"),i=r("el-button"),u=r("el-option"),q=r("el-select"),X=r("el-dialog");return m(),p("div",null,[c("div",U,[c("div",S,[c("iframe",{style:{width:"100%",height:"99%",border:"0px solid #fff"},src:`https://view.officeapps.live.com/op/view.aspx?src=${encodeURIComponent(P.value)}&wdOrigin=BROWSELINK`},null,8,N)]),c("div",j,[Z.value?(m(),g(o,{key:0,rows:5,animated:!0})):h("",!0),(m(!0),p(x,null,f(K.value,((e,l)=>(m(),p("div",{key:e.id,class:"question-item"},[c("div",null,y(l+1)+"、【id:"+y(e.id)+"】"+y(e.questiontype),1),c("div",O,[t[7]||(t[7]=c("div",{style:{"font-size":"17px","font-weight":"bold"}},"题干：",-1)),v(s,{toolbarsFlag:!1,boxShadow:!1,html:!0,style:{"margin-top":"10px"},modelValue:e.ques.title,"onUpdate:modelValue":l=>e.ques.title=l},null,8,["modelValue","onUpdate:modelValue"])]),e.ques.subQuestions?(m(),p("div",z,[(m(!0),p(x,null,f(e.ques.subQuestions,((e,l)=>(m(),p("div",{key:l,style:{"margin-top":"10px"}},[w(y(l+1)+"、"+y(e.questionType)+" ",1),v(s,{toolbarsFlag:!1,boxShadow:!1,html:!0,style:{"margin-top":"10px"},modelValue:e.title,"onUpdate:modelValue":l=>e.title=l},null,8,["modelValue","onUpdate:modelValue"]),e.options?(m(),p("div",F,[t[8]||(t[8]=c("div",{style:{"font-size":"17px","font-weight":"bold"}},"选项：",-1)),(m(!0),p(x,null,f(e.options,((e,l)=>(m(),p("div",{key:l,style:{display:"flex","align-items":"start",gap:"10px","margin-top":"10px"}},[v(n,{modelValue:e.listNo,"onUpdate:modelValue":l=>e.listNo=l,style:{width:"40px"}},null,8,["modelValue","onUpdate:modelValue"]),v(s,{toolbarsFlag:!1,boxShadow:!1,html:!0,modelValue:e.option,"onUpdate:modelValue":l=>e.option=l,class:"content-show",style:{flex:"1",height:"50px"}},null,8,["modelValue","onUpdate:modelValue"])])))),128))])):h("",!0),c("div",T,[c("div",null,[t[9]||(t[9]=c("div",{style:{"font-size":"17px","font-weight":"bold"}},"答案：",-1)),v(s,{toolbarsFlag:!1,boxShadow:!1,html:!0,modelValue:e.answer,"onUpdate:modelValue":l=>e.answer=l,class:"content-show",style:{flex:"1",height:"50px","margin-top":"10px"}},null,8,["modelValue","onUpdate:modelValue"])])])])))),128))])):(m(),p("div",A,[e.ques.options?(m(),p("div",D,[t[10]||(t[10]=c("div",{style:{"font-size":"17px","font-weight":"bold"}},"选项：",-1)),(m(!0),p(x,null,f(e.ques.options,((e,l)=>(m(),p("div",{key:l,style:{display:"flex","align-items":"start",gap:"10px","margin-top":"10px"}},[v(n,{modelValue:e.listNo,"onUpdate:modelValue":l=>e.listNo=l,style:{width:"40px"}},null,8,["modelValue","onUpdate:modelValue"]),v(s,{toolbarsFlag:!1,boxShadow:!1,html:!0,modelValue:e.option,"onUpdate:modelValue":l=>e.option=l,class:"content-show",style:{flex:"1",height:"50px"}},null,8,["modelValue","onUpdate:modelValue"])])))),128))])):h("",!0),c("div",E,[c("div",null,[t[11]||(t[11]=c("div",{style:{"font-size":"17px","font-weight":"bold"}},"答案：",-1)),v(s,{toolbarsFlag:!1,boxShadow:!1,html:!0,modelValue:e.ques.answer,"onUpdate:modelValue":l=>e.ques.answer=l,class:"content-show",style:{flex:"1",height:"50px","margin-top":"10px"}},null,8,["modelValue","onUpdate:modelValue"])])]),c("div",L,[c("div",null,[t[12]||(t[12]=c("div",{style:{"font-size":"17px","font-weight":"bold"}},"答案解析：",-1)),v(s,{toolbarsFlag:!1,boxShadow:!1,html:!0,modelValue:e.answer_parsing,"onUpdate:modelValue":l=>e.answer_parsing=l,class:"content-show",style:{flex:"1",height:"50px","margin-top":"10px"}},null,8,["modelValue","onUpdate:modelValue"])])])]))])))),128))])]),c("div",R,[v(i,{type:"primary",onClick:t[0]||(t[0]=e=>{Q.value=!0})},{default:V((()=>t[13]||(t[13]=[w("导入题库")]))),_:1,__:[13]})]),v(X,{modelValue:Q.value,"onUpdate:modelValue":t[5]||(t[5]=e=>Q.value=e),title:"导入",width:"500"},{footer:V((()=>[c("div",G,[v(i,{type:"primary",onClick:t[4]||(t[4]=e=>(()=>{if(""==$.value)return void d.error("请选择课程");if(""==M.value)return void d.error("请输入来源");if(0==K.value.length)return void d.error("没有题目");var e=[];K.value.forEach((l=>{var t={title:JSON.stringify(l.ques),questiontype:l.questiontype,is_autoscore:l.ques.options||l.ques.subQuestions?1:0,answer:l.ques.answer?l.ques.answer:l.answer,answer_parsing:l.answer_parsing,course_base_id:$.value,jobbank_source_id:M.value,ver:l.ver?l.ver:le.value,sn:l.sn?l.sn:"",knowledge_point:l.knowledge_point?l.knowledge_point:"",competence_level:l.competence_level?l.competence_level:"",difficulty_level:l.difficulty_level?l.difficulty_level:"",chapter_mc:l.chapter_mc?l.chapter_mc:"",chapter_no:l.chapter_no?l.chapter_no:""};e.push(t)}));let t=new FormData;t.append("jsonstr",JSON.stringify(e)),t.append("guid",Y.value),b.confirm("是否导入题库","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{l(t,{jsonstr:JSON.stringify(e)}).then((e=>{200==e.code?alert("导入"+e.data+"道题"):a("error",e.msg)})).catch((e=>{a("error",e)}))})).catch((()=>{d.info("已取消导入")}))})())},{default:V((()=>t[14]||(t[14]=[w(" 导入 ")]))),_:1,__:[14]})])])),default:V((()=>[c("div",J,[v(q,{modelValue:$.value,"onUpdate:modelValue":t[1]||(t[1]=e=>$.value=e),placeholder:"选择课程",size:"large",style:{width:"240px"}},{default:V((()=>[(m(!0),p(x,null,f(W.value,(e=>(m(),g(u,{key:e.id,label:"【"+e.kc_bm+"】"+e.kc_mc,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])]),c("div",B,[v(q,{modelValue:M.value,"onUpdate:modelValue":t[2]||(t[2]=e=>M.value=e),placeholder:"选择来源",size:"large",style:{width:"240px"}},{default:V((()=>[(m(!0),p(x,null,f(ee.value,(e=>(m(),g(u,{key:e.id,label:e.title,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])]),c("div",C,[v(n,{modelValue:le.value,"onUpdate:modelValue":t[3]||(t[3]=e=>le.value=e),placeholder:"版本",style:{width:"240px"}},null,8,["modelValue"])])])),_:1},8,["modelValue"]),v(X,{"header-class":"loadingHader",modelValue:Z.value,"onUpdate:modelValue":t[6]||(t[6]=e=>Z.value=e),width:"300"},{default:V((()=>[c("div",I,[c("img",{src:_(k),style:{width:"50px"}},null,8,H),t[15]||(t[15]=w(" 渲染中.... "))])])),_:1},8,["modelValue"])])}}});export{K as default};
