import{d as e,r as l,N as a,o as i,a as o,L as u,c as d,b as t,e as r,j as s,w as n,p as c,F as f,f as p,h as v,M as m,t as _,m as g,q as w,O as h,P as b,_ as y}from"./index-BERmxY3Y.js";import{g as x,a as k,u as V,i as z,U as C,b as U,c as I}from"./dzs-DZN_gNG7.js";import{m as L}from"./message-Df6PNwXY.js";import{_ as j}from"./preview_url.vue_vue_type_style_index_0_lang-uW4b37j2.js";const F={class:"page"},S={class:"header"},q={class:"body"},R={key:0,class:"upload-file-info"},A={class:"dialog-footer"},N=["src"],$={key:0,class:"upload-file-info"},D={class:"dialog-footer"},M=y(e({__name:"dzs",setup(e){const y=l([]),M=l({pageLoading:!1,pageLoadingText:"获取数据中，请稍后...",pageLoadingSvg:'\n        <path class="path" d="\n          M 30 15\n          L 28 17\n          M 25.61 25.61\n          A 15 15, 0, 0, 1, 15 30\n          A 15 15, 0, 1, 1, 27.99 7.5\n          L 15 15\n        " style="stroke-width: 4px; fill: rgba(0, 0, 0, 0)"/>\n      ',selected_course_id:null}),O=a(),T=l(!1),B=l(null),X=l({visible:!1,progress:0,uploading:!1,form:{title:"",file:null,course_id:null,zsd_key:""}}),E=l({visible:!1,url:""}),J=l({visible:!1,progress:0,uploading:!1,form:{id:null,title:"",file:null,course_id:null,zsd_key:"",url:""}}),P=l([]),G=async()=>{try{const e=await U();200===e.code?P.value=e.data:L(e.msg||"获取课程列表失败","warning")}catch(e){L("获取课程列表失败","error")}},H=()=>({height:"40px"}),K=()=>({padding:"4px 0"}),Q=e=>{if(!e)return"-";const l=P.value.find((l=>Number(l.id)===Number(e)));return(null==l?void 0:l.course_code)||"-"},W=e=>{if(!e)return"-";const l=P.value.find((l=>Number(l.id)===Number(e)));return(null==l?void 0:l.course_name)||"-"},Y=l([]),Z=l(!1),ee=()=>{setInterval((()=>{(async()=>{if(Z.value||0===Y.value.length)return;Z.value=!0;const e=Y.value[0];try{const l={execute_id:e.executeId},a=await I(l);if(200===a.code){const l=y.value.findIndex((l=>l.id===e.row.id));-1!==l&&(y.value[l].execute_status=a.data),"Running"===a.data&&Y.value.push(e)}}catch(l){L("检查内容生成状态失败","error")}finally{Y.value.shift(),Z.value=!1}})()}),3e3)},le=(e,l)=>{Y.value.some((a=>a.executeId===e&&a.row.id===l.id))||Y.value.push({executeId:e,row:l})},ae=async e=>{try{const l={zsd_key:e.zsd_key,url:e.url,id:e.id},a=await x(l);if(200===a.code){const l=y.value.findIndex((l=>l.id===e.id));-1!==l&&(y.value[l].execute_id=a.data,y.value[l].execute_status="Running"),(async(e,l)=>{le(e,l)})(a.data,e),L("AI内容获取已开始，耗时较长，稍候查看","success")}else L(a.msg||"AI内容获取失败","warning")}catch(l){L("AI内容获取失败","error")}},ie=async()=>{M.value.pageLoading=!0;try{await G();const e={course_id:M.value.selected_course_id},l=await k(e);200===l.code?(y.value=l.data,y.value.forEach((e=>{(e.execute_id&&"Running"===e.execute_status||e.execute_id&&null===e.execute_status)&&le(e.execute_id,e)}))):L(l.msg||"获取电子书列表失败","warning")}catch(e){L("获取数据失败","error")}finally{M.value.pageLoading=!1}},oe=()=>{ie()},ue=async()=>{await G(),X.value.visible=!0,X.value.form={title:"",file:null,course_id:null,zsd_key:""}},de=e=>{const l=e.raw||e.file;return"application/vnd.openxmlformats-officedocument.wordprocessingml.document"===l.type?(X.value.form.file=l,X.value.form.title=l.name.split(".")[0],!1):(L("只能上传 DOCX 格式的文件！","warning"),!1)},te=l(),re=e=>{te.value.clearFiles();const l=e[0];l.uid=b(),te.value.handleStart(l)},se=e=>e+"%",ne=async()=>{if(X.value.form.title)if(X.value.form.file)if(X.value.form.course_id){X.value.uploading=!0,X.value.progress=0;try{const e=new FormData;e.append("file",X.value.form.file);const l=await V(e);if(l.success){const e=await z({course_jc_id:X.value.form.course_id,title:X.value.form.title,url:l.url,zsd_key:X.value.form.zsd_key});200===e.code?(L("上传成功","success"),X.value.visible=!1,ie()):L(e.msg||"保存数据失败","error")}else L("文件上传失败","error")}catch(e){L("上传失败","error")}finally{X.value.uploading=!1,X.value.progress=0}}else L("请选择所属课程","warning");else L("请选择要上传的文件","warning");else L("请输入标题","warning")},ce=e=>{switch(e){case"Success":return"success";case"Fail":return"danger";case"Running":return"warning";default:return"info"}},fe=e=>{switch(e){case"Success":return"成功";case"Fail":return"失败";case"Running":return"执行中";default:return"未执行"}},pe=l(),ve=e=>{const l=e.raw||e.file;return"application/vnd.openxmlformats-officedocument.wordprocessingml.document"===l.type?(J.value.form.file=l,!1):(L("只能上传 DOCX 格式的文件！","warning"),!1)},me=e=>{pe.value.clearFiles();const l=e[0];l.uid=b(),pe.value.handleStart(l)},_e=async()=>{if(J.value.form.title)if(J.value.form.course_id){J.value.uploading=!0,J.value.progress=0;try{let e=J.value.form.url,l=0;if(J.value.form.file){const a=new FormData;a.append("file",J.value.form.file);const i=await V(a);if(!i.success)return void L("文件上传失败","error");e=i.url,l=1}const a=await C({id:J.value.form.id,course_jc_id:J.value.form.course_id,title:J.value.form.title,url:e,zsd_key:J.value.form.zsd_key,url_change:l});200===a.code?(L("更新成功","success"),J.value.visible=!1,ie()):L(a.msg||"更新失败","error")}catch(e){L("更新失败","error")}finally{J.value.uploading=!1,J.value.progress=0}}else L("请选择所属课程","warning");else L("请输入标题","warning")};return i((()=>{ie(),ee()})),(e,l)=>{const a=o("el-button"),i=o("el-option"),b=o("el-select"),x=o("el-table-column"),k=o("el-tag"),V=o("el-table"),z=o("el-form-item"),C=o("el-input"),U=o("el-icon"),I=o("el-upload"),L=o("el-progress"),G=o("el-form"),Y=o("el-dialog"),Z=u("loading");return t(),d("div",F,[r("div",S,[s(a,{plain:"",type:"success",size:"small",onClick:ie,icon:"refresh"},{default:n((()=>l[13]||(l[13]=[c("刷新 ")]))),_:1,__:[13]}),s(b,{modelValue:M.value.selected_course_id,"onUpdate:modelValue":l[0]||(l[0]=e=>M.value.selected_course_id=e),placeholder:"请选择课程",clearable:"",filterable:"",size:"small",style:{width:"260px"},onChange:oe},{default:n((()=>[(t(!0),d(f,null,p(P.value,(e=>(t(),v(i,{key:e.id,label:`${e.course_code} ${e.course_name}`,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"]),s(a,{type:"primary",size:"small",onClick:ue,icon:"upload"},{default:n((()=>l[14]||(l[14]=[c("上传电子书 ")]))),_:1,__:[14]})]),r("div",q,[m((t(),v(V,{data:y.value,width:"100%",height:"calc(100vh - 135px)",ref:"multipleTable","highlight-current-row":"",size:"small",border:"","element-loading-text":M.value.pageLoadingText,"element-loading-spinner":M.value.pageLoadingSvg,"element-loading-svg-view-box":"-10, -10, 50, 50","element-loading-background":"rgba(122, 122, 122, 0.8)","row-style":H,"cell-style":K},{default:n((()=>[s(x,{prop:"",type:"index",label:"序号",align:"center",width:"60","header-align":"center"}),s(x,{label:"课程编码",align:"left","min-width":"100","header-align":"center","show-overflow-tooltip":""},{default:n((e=>[c(_(Q(e.row.course_jc_id)),1)])),_:1}),s(x,{label:"课程名称",align:"left","min-width":"100","header-align":"center","show-overflow-tooltip":""},{default:n((e=>[c(_(W(e.row.course_jc_id)),1)])),_:1}),s(x,{label:"标题",align:"left","min-width":"200","header-align":"center","show-overflow-tooltip":"",prop:"title"}),s(x,{label:"知识点标记",align:"left","min-width":"100","header-align":"center","show-overflow-tooltip":"",prop:"zsd_key"}),s(x,{label:"URL","min-width":"100",align:"center","header-align":"center","show-overflow-tooltip":""},{default:n((e=>[s(a,{link:"",type:"primary",size:"small",onClick:l=>{return a=e.row.url,B.value=a,void(T.value=!0);var a}},{default:n((()=>l[15]||(l[15]=[c("查看")]))),_:2,__:[15]},1032,["onClick"])])),_:1}),s(x,{label:"修改时间",align:"center",width:"200","header-align":"center",prop:"create_date"}),s(x,{label:"修改人",align:"center",width:"100","header-align":"center",prop:"name"}),s(x,{label:"执行ID",align:"center",width:"150","header-align":"center",prop:"execute_id"}),s(x,{label:"执行状态",align:"center",width:"100","header-align":"center"},{default:n((e=>[s(k,{type:ce(e.row.execute_status)},{default:n((()=>[c(_(fe(e.row.execute_status)),1)])),_:2},1032,["type"])])),_:1}),s(x,{label:"操作",align:"center",width:"180","header-align":"center"},{default:n((e=>["Success"!==e.row.execute_status?(t(),v(a,{key:0,size:"small",type:"primary",icon:"magic-stick",disabled:!(!e.row.execute_id||"Running"!==e.row.execute_status),onClick:l=>ae(e.row)},{default:n((()=>l[16]||(l[16]=[c("AI获取 ")]))),_:2,__:[16]},1032,["disabled","onClick"])):g("",!0),"Success"===e.row.execute_status?(t(),v(a,{key:1,size:"small",type:"info",icon:"view",onClick:l=>(e=>{const{href:l}=O.resolve({path:"/dzs_info",query:{id:e.id}});window.open(l,"_blank")})(e.row)},{default:n((()=>l[17]||(l[17]=[c("预览 ")]))),_:2,__:[17]},1032,["onClick"])):g("",!0),s(a,{size:"small",type:"warning",icon:"edit",onClick:l=>{return a=e.row,J.value.visible=!0,void(J.value.form={id:a.id,title:a.title,file:null,course_id:a.course_jc_id,zsd_key:a.zsd_key,url:a.url});var a}},{default:n((()=>l[18]||(l[18]=[c("编辑 ")]))),_:2,__:[18]},1032,["onClick"])])),_:1})])),_:1},8,["data","element-loading-text","element-loading-spinner"])),[[Z,M.value.pageLoading]])]),s(Y,{modelValue:X.value.visible,"onUpdate:modelValue":l[5]||(l[5]=e=>X.value.visible=e),title:"上传电子书",width:"500px","close-on-click-modal":!1,"close-on-press-escape":!X.value.uploading},{footer:n((()=>[r("span",A,[s(a,{onClick:l[4]||(l[4]=e=>X.value.visible=!1),disabled:X.value.uploading},{default:n((()=>l[22]||(l[22]=[c("取消")]))),_:1,__:[22]},8,["disabled"]),s(a,{type:"primary",onClick:ne,loading:X.value.uploading,disabled:X.value.uploading},{default:n((()=>[c(_(X.value.uploading?"上传中...":"确定"),1)])),_:1},8,["loading","disabled"])])])),default:n((()=>[s(G,{model:X.value.form,"label-width":"100px"},{default:n((()=>[s(z,{label:"所属课程",required:""},{default:n((()=>[s(b,{modelValue:X.value.form.course_id,"onUpdate:modelValue":l[1]||(l[1]=e=>X.value.form.course_id=e),placeholder:"请选择课程",style:{width:"100%"},filterable:"",clearable:""},{default:n((()=>[(t(!0),d(f,null,p(P.value,(e=>(t(),v(i,{key:e.id,label:`${e.course_code} ${e.course_name}`,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),s(z,{label:"标题",required:""},{default:n((()=>[s(C,{modelValue:X.value.form.title,"onUpdate:modelValue":l[2]||(l[2]=e=>X.value.form.title=e),placeholder:"请输入标题"},null,8,["modelValue"])])),_:1}),s(z,{label:"知识点标记"},{default:n((()=>[s(C,{modelValue:X.value.form.zsd_key,"onUpdate:modelValue":l[3]||(l[3]=e=>X.value.form.zsd_key=e),placeholder:"请输入知识点标记"},null,8,["modelValue"])])),_:1}),s(z,{label:"电子书文件",required:""},{default:n((()=>[s(I,{class:"upload-demo",drag:"",ref_key:"upload",ref:te,"show-file-list":!1,action:"#","auto-upload":!1,"on-change":de,limit:1,multiple:!1,disabled:X.value.uploading,"on-exceed":re,accept:".docx"},{tip:n((()=>l[19]||(l[19]=[r("div",{class:"el-upload__tip"}," 仅支持 docx 格式 ",-1)]))),default:n((()=>[s(U,{class:"el-icon--upload"},{default:n((()=>[s(w(h))])),_:1}),l[20]||(l[20]=r("div",{class:"el-upload__text"},[c(" 将文件拖到此处，或"),r("em",null,"点击上传")],-1))])),_:1,__:[20]},8,["disabled"]),X.value.form.file?(t(),d("div",R," 已选择: "+_(X.value.form.file.name)+" ("+_((X.value.form.file.size/1048576).toFixed(2))+"MB) ",1)):g("",!0)])),_:1}),X.value.uploading?(t(),v(z,{key:0},{default:n((()=>[s(L,{percentage:X.value.progress,format:se},null,8,["percentage"]),l[21]||(l[21]=r("div",{class:"upload-status"},"正在上传中，请勿关闭窗口...",-1))])),_:1,__:[21]})):g("",!0)])),_:1},8,["model"])])),_:1},8,["modelValue","close-on-press-escape"]),s(Y,{modelValue:E.value.visible,"onUpdate:modelValue":l[6]||(l[6]=e=>E.value.visible=e),title:"电子书预览",width:"800px","destroy-on-close":!0,"close-on-click-modal":!1},{default:n((()=>[E.value.url?(t(),d("iframe",{key:0,src:E.value.url,style:{width:"100%",height:"600px",border:"none"}},null,8,N)):g("",!0)])),_:1},8,["modelValue"]),s(j,{modelValue:T.value,"onUpdate:modelValue":l[7]||(l[7]=e=>T.value=e),"doc-url":B.value},null,8,["modelValue","doc-url"]),s(Y,{modelValue:J.value.visible,"onUpdate:modelValue":l[12]||(l[12]=e=>J.value.visible=e),title:"编辑电子书",width:"500px","close-on-click-modal":!1,"close-on-press-escape":!J.value.uploading},{footer:n((()=>[r("span",D,[s(a,{onClick:l[11]||(l[11]=e=>J.value.visible=!1),disabled:J.value.uploading},{default:n((()=>l[26]||(l[26]=[c("取消")]))),_:1,__:[26]},8,["disabled"]),s(a,{type:"primary",onClick:_e,loading:J.value.uploading,disabled:J.value.uploading},{default:n((()=>[c(_(J.value.uploading?"保存中...":"确定"),1)])),_:1},8,["loading","disabled"])])])),default:n((()=>[s(G,{model:J.value.form,"label-width":"100px"},{default:n((()=>[s(z,{label:"所属课程",required:""},{default:n((()=>[s(b,{modelValue:J.value.form.course_id,"onUpdate:modelValue":l[8]||(l[8]=e=>J.value.form.course_id=e),placeholder:"请选择课程",style:{width:"100%"},filterable:"",clearable:""},{default:n((()=>[(t(!0),d(f,null,p(P.value,(e=>(t(),v(i,{key:e.id,label:`${e.course_code} ${e.course_name}`,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),s(z,{label:"标题",required:""},{default:n((()=>[s(C,{modelValue:J.value.form.title,"onUpdate:modelValue":l[9]||(l[9]=e=>J.value.form.title=e),placeholder:"请输入标题"},null,8,["modelValue"])])),_:1}),s(z,{label:"知识点标记"},{default:n((()=>[s(C,{modelValue:J.value.form.zsd_key,"onUpdate:modelValue":l[10]||(l[10]=e=>J.value.form.zsd_key=e),placeholder:"请输入知识点标记"},null,8,["modelValue"])])),_:1}),s(z,{label:"电子书文件"},{default:n((()=>[s(I,{class:"upload-demo",drag:"",ref_key:"editUpload",ref:pe,"show-file-list":!1,action:"#","auto-upload":!1,"on-change":ve,limit:1,multiple:!1,disabled:J.value.uploading,"on-exceed":me,accept:".docx"},{tip:n((()=>l[23]||(l[23]=[r("div",{class:"el-upload__tip"}," 仅支持 docx 格式，不选择文件则保持原文件不变 ",-1)]))),default:n((()=>[s(U,{class:"el-icon--upload"},{default:n((()=>[s(w(h))])),_:1}),l[24]||(l[24]=r("div",{class:"el-upload__text"},[c(" 将文件拖到此处，或"),r("em",null,"点击上传")],-1))])),_:1,__:[24]},8,["disabled"]),J.value.form.file?(t(),d("div",$," 已选择: "+_(J.value.form.file.name)+" ("+_((J.value.form.file.size/1048576).toFixed(2))+"MB) ",1)):g("",!0)])),_:1}),J.value.uploading?(t(),v(z,{key:0},{default:n((()=>[s(L,{percentage:J.value.progress,format:se},null,8,["percentage"]),l[25]||(l[25]=r("div",{class:"upload-status"},"正在上传中，请勿关闭窗口...",-1))])),_:1,__:[25]})):g("",!0)])),_:1},8,["model"])])),_:1},8,["modelValue","close-on-press-escape"])])}}}),[["__scopeId","data-v-722320f7"]]);export{M as default};
