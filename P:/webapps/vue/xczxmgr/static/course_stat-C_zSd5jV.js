import{e}from"./course-DUm3UmHR.js";import{m as a}from"./message-Df6PNwXY.js";import{d as l,r as n,o as t,n as r,a as o,L as s,c as i,b as d,e as u,j as p,w as c,p as _,F as g,f as m,h,t as b,M as w,_ as y}from"./index-BERmxY3Y.js";const v={class:"course_info page"},k={class:"header"},f={class:"body"},x={key:0},j={key:1},L={key:0},z={key:1},V={key:0},S={key:1},C={key:0},M={key:1},T={key:0},A={key:1},I={key:0},U={key:1},E={key:0},F={key:1},H=y(l({__name:"course_stat",setup(l){const y=n([]),H=n([]),J=n({pageLoading:!1,pageLoadingText:"获取数据中，请稍后...",pageLoadingSvg:'\n        <path class="path" d="\n          M 30 15\n          L 28 17\n          M 25.61 25.61\n          A 15 15, 0, 0, 1, 15 30\n          A 15 15, 0, 1, 1, 27.99 7.5\n          L 15 15\n        " style="stroke-width: 4px; fill: rgba(0, 0, 0, 0)"/>\n      ',key_search:"",learn_type_id:["2"],learn_type_ls:[{t:"成人高考",v:"1",num:0},{t:"自考",v:"2",num:0},{t:"微课",v:"3",num:0}]});t((async()=>{await r((async()=>{await B()}))}));const q=()=>{H.value=y.value.filter((e=>{const{key_search:a,learn_type_id:l}=J.value;return(e.kc_bm.includes(a)||e.kc_mc.includes(a))&&l.includes(e.learn_type_id.toString())})),J.value.learn_type_ls.forEach((e=>{e.num=y.value.filter((a=>a.learn_type_id.toString()===e.v)).length}))},B=async()=>{await D()},D=async()=>{await e().then((e=>{200===e.code?(y.value=e.data,q()):a(e.msg,"warning")})).catch((e=>{a(e.toString(),"error")}))};return(e,a)=>{const l=o("el-button"),n=o("el-input"),t=o("el-tag"),r=o("el-checkbox"),y=o("el-checkbox-group"),D=o("el-table-column"),G=o("el-table"),K=s("loading");return d(),i("div",v,[u("div",k,[p(l,{plain:"",type:"success",size:"small",onClick:B,icon:"refresh"},{default:c((()=>a[2]||(a[2]=[_("刷新 ")]))),_:1,__:[2]}),p(n,{placeholder:"课程代码、名称关键字查询",modelValue:J.value.key_search,"onUpdate:modelValue":a[0]||(a[0]=e=>J.value.key_search=e),size:"small",onChange:q,clearable:"",style:{width:"260px"}},{prepend:c((()=>a[3]||(a[3]=[_(" 查询 ")]))),_:1},8,["modelValue"]),p(t,{type:"info"},{default:c((()=>a[4]||(a[4]=[_("筛选1")]))),_:1,__:[4]}),p(y,{modelValue:J.value.learn_type_id,"onUpdate:modelValue":a[1]||(a[1]=e=>J.value.learn_type_id=e),size:"small",onChange:q},{default:c((()=>[(d(!0),i(g,null,m(J.value.learn_type_ls,(e=>(d(),h(r,{key:e.v,label:e.t,value:e.v,border:""},{default:c((()=>[_(b(e.t)+" "+b(e.num),1)])),_:2},1032,["label","value"])))),128))])),_:1},8,["modelValue"])]),u("div",f,[w((d(),h(G,{data:H.value,width:"100%",height:"calc(100vh - 135px)",ref:"multipleTable","highlight-current-row":"",size:"small",border:"","element-loading-text":J.value.pageLoadingText,"element-loading-spinner":J.value.pageLoadingSvg,"element-loading-svg-view-box":"-10, -10, 50, 50","element-loading-background":"rgba(122, 122, 122, 0.8)"},{default:c((()=>[p(D,{label:"序号",align:"center",width:"45","header-align":"center",prop:"sn"}),p(D,{label:"基础课程Id",align:"center",width:"100",sortable:"","header-align":"center",prop:"course_base_id"}),p(D,{label:"课程代码",align:"center",sortable:"",width:"80","header-align":"center",prop:"kc_bm"}),p(D,{label:"课程名称",sortable:"",align:"left","min-width":"150","header-align":"center","show-overflow-tooltip":"",prop:"kc_mc"}),p(D,{label:"类别",align:"center",sortable:"",width:"80","header-align":"center",prop:"learn_type_title"}),p(D,{label:"在线",sortable:"",align:"center",width:"80","header-align":"center",prop:"num_online"},{default:c((e=>[e.row.num_online>0?(d(),i("span",x,b(e.row.num_online),1)):(d(),i("span",j))])),_:1}),p(D,{label:"发布",sortable:"",align:"center",width:"80","header-align":"center",prop:"num_pub"},{default:c((e=>[e.row.num_pub>0?(d(),i("span",L,b(e.row.num_pub),1)):(d(),i("span",z))])),_:1}),p(D,{label:"教材",sortable:"",align:"center",width:"80","header-align":"center",prop:"num_jc"},{default:c((e=>[e.row.num_jc>0?(d(),i("span",V,b(e.row.num_jc),1)):(d(),i("span",S))])),_:1}),p(D,{label:"考试大纲",sortable:"",align:"center",width:"80","header-align":"center",prop:"num_ksdg"},{default:c((e=>[e.row.num_ksdg>0?(d(),i("span",C,b(e.row.num_ksdg),1)):(d(),i("span",M))])),_:1}),p(D,{label:"电子书",sortable:"",align:"center",width:"80","header-align":"center",prop:"num_dzs"},{default:c((e=>[e.row.num_dzs>0?(d(),i("span",T,b(e.row.num_dzs),1)):(d(),i("span",A))])),_:1}),p(D,{label:"题库",sortable:"",align:"center",width:"80","header-align":"center",prop:"num_job"},{default:c((e=>[e.row.num_job>0?(d(),i("span",I,b(e.row.num_job),1)):(d(),i("span",U))])),_:1}),p(D,{label:"视频",sortable:"",align:"center",width:"80","header-align":"center",prop:"num_video"},{default:c((e=>[e.row.num_video>0?(d(),i("span",E,b(e.row.num_video),1)):(d(),i("span",F))])),_:1})])),_:1},8,["data","element-loading-text","element-loading-spinner"])),[[K,J.value.pageLoading]])])])}}}),[["__scopeId","data-v-dc013c18"]]);export{H as default};
