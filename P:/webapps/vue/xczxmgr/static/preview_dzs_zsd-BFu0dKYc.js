import{d as e,r as l,Y as a,S as t,o as s,a as o,h as n,b as d,w as i,e as u,j as c,q as r,a4 as p,c as v,F as m,f,t as w,v as y,E as V,_ as h}from"./index-BERmxY3Y.js";import{x as _}from"./dzs-DZN_gNG7.js";const g={class:"preview-container"},k={class:"preview-panel left-panel"},x={class:"panel-content"},T={class:"preview-panel right-panel"},z={class:"panel-content"},C={key:0,class:"empty-hint"},S={key:1,class:"knowledge-points"},j=h(e({__name:"preview_dzs_zsd",props:{modelValue:{type:Boolean,default:!1},id:{},title:{default:"知识点预览"},content:{default:""}},emits:["update:content","update:modelValue","close","save"],setup(e,{emit:h}){const j=e,q=h,B=l(j.modelValue),I=l(""),U=l(""),b=()=>{let e=document.querySelector(".el-textarea__inner"),l=document.querySelector(".markdown-preview");l&&e&&(l.scrollTop=e.scrollTop)},A=a((()=>I.value.trim()?I.value.split(",").map((e=>e.trim())).filter((e=>e)):[]));let E=null;const F=()=>{E&&clearTimeout(E),E=setTimeout((()=>{}),500)},N=async()=>{if(I.value!==U.value)try{await y.confirm("内容已修改，是否保存更改？","提示",{confirmButtonText:"保存",cancelButtonText:"不保存",type:"warning",distinguishCancelAndClose:!0});try{const e=await _({id:j.id,zsd:A.value});if(200!==e.code)return void V.error(e.msg||"保存失败");U.value=I.value,q("update:content",I.value),q("save",I.value),V.success("保存成功")}catch(e){return void V.error("保存失败，请检查数据格式")}}catch(l){if("cancel"===l)I.value=U.value;else if("close"===l)return}q("update:modelValue",!1),q("close"),E&&(clearTimeout(E),E=null)},Y=()=>{I.value=j.content||"",U.value=j.content||""};return t((()=>j.modelValue),(e=>{B.value=e,e&&Y()}),{immediate:!0}),t(B,(e=>{e&&Y(),q("update:modelValue",e)})),t((()=>j.content),(e=>{e!==I.value&&Y()}),{immediate:!0}),s((()=>{Y()})),(e,l)=>{const a=o("el-icon"),t=o("el-input"),s=o("el-empty"),y=o("el-dialog");return d(),n(y,{modelValue:B.value,"onUpdate:modelValue":l[1]||(l[1]=e=>B.value=e),title:e.title,fullscreen:"","close-on-click-modal":!1,"close-on-press-escape":!0,"show-close":!1,"destroy-on-close":"",class:"preview-dialog",onClose:N},{default:i((()=>[u("div",{class:"dialog-close",onClick:N},[c(a,null,{default:i((()=>[c(r(p))])),_:1})]),u("div",g,[u("div",k,[l[2]||(l[2]=u("div",{class:"panel-header"},"原始内容",-1)),u("div",x,[c(t,{modelValue:I.value,"onUpdate:modelValue":l[0]||(l[0]=e=>I.value=e),type:"textarea",rows:10,placeholder:"请输入知识点内容，多个知识点用逗号分隔",onInput:F,onScroll:b},null,8,["modelValue"])])]),u("div",T,[l[3]||(l[3]=u("div",{class:"panel-header"},"预览效果",-1)),u("div",z,[0===A.value.length?(d(),v("div",C,[c(s,{description:"暂无内容"})])):(d(),v("div",S,[(d(!0),v(m,null,f(A.value,((e,l)=>(d(),v("div",{key:l,class:"knowledge-point"},w(e),1)))),128))]))])])])])),_:1},8,["modelValue","title"])}}}),[["__scopeId","data-v-ee8a430d"]]);export{j as default};
