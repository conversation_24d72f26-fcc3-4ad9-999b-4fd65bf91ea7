import{h as e,m as l}from"./settings-CpWN36lq.js";import{d as a,K as t,r as i,o as n,a as o,L as d,c as s,b as r,e as u,j as c,w as p,p as m,M as h,h as g,t as v,E as f}from"./index-BERmxY3Y.js";const b={class:"page"},_={class:"header"},y={class:"body"},k={style:{width:"100%","text-align":"center"}},w={class:"dialog-footer"},C=a({__name:"user-type",setup(a){const C=t({loading:!1,tableHeight:window.innerHeight-180,keySearch:"",dataTable:[]}),x=i(!1),V=i(""),S=i([]),z=i({}),T=()=>{C.loading=!0;const l={key:C.keySearch};e(l).then((e=>{C.dataTable=e.data,C.loading=!1})).catch((e=>{C.loading=!1}))},j=e=>{S.value=e};return n((()=>{T()})),(e,a)=>{const t=o("el-button"),i=o("el-input"),n=o("el-table-column"),H=o("el-link"),U=o("el-table"),D=o("el-form-item"),E=o("el-form"),K=o("el-dialog"),L=d("loading");return r(),s("div",b,[u("div",_,[c(t,{type:"success",plain:"",size:"default",icon:"refresh",round:"",onClick:T}),c(i,{modelValue:C.keySearch,"onUpdate:modelValue":a[0]||(a[0]=e=>C.keySearch=e),placeholder:"名称",size:"default",style:{width:"260px",margin:"0 10px"},clearable:"",onChange:T},{append:p((()=>[c(t,{icon:"Search",onClick:T})])),_:1},8,["modelValue"]),c(t,{type:"primary",plain:"",size:"default",round:"",icon:"CirclePlus",onClick:a[1]||(a[1]=e=>(z.value={id:0,name:"",title:"",remark:""},V.value="新增用户类型",void(x.value=!0)))},{default:p((()=>a[7]||(a[7]=[m("新增")]))),_:1,__:[7]}),c(t,{type:"success",plain:"",size:"default",round:"",icon:"edit",onClick:a[2]||(a[2]=e=>(()=>{if(null==S.value||1!==S.value.length)return f({message:"请勾选一条数据进行编辑！",type:"error"}),!1;V.value="编辑【"+S.value[0].title+"】",x.value=!0,z.value=S.value[0]})())},{default:p((()=>a[8]||(a[8]=[m("编辑")]))),_:1,__:[8]})]),u("div",y,[h((r(),g(U,{ref:"multipleRolesTable",class:"tb-edit",data:C.dataTable,border:"",height:C.tableHeight,size:"mini",onSelectionChange:j},{default:p((()=>[c(n,{type:"selection",width:"60",align:"center"}),c(n,{prop:"",type:"index",label:"序号",align:"center",width:"60","header-align":"center"}),c(n,{prop:"title",label:"名称","min-width":"120",align:"center",sortable:""}),c(n,{prop:"num",label:"人数","min-width":"120",align:"center",sortable:""},{default:p((e=>[c(H,{type:"primary",underline:!1},{default:p((()=>[m(v(e.row.num),1)])),_:2},1024)])),_:1}),c(n,{prop:"date_created",label:"创建时间","min-width":"160",align:"center",sortable:""},{default:p((e=>[u("span",null,v(e.row.date_created),1)])),_:1}),c(n,{prop:"create_name",label:"操作人","min-width":"100",align:"center",sortable:""})])),_:1},8,["data","height"])),[[L,C.loading]])]),c(K,{modelValue:x.value,"onUpdate:modelValue":a[6]||(a[6]=e=>x.value=e),ref:"kdroomDialog",title:V.value,draggable:"",width:"600px","close-on-press-escape":!1,"close-on-click-modal":!1},{footer:p((()=>[u("span",w,[c(t,{onClick:a[4]||(a[4]=e=>x.value=!1),icon:"Close"},{default:p((()=>a[9]||(a[9]=[m("关闭")]))),_:1,__:[9]}),c(t,{type:"success",onClick:a[5]||(a[5]=e=>{""!==z.value.title&&null!==z.value.title&&void 0!==z.value.title?l(z.value).then((e=>{e.data>0?(x.value=!1,f({message:"保存成功！",type:"success"}),T()):f({message:"保存失败！"+e.msg,type:"error"})})):f({message:"用户类型名称不能为空",type:"info"})}),icon:"CircleCheck"},{default:p((()=>a[10]||(a[10]=[m(" 保存 ")]))),_:1,__:[10]})])])),default:p((()=>[u("div",k,[c(E,{model:z.value,inline:!0,"label-width":"100px"},{default:p((()=>[c(D,{label:"用户类型名称"},{default:p((()=>[c(i,{modelValue:z.value.title,"onUpdate:modelValue":a[3]||(a[3]=e=>z.value.title=e),placeholder:"用户类型名称",style:{width:"400px"}},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])])])),_:1},8,["modelValue","title"])])}}});export{C as default};
