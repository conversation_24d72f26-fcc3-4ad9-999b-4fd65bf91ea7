import{d as e,e as a,S as t}from"./dzs-DZN_gNG7.js";import{m as l}from"./message-Df6PNwXY.js";import{d as s,K as o,r,o as i,a as n,L as c,c as d,b as p,e as u,j as _,w as h,p as g,F as m,f,h as b,t as w,M as y,E as j,v}from"./index-BERmxY3Y.js";const x={class:"Jc_chapter page"},L={class:"header"},D={style:{float:"left"}},k={style:{float:"right",color:"#67C23A","font-size":"13px"}},z={class:"body"},C=s({__name:"jc_chapters",setup(s){const C=o({tableDataList:[],tableHeight:window.innerHeight-155,loading:!1,course_jc_id:""}),T=r([]),H=()=>{C.loading=!0;var e={course_jc_id:C.course_jc_id};a(e).then((e=>{C.tableDataList=e.data,C.loading=!1}))};return i((()=>{(async()=>{try{const a=await e();200===a.code?(T.value=a.data,C.course_jc_id=45,H()):l(a.msg||"获取课程列表失败","warning")}catch(a){l("获取课程列表失败","error")}})()})),(e,a)=>{const l=n("el-button"),s=n("el-tag"),o=n("el-option"),r=n("el-select"),i=n("el-table-column"),V=n("el-table"),B=c("loading");return p(),d("div",x,[u("div",L,[_(l,{type:"success",plain:"",size:"default",icon:"refresh",round:"",onClick:H}),_(s,{class:"el_tag_5"},{default:h((()=>a[2]||(a[2]=[g("教材课程")]))),_:1,__:[2]}),_(r,{modelValue:C.course_jc_id,"onUpdate:modelValue":a[0]||(a[0]=e=>C.course_jc_id=e),placeholder:"请选择教材课程",clearable:"",filterable:"",style:{width:"260px"},onChange:H},{default:h((()=>[(p(!0),d(m,null,f(T.value,(e=>(p(),b(o,{key:e.id,label:"[ "+e.course_code+" ] "+e.course_name,value:e.id},{default:h((()=>[u("span",D,w("[ "+e.course_code+" ] "+e.course_name),1),u("span",k,w(e.ver),1)])),_:2},1032,["label","value"])))),128))])),_:1},8,["modelValue"]),_(l,{type:"primary",style:{"margin-left":"10px"},disabled:C.tableDataList.length>0,plain:"",onClick:a[1]||(a[1]=e=>{""!==C.course_jc_id&&null!==C.course_jc_id&&void 0!==C.course_jc_id?C.tableDataList.length>0?j({message:"所选教材课程已有章节，不能重复同步！！！",type:"error"}):v.confirm("此操作将批量提取“知识点层级”信息同步为“教材”章节, 是否继续?","提示",{confirmButtonText:"确认同步",cancelButtonText:"取消",type:"warning"}).then((()=>{const e={course_jc_id:C.course_jc_id};t(e).then((e=>{e.data>0?(j({type:"success",message:"同步成功!"+e.data+"条"}),H()):j({type:"info",message:"同步失败!"+e.msg})})).catch((e=>{j({type:"info",message:"同步失败!"+e})}))})).catch((()=>{j({type:"info",message:"取消同步!"})})):j({message:"请先选择教材课程",type:"error"})}),size:"default",round:"",icon:"Pointer","aria-label":""},{default:h((()=>a[3]||(a[3]=[g(" “知识点层级”同步生成“教材”章节 ")]))),_:1,__:[3]},8,["disabled"])]),u("div",z,[y((p(),b(V,{border:"",height:C.tableHeight,data:C.tableDataList,"row-key":"id",class:"moduleTable","default-expand-all":!1,"highlight-current-row":"",ref:"tablexjcTree"},{default:h((()=>[_(i,{prop:"",type:"index",label:"序号",align:"center",width:"40","header-align":"center"}),_(i,{prop:"chapter_code",label:"章编码",align:"center",width:"60"}),_(i,{prop:"chapter_name",label:"章名称",align:"left","header-align":"center","min-width":"160","show-overflow-tooltip":""}),_(i,{prop:"node_code",label:"节编码",align:"center",width:"60"}),_(i,{prop:"node_name",label:"节名称",align:"left","header-align":"center","min-width":"160","show-overflow-tooltip":""}),_(i,{prop:"course_code",label:"课程编码",width:"80",align:"center","header-align":"center"}),_(i,{prop:"course_name",label:"课程名称",width:"140",align:"left","header-align":"center","show-overflow-tooltip":""})])),_:1},8,["height","data"])),[[B,C.loading]])])])}}});export{C as default};
