import{d as e,ac as l,r as a,N as t,K as o,o as u,x as d,a as r,L as n,c as i,b as s,e as c,j as p,w as m,p as v,M as _,h as f,t as h,F as w,f as g,q as b,ad as y,E as V,v as k}from"./index-BERmxY3Y.js";import{h as x,i as C,s as U,j as z}from"./question-DWIQ7W1h.js";import{g as I}from"./exam-jF1-Sa8S.js";import{l as T}from"./loading-Bz4tfNWn.js";const S={class:"quesbank page"},q={class:"header"},B={class:"body"},j={key:0},H={key:1},O={key:1},A={class:"dialog-footer"},N={class:"dialog-footer"},E={style:{display:"flex","flex-direction":"column","justify-content":"center","align-items":"center"}},J=["src"],L=e({__name:"question_temp",setup(e){const L=l.<PERSON>;let R=a();const F=t(),X=a([]),D=a(!1),K=a(!1),M=a(!1),P=o({loading:!1,tableHeight:window.innerHeight-200,keySearch:"",learn_type:0,dataTable:[],total:0,pageSize:30,currentPage:1}),W=a([]),Z=a(""),G=a({code:-1,cost:"",data:"",out:{output:{preview_url:"",result:""}},debug_url:"",msg:"",token:""}),$=a(null),Q=a(1),Y=()=>{P.loading=!0,P.keySearch,P.learn_type,P.currentPage,P.pageSize,x().then((e=>{P.dataTable=e.data||[],P.loading=!1})).catch((e=>{P.loading=!1}))},ee=a({course_base_id:"",title:"",url:"",ex:"doc",doc_type:"真题"}),le=e=>{W.value=e},ae=()=>{P.tableHeight=window.innerHeight-200};u((()=>{Y(),ae(),window.addEventListener("resize",ae)})),d((()=>{window.removeEventListener("resize",ae)}));const te=()=>{D.value=!0,ee.value={course_base_id:"",title:"",url:""},I({page:1,size:99999}).then((e=>{200==e.code&&(X.value=e.data.list,X.value.forEach((e=>{e.id=e.id.toString()})))})).catch((e=>{})),R.value.clearFiles()},oe=e=>{const{success:l,url:a}=e;l?(ee.value.url=a,V.success("上传成功！")):V.error("上传失败！")},ue=async(e,a,t,o)=>{K.value=!0;var u=setInterval((()=>{Q.value=Q.value+1,Q.value%10==0&&re(a)}),1e3);const d={Authorization:"Bearer pat_IWZ9ZDtuuAk92jfrUO1nmnFb9GXqs3pyvdcxXxtvMXNwNboBRf7X4wTt2bJTOIsn","Content-Type":"application/json"},r={workflow_id:"真题"==o?"doc"==t?"7503831708500885514":"7517113904524197907":"7516454105667633171",parameters:{file_url:e,guid:a}};try{const e=await l.post("https://api.coze.cn/v1/workflow/run",r,{headers:d,cancelToken:new L((function(e){$.value=e}))});e&&(G.value=e.data,0==G.value.code?(V.success("分析成功"),Z.value=JSON.stringify(e.data),G.value.out=JSON.parse(G.value.data),M.value=!0,re(a),Y()):V.error("分析失败"))}catch(n){l.isCancel(n)||V.error("分析失败")}finally{K.value=!1,clearInterval(u),Q.value=0}},de=async(e,l,a,t)=>{K.value=!0,setInterval((()=>{Q.value=Q.value+1,Q.value%10==0&&re(l)}),1e3);try{const a={fileurl:e,guid:l},t=await U(a);t&&(G.value=t,200==G.value.code?(V.success("分析成功"),Z.value=JSON.stringify(t.data),G.value.out=JSON.parse(G.value.data),M.value=!0,re(l),Y()):V.error("分析失败"))}catch(o){V.error("分析失败")}finally{K.value=!1,Q.value=0}},re=e=>{z({guid:e}).then((e=>{var l=e.data.total;l>0&&(K.value=!1,alert("解析成功："+l+"题"),Y(),$.value("Operation canceled by the user."))}))},ne=({row:e,rowIndex:l})=>e.import_num>0?"success-row":e.question_num>0?"warning-row":"error-row";return(e,l)=>{const a=r("el-button"),t=r("el-table-column"),o=r("el-link"),u=r("el-table"),d=r("el-option"),x=r("el-select"),U=r("el-form-item"),z=r("el-input"),I=r("el-radio-button"),L=r("el-radio-group"),W=r("el-upload"),$=r("el-form"),ae=r("el-dialog"),re=n("loading");return s(),i("div",S,[c("div",q,[p(a,{type:"primary",onClick:Y,icon:"RefreshRight"}),p(a,{type:"primary",onClick:l[0]||(l[0]=e=>te())},{default:m((()=>l[19]||(l[19]=[v("添加")]))),_:1,__:[19]})]),c("div",B,[_((s(),f(u,{data:P.dataTable,border:"",class:"modTable",height:P.tableHeight,style:{width:"100%"},onSelectionChange:le,"row-class-name":ne},{default:m((()=>[p(t,{type:"index",align:"center"}),p(t,{prop:"kc_mc",label:"所属课程",width:"100",align:"center"},{default:m((e=>[e.row.course_base_id?(s(),i("span",j,h(e.row.kc_mc),1)):(s(),i("span",H,"无课程"))])),_:1}),p(t,{prop:"title",label:"标题",width:"200",align:"center"}),p(t,{prop:"fileurl",label:"文档url",align:"left","show-overflow-tooltip":"","header-align":"center"},{default:m((e=>[p(o,{type:"primary",onClick:l=>{return a=e.row.fileurl,void window.open(`https://view.officeapps.live.com/op/view.aspx?src=${encodeURIComponent(a)}&wdOrigin=BROWSELINK`,"_blank");var a}},{default:m((()=>[v(h(e.row.fileurl),1)])),_:2},1032,["onClick"])])),_:1}),p(t,{prop:"guid",width:"120",label:"guid",align:"center","header-align":"center","show-overflow-tooltip":""}),p(t,{prop:"",width:"120",label:"AI分析",align:"center","header-align":"center"},{default:m((e=>[p(a,{type:"primary",onClick:l=>(async(e,l,a,t)=>{k.confirm("是否进行AI分析，此过程可能耗时比较长，请耐心等待?","提示",{confirmButtonText:"确认",cancelButtonText:"取消",type:"warning"}).then((()=>{ue(e,l,a,t)})).catch((()=>{}))})(e.row.fileurl,e.row.guid,e.row.ex,e.row.doc_type)},{default:m((()=>l[20]||(l[20]=[c("svg",{viewBox:"0 0 1024 1024",width:"20",height:"20"},[c("path",{d:"M371.915599 1023.407835a40.06491 40.06491 0 0 1-34.950241-59.046017 470.435923 470.435923 0 0 0 25.971154-55.977214 340.977959 340.977959 0 0 0 17.048898-51.999139H372.938532a368.597173 368.597173 0 0 1-71.889519 8.979086 194.300607 194.300607 0 0 1-101.89558-29.949231 176.171945 176.171945 0 0 1-65.979235-128.037223 610.009568 610.009568 0 0 1-11.991058-114.966402 55.06794 55.06794 0 0 0-15.912305-11.024954c-10.00202-4.94418-25.00505-12.957162-38.019042-18.981106l-34.097796-15.969134A59.898461 59.898461 0 0 1 0.135964 500.461306c-1.989038-28.017022 18.015002-44.042986 51.999139-77.004189a592.335544 592.335544 0 0 0 49.896441-51.146694 98.428971 98.428971 0 0 0 18.981107-31.938269 301.992812 301.992812 0 0 1 21.993078-115.023231 344.387738 344.387738 0 0 1 72.912454-112.977364 401.274228 401.274228 0 0 1 295.51423-112.011259 306.539185 306.539185 0 0 1 265.735489 157.986454 522.83287 522.83287 0 0 1 64.899472 152.01934 39.837591 39.837591 0 0 1-77.856634 17.048898 437.986188 437.986188 0 0 0-55.977215-128.946498 228.171084 228.171084 0 0 0-196.801112-117.978374 319.155369 319.155369 0 0 0-236.752362 88.995247 244.367537 244.367537 0 0 0-71.88952 169.977513c0.966104 45.009091-48.930337 99.963372-93.882598 142.983424l-9.035916 8.922256h1.022934a353.366824 353.366824 0 0 1 56.82966 30.06289 83.369111 83.369111 0 0 1 42.963223 68.195592 496.577567 496.577567 0 0 0 10.968124 103.998277 120.819857 120.819857 0 0 0 30.972165 76.038085 140.539749 140.539749 0 0 0 114.852742 8.922257l30.00606-4.944181a61.83067 61.83067 0 0 1 61.8875 23.982117 158.782069 158.782069 0 0 1-8.012982 130.992365 743.729757 743.729757 0 0 1-32.904373 72.969284 39.780762 39.780762 0 0 1-36.996109 22.049908z m609.327611-485.041146h-161.850871a39.780762 39.780762 0 0 1-27.903362-12.218377l-138.834859-137.982414H461.87695a40.00808 40.00808 0 1 1 0-79.959331h207.826066a39.780762 39.780762 0 0 1 27.903363 11.991058l138.891688 137.982414h144.858803a40.00808 40.00808 0 1 1 0 79.959331z m-297.673757 229.989633H507.738485a40.00808 40.00808 0 1 1 0-80.016161h159.123048l82.914473-82.971303a39.780762 39.780762 0 0 1 27.960193-11.991058h147.757115a40.00808 40.00808 0 1 1 0 80.016161h-129.912602l-82.857644 82.971303a40.12174 40.12174 0 0 1-28.983126 11.991058z m-87.915484-187.992515h-193.78914a40.00808 40.00808 0 1 1 0-79.959331h193.78914a40.00808 40.00808 0 1 1 0 79.959331z m0 0",fill:"#00D0BD","p-id":"12706"})],-1),v("   AI解析 ")]))),_:2,__:[20]},1032,["onClick"])])),_:1}),p(t,{prop:"",width:"120",label:"本地读取",align:"center","header-align":"center"},{default:m((e=>[p(a,{type:"success",onClick:l=>(async(e,l)=>{k.confirm("是否进行文档读取，此过程可能耗时比较长，请耐心等待?","提示",{confirmButtonText:"确认",cancelButtonText:"取消",type:"warning"}).then((()=>{de(e,l)})).catch((()=>{}))})(e.row.fileurl,e.row.guid,e.row.ex,e.row.doc_type)},{default:m((()=>l[21]||(l[21]=[v(" 本地解析 ")]))),_:2,__:[21]},1032,["onClick"])])),_:1}),p(t,{prop:"create_date",width:"100",label:"预览",align:"center","header-align":"center"},{default:m((e=>[e.row.question_num>0?(s(),f(a,{key:0,type:"primary",onClick:l=>(e=>{const{href:l}=F.resolve({path:"/temp_question",query:{guid:e}});window.open(l,"_blank")})(e.row.guid)},{default:m((()=>l[22]||(l[22]=[v("预览 ")]))),_:2,__:[22]},1032,["onClick"])):(s(),i("div",O))])),_:1}),p(t,{prop:"question_num",width:"50",label:"题目数",align:"center","header-align":"center"}),p(t,{prop:"import_num",width:"80",label:"导入题库数",align:"center","header-align":"center"}),p(t,{prop:"create_date",label:"创建日期",width:"150",align:"center","header-align":"center"}),p(t,{prop:"name",label:"创建人",width:"130",align:"center","header-align":"center"})])),_:1},8,["data","height"])),[[re,P.loading]])]),p(ae,{modelValue:D.value,"onUpdate:modelValue":l[7]||(l[7]=e=>D.value=e),title:"添加文档",width:"600"},{footer:m((()=>[c("div",A,[p(a,{onClick:l[5]||(l[5]=e=>D.value=!1)},{default:m((()=>l[26]||(l[26]=[v("关闭")]))),_:1,__:[26]}),p(a,{type:"primary",onClick:l[6]||(l[6]=e=>(()=>{if(!ee.value.title||!ee.value.url)return V.error("缺少参数"),!1;C(ee.value).then((e=>{e.data>0?(V.success("保存成功！"),D.value=!1,ee.value={course_base_id:"",title:"",url:"",ex:"doc",doc_type:"真题"},R.value.clearFiles(),Y()):V.error("保存失败！")}))})())},{default:m((()=>l[27]||(l[27]=[v(" 保存 ")]))),_:1,__:[27]})])])),default:m((()=>[p($,{"label-position":"right","label-width":"auto",model:ee.value,style:{"max-width":"600px"}},{default:m((()=>[p(U,{label:"课程"},{default:m((()=>[p(x,{modelValue:ee.value.course_base_id,"onUpdate:modelValue":l[1]||(l[1]=e=>ee.value.course_base_id=e),filterable:"",placeholder:"选择课程",size:"large",style:{width:"240px"}},{default:m((()=>[(s(!0),i(w,null,g(X.value,(e=>(s(),f(d,{key:e.id,label:"【"+e.kc_bm+"】"+e.kc_mc,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),p(U,{label:"标题"},{default:m((()=>[p(z,{modelValue:ee.value.title,"onUpdate:modelValue":l[2]||(l[2]=e=>ee.value.title=e)},null,8,["modelValue"])])),_:1}),p(U,{label:"类型"},{default:m((()=>[p(L,{modelValue:ee.value.ex,"onUpdate:modelValue":l[3]||(l[3]=e=>ee.value.ex=e)},{default:m((()=>[p(I,{label:"doc",value:"doc"}),p(I,{label:"pdf",value:"pdf"})])),_:1},8,["modelValue"])])),_:1}),p(U,{label:"是否含有章节"},{default:m((()=>[p(L,{modelValue:ee.value.doc_type,"onUpdate:modelValue":l[4]||(l[4]=e=>ee.value.doc_type=e)},{default:m((()=>[p(I,{label:"真题",value:"真题"}),p(I,{label:"章节题",value:"章节题"})])),_:1},8,["modelValue"])])),_:1}),p(U,{label:"本地解析word模板"},{default:m((()=>[p(o,{type:"primary",href:"https://cdn8a.swufe-online.com/oc/mfile/public-comfyui/75d95a09-0b15-41be-8694-ad398ae7c04b.docx"},{default:m((()=>l[23]||(l[23]=[v("下载本地解析wrod模板")]))),_:1,__:[23]})])),_:1}),p(U,{label:"文件上传"},{default:m((()=>[p(W,{ref_key:"uploadrefss",ref:R,class:"upload-demo",action:"https://xczx7.swufe.edu.cn/oc/xczk/file/upload_to_drive",headers:{Authorization:"Bearer "+b(y)()},method:"post",multiple:"","on-success":oe},{tip:m((()=>l[25]||(l[25]=[c("div",{class:"el-upload__tip"},null,-1)]))),default:m((()=>[p(a,{type:"primary"},{default:m((()=>l[24]||(l[24]=[v("文件上传")]))),_:1,__:[24]})])),_:1},8,["headers"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue"]),p(ae,{modelValue:M.value,"onUpdate:modelValue":l[17]||(l[17]=e=>M.value=e),title:"添加文档",width:"600"},{footer:m((()=>[c("div",N,[p(a,{onClick:l[16]||(l[16]=e=>M.value=!1)},{default:m((()=>l[28]||(l[28]=[v("Cancel")]))),_:1,__:[28]})])])),default:m((()=>[p($,{"label-position":"right","label-width":"auto",model:ee.value,style:{"max-width":"600px"}},{default:m((()=>[p(U,{label:"code"},{default:m((()=>[p(z,{modelValue:G.value.code,"onUpdate:modelValue":l[8]||(l[8]=e=>G.value.code=e)},null,8,["modelValue"])])),_:1}),p(U,{label:"cost"},{default:m((()=>[p(z,{modelValue:G.value.cost,"onUpdate:modelValue":l[9]||(l[9]=e=>G.value.cost=e)},null,8,["modelValue"])])),_:1}),p(U,{label:"预览题库"},{default:m((()=>[p(z,{modelValue:G.value.out.output.preview_url,"onUpdate:modelValue":l[10]||(l[10]=e=>G.value.out.output.preview_url=e)},null,8,["modelValue"])])),_:1}),p(U,{label:"题目数量"},{default:m((()=>[p(z,{modelValue:G.value.out.output.result,"onUpdate:modelValue":l[11]||(l[11]=e=>G.value.out.output.result=e)},null,8,["modelValue"])])),_:1}),p(U,{label:"debug_url"},{default:m((()=>[p(z,{modelValue:G.value.debug_url,"onUpdate:modelValue":l[12]||(l[12]=e=>G.value.debug_url=e)},null,8,["modelValue"])])),_:1}),p(U,{label:"msg"},{default:m((()=>[p(z,{modelValue:G.value.msg,"onUpdate:modelValue":l[13]||(l[13]=e=>G.value.msg=e)},null,8,["modelValue"])])),_:1}),p(U,{label:"token"},{default:m((()=>[p(z,{modelValue:G.value.token,"onUpdate:modelValue":l[14]||(l[14]=e=>G.value.token=e)},null,8,["modelValue"])])),_:1}),p(U,{label:"返回数据"},{default:m((()=>[p(z,{modelValue:Z.value,"onUpdate:modelValue":l[15]||(l[15]=e=>Z.value=e),style:{width:"100%"},rows:2,type:"textarea",placeholder:"Please input"},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue"]),p(ae,{"close-on-click-modal":!1,"close-on-press-escape":!1,"header-class":"loadingHader",modelValue:K.value,"onUpdate:modelValue":l[18]||(l[18]=e=>K.value=e),width:"300"},{default:m((()=>[c("div",E,[c("img",{src:b(T),style:{width:"50px"}},null,8,J),v(" AI解析中...."+h(Q.value),1)])])),_:1},8,["modelValue"])])}}});export{L as default};
