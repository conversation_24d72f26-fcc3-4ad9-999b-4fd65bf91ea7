import{g as e,k as l,h as a,i as t}from"./exam-jF1-Sa8S.js";import{d as o,r as d,o as i,E as s,a as u,L as n,c as r,b as p,e as c,M as v,j as m,w as _,p as g,h,t as f,F as y,f as b,v as x}from"./index-BERmxY3Y.js";const k={class:"kcmgr",style:{padding:"5px"}},w={style:{padding:"5px 50px!important"}},V={style:{padding:"5px 50px 30px 50px!important"}},z={style:{"margin-top":"15px","text-align":"center",width:"100%",display:"flex","justify-content":"center"}},C={style:{height:"500px"}},U={style:{padding:"10px 20px"}},F=["src"],D={style:{height:"500px"}},j={class:"dialog-footer"},S={class:"m-2",style:{display:"flex","justify-content":"center"}},E={style:{padding:"10px 20px"}},L={style:{padding:"10px 0px"}},M={style:{color:"#409EFF","font-weight":"bold"}},O={style:{padding:"0px 20px",height:"380px",width:"610px","overflow-y":"auto"}},T=["onDragstart","onDragenter"],B={style:{width:"100%",display:"flex","align-items":"center"}},P={style:{"margin-left":"15px"}},A={style:{"margin-left":"40px","margin-top":"1px"},class:"cj_ksplan-item"},H={class:"dialog-footer",style:{padding:"10px 20px","text-align":"center","border-top":"1px solid #F2F6FC"}},R=o({__name:"kcmgr",setup(o){const R=d([]);d({});const J=d(),K=d(window.innerHeight-200),N=d({}),$=d(!1),q=d(0),G=d("");i((()=>{W()}));const I=d({id:"",kc_bm:"",kc_mc:"",kc_hour:"",status:"0",cover:"",kc_introduction:""});d("");const Q=d(!1),W=()=>{$.value=!0,e({page:se.value,size:ie.value}).then((e=>{200==e.code?($.value=!1,G.value?R.value=e.data.list.filter((e=>e.learn_type==G.value)):R.value=e.data.list,q.value=e.data.total):s.error(e.msg)}))},X=e=>{J.value=e},Y=(e,l)=>{200==e.code?(s.success(e.msg),I.value.cover=e.data):s.error(e.msg)},Z=d(!1),ee=d([]),le=d([]),ae=d([]);let te=0;function oe(e){e.preventDefault(),e.dataTransfer.dropEffect="move"}function de(e){e.target.classList.remove("moveing")}const ie=d(30),se=d(1),ue=e=>{ie.value=e,W()},ne=e=>{se.value=e,W()};return(e,o)=>{const d=u("el-button"),i=u("el-option"),re=u("el-select"),pe=u("el-table-column"),ce=u("H3"),ve=u("mavon-editor"),me=u("EditPen"),_e=u("el-icon"),ge=u("el-link"),he=u("el-tag"),fe=u("el-table"),ye=u("el-pagination"),be=u("el-form-item"),xe=u("el-input"),ke=u("el-input-number"),we=u("el-radio"),Ve=u("el-radio-group"),ze=u("Plus"),Ce=u("el-upload"),Ue=u("el-form"),Fe=u("el-tab-pane"),De=u("el-tabs"),je=u("el-dialog"),Se=u("el-transfer"),Ee=n("loading");return p(),r("div",k,[c("div",null,[m(d,{type:"primary",onClick:W,icon:"RefreshRight"}),m(d,{type:"primary",onClick:o[0]||(o[0]=e=>(I.value={id:"",kc_bm:"",kc_mc:"",kc_hour:"",status:"1",cover:"",learn_type:"",kc_introduction:""},void(Q.value=!0))),plain:"",icon:"DocumentAdd"},{default:_((()=>o[18]||(o[18]=[g("添加")]))),_:1,__:[18]}),m(d,{type:"danger",onClick:o[1]||(o[1]=e=>{J.value?x.confirm("此操作将永久删除该课程, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{a(J.value.id).then((e=>{200==e.code?(s.success(e.msg),W()):s.error(e.msg)}))})).catch((()=>{s({type:"info",message:"已取消删除"})})):s.warning("请先选择要删除的课程")}),plain:"",icon:"DeleteFilled"},{default:_((()=>o[19]||(o[19]=[g("删除")]))),_:1,__:[19]}),m(re,{modelValue:G.value,"onUpdate:modelValue":o[2]||(o[2]=e=>G.value=e),class:"m-2",placeholder:"筛选学习类型",style:{width:"190px","margin-left":"5px"},onChange:o[3]||(o[3]=e=>W())},{default:_((()=>[m(i,{key:"1",label:"成人高考",value:"1"}),m(i,{key:"2",label:"自考",value:"2"}),m(i,{key:"3",label:"微课",value:"3"})])),_:1},8,["modelValue"])]),v((p(),h(fe,{border:"",height:K.value,data:R.value,size:"mini",stripe:"",style:{width:"100%","margin-top":"10px"},"highlight-current-row":"",onCurrentChange:X},{default:_((()=>[m(pe,{type:"index",width:"50",align:"center"}),m(pe,{type:"expand"},{default:_((e=>[c("div",w,[m(ce,null,{default:_((()=>o[20]||(o[20]=[g("课程简介")]))),_:1,__:[20]})]),c("div",V,[m(ve,{subfield:!1,defaultOpen:"preview",editable:!1,toolbarsFlag:!1,boxShadow:!1,xssOptions:{},html:!1,modelValue:e.row.kc_introduction,"onUpdate:modelValue":l=>e.row.kc_introduction=l},null,8,["modelValue","onUpdate:modelValue"])])])),_:1}),m(pe,{prop:"learn_type_name",width:"80",label:"学习类型",align:"center"}),m(pe,{prop:"kc_bm",label:"课程编码",width:"180",align:"center"}),m(pe,{prop:"kc_mc",label:"课程名称",width:"300"},{default:_((e=>[m(ge,{onClick:l=>{return a=e.$index,I.value=R.value[a],I.value.status=I.value.status.toString(),I.value.learn_type=I.value.learn_type.toString(),void(Q.value=!0);var a},type:"primary"},{default:_((()=>[g(f(e.row.kc_mc)+"  ",1),m(_e,{style:{"font-size":"13px"}},{default:_((()=>[m(me)])),_:1})])),_:2},1032,["onClick"])])),_:1}),m(pe,{prop:"online_num",label:"线上课程数",align:"center"},{default:_((e=>[g(f(e.row.online_num)+"/"+f(e.row.pub_num),1)])),_:1}),m(pe,{prop:"status",label:"状态",align:"center"},{default:_((e=>[1==e.row.status?(p(),h(he,{key:0,type:"success"},{default:_((()=>o[21]||(o[21]=[g("正常")]))),_:1,__:[21]})):(p(),h(he,{key:1,type:"danger"},{default:_((()=>o[22]||(o[22]=[g("禁用")]))),_:1,__:[22]}))])),_:1}),m(pe,{prop:"create_name",label:"创建人",align:"center"}),m(pe,{prop:"create_date",label:"创建时间",align:"center"})])),_:1},8,["height","data"])),[[Ee,$.value]]),c("div",z,[m(ye,{onSizeChange:ue,onCurrentChange:ne,"current-page":se.value,"hide-on-single-page":!0,background:!0,"page-sizes":[10,20,50,100],"page-size":ie.value,layout:"total, sizes, prev, pager, next, jumper",total:q.value},null,8,["current-page","page-size","total"])]),m(je,{modelValue:Q.value,"onUpdate:modelValue":o[12]||(o[12]=e=>Q.value=e),title:"添加课程",width:"800",top:"6vh","append-to-body":!0,"close-on-click-modal":!1,"close-on-press-escape":!1},{footer:_((()=>[c("span",j,[m(d,{onClick:o[10]||(o[10]=e=>Q.value=!1)},{default:_((()=>o[25]||(o[25]=[g("取消")]))),_:1,__:[25]}),m(d,{type:"primary",onClick:o[11]||(o[11]=e=>(I.value.id=I.value.id?I.value.id:0,void l(I.value).then((e=>{"200"==e.code?(s.success(e.msg),W()):s.error(e.msg)})))),disabled:!I.value.kc_bm||!I.value.kc_mc||!I.value.kc_hour},{default:_((()=>o[26]||(o[26]=[g(" 确认 ")]))),_:1,__:[26]},8,["disabled"])])])),default:_((()=>[c("div",C,[m(De,{"tab-position":"left",style:{height:"500px"},class:"demo-tabs"},{default:_((()=>[m(Fe,{label:"基本设置"},{default:_((()=>[c("div",U,[m(Ue,{inline:!0,size:"large",model:I.value,class:"demo-form-inline"},{default:_((()=>[m(be,{label:"学习类型"},{default:_((()=>[m(re,{modelValue:I.value.learn_type,"onUpdate:modelValue":o[4]||(o[4]=e=>I.value.learn_type=e),class:"m-2",style:{width:"170px"},placeholder:"Select",size:"large"},{default:_((()=>[m(i,{key:"6",label:"自考",value:"6"}),m(i,{key:"5",label:"成人高考",value:"5"}),m(i,{key:"99",label:"微课",value:"99"})])),_:1},8,["modelValue"])])),_:1}),m(be,{label:"课程编码"},{default:_((()=>[m(xe,{style:{width:"200px"},modelValue:I.value.kc_bm,"onUpdate:modelValue":o[5]||(o[5]=e=>I.value.kc_bm=e),placeholder:"课程编码",clearable:""},null,8,["modelValue"])])),_:1}),m(be,{label:"课程名称"},{default:_((()=>[m(xe,{size:"large",modelValue:I.value.kc_mc,"onUpdate:modelValue":o[6]||(o[6]=e=>I.value.kc_mc=e),style:{width:"170px"},placeholder:"课程名称",clearable:""},null,8,["modelValue"])])),_:1}),m(be,{label:"课程时长"},{default:_((()=>[m(ke,{modelValue:I.value.kc_hour,"onUpdate:modelValue":o[7]||(o[7]=e=>I.value.kc_hour=e),controls:!1,style:{width:"200px"},placeholder:"课程时长",clearable:""},null,8,["modelValue"])])),_:1}),m(be,{label:"课程状态"},{default:_((()=>[m(Ve,{modelValue:I.value.status,"onUpdate:modelValue":o[8]||(o[8]=e=>I.value.status=e)},{default:_((()=>[m(we,{label:"1",size:"large",border:""},{default:_((()=>o[23]||(o[23]=[g("启用")]))),_:1,__:[23]}),m(we,{label:"0",size:"large",border:""},{default:_((()=>o[24]||(o[24]=[g("禁用")]))),_:1,__:[24]})])),_:1},8,["modelValue"])])),_:1}),m(be,{label:"封面",style:{"align-items":"center","font-weight":"bold"}},{default:_((()=>[m(Ce,{class:"avatar-uploader",action:"https://xczx7.swufe.edu.cn/oc/xczk/examplan/upload","show-file-list":!1,"on-success":Y},{default:_((()=>[I.value.cover?(p(),r("img",{key:0,style:{width:"108px",height:"108px"},src:"https://xczx7.swufe.edu.cn/oc/xczk/examplan/getFile?path="+I.value.cover,class:"avatar"},null,8,F)):(p(),h(_e,{key:1,class:"avatar-uploader-icon"},{default:_((()=>[m(ze)])),_:1}))])),_:1})])),_:1})])),_:1},8,["model"])])])),_:1}),m(Fe,{label:"课程简介"},{default:_((()=>[c("div",D,[m(ve,{boxShadow:!1,xssOptions:{},html:!1,modelValue:I.value.kc_introduction,"onUpdate:modelValue":o[9]||(o[9]=e=>I.value.kc_introduction=e)},null,8,["modelValue"])])])),_:1})])),_:1})])])),_:1},8,["modelValue"]),m(je,{modelValue:Z.value,"onUpdate:modelValue":o[17]||(o[17]=e=>Z.value=e),width:"700","close-on-click-modal":!1,"close-on-press-escape":!1,"append-to-body":!0,top:"6vh"},{default:_((()=>[c("div",S,[m(De,{"tab-position":"top",style:{},class:"demo-tabs"},{default:_((()=>[m(Fe,{label:"板块设置"},{default:_((()=>[c("div",E,[c("div",L,[o[27]||(o[27]=g(" 为【")),c("span",M,f(N.value.kc_mc),1),o[28]||(o[28]=g("】课程指定板块 "))]),m(Se,{modelValue:le.value,"onUpdate:modelValue":o[13]||(o[13]=e=>le.value=e),data:ee.value,titles:["未开通板块","已开通板块"],onClick:o[14]||(o[14]=e=>(ae.value=[],void(le.value.length>0&&ee.value.forEach((e=>{le.value.indexOf(e.id)>-1&&(e.is_list=e.is_list?e.is_list:0,e.is_blank=e.is_blank?e.is_blank:0,e.show_progress=e.show_progress?e.show_progress:0,ae.value.push(e))})))))},null,8,["modelValue","data"])])])),_:1}),m(Fe,{label:"板块详细设置"},{default:_((()=>[c("div",O,[(p(!0),r(y,null,b(ae.value,((e,l)=>(p(),r("div",{class:"item",key:e.id,draggable:"true",onDragstart:e=>{return t=l,(a=e).stopPropagation(),te=t,void setTimeout((()=>{a.target.classList.add("moveing")}),0);var a,t},onDragenter:e=>function(e,l){if(e.preventDefault(),te!==l){const e=ae.value[te];ae.value.splice(te,1),ae.value.splice(l,0,e),te=l}}(e,l),onDragend:de,onDragover:oe},[c("div",B,[o[29]||(o[29]=c("div",{style:{"margin-top":"4px"},class:"dragCurr"},[c("svg",{viewBox:"0 0 1024 1024",width:"30",height:"30"},[c("path",{d:"M512 0a512 512 0 0 1 512 512 512 512 0 0 1-512 512A512 512 0 0 1 0 512 512 512 0 0 1 512 0z",fill:"#FFFFFF","p-id":"7425"}),c("path",{d:"M509.5424 720.6912L593.92 636.5184l35.2256 35.2256-119.1936 118.784-118.784-118.784 35.2256-35.2256zM509.952 245.76l118.784 118.784-34.816 35.2256-83.7632-84.1728-84.1728 84.1728L389.12 364.544l119.808-118.784zM307.2 482.304v-49.7664h409.6v49.7664z m0 112.8448v-49.7664h409.6v49.7664z",fill:"#2693FF","p-id":"7426"})])],-1)),c("div",P," 【"+f(l+1)+"】 "+f(e.title),1)]),c("div",A,[m(re,{modelValue:e.is_list,"onUpdate:modelValue":l=>e.is_list=l,placeholder:"展示方式",size:"mini",style:{width:"100px"}},{default:_((()=>[(p(),h(i,{key:1,label:"有列表",value:1})),(p(),h(i,{key:0,label:"无列表",value:0}))])),_:2},1032,["modelValue","onUpdate:modelValue"]),m(re,{modelValue:e.is_blank,"onUpdate:modelValue":l=>e.is_blank=l,placeholder:"打开方式",size:"mini",style:{width:"150px","margin-left":"15px"}},{default:_((()=>[(p(),h(i,{key:1,label:"新标签页打开",value:1})),(p(),h(i,{key:0,label:"当前页打开",value:0}))])),_:2},1032,["modelValue","onUpdate:modelValue"]),m(re,{modelValue:e.show_progress,"onUpdate:modelValue":l=>e.show_progress=l,placeholder:"显示进度条",size:"mini",style:{width:"150px","margin-left":"15px"}},{default:_((()=>[(p(),h(i,{key:1,label:"显示进度条",value:1})),(p(),h(i,{key:0,label:"不显示进度条",value:0}))])),_:2},1032,["modelValue","onUpdate:modelValue"])])],40,T)))),128))])])),_:1})])),_:1})]),c("div",H,[m(d,{onClick:o[15]||(o[15]=e=>Z.value=!1)},{default:_((()=>o[30]||(o[30]=[g("取消")]))),_:1,__:[30]}),m(d,{type:"primary",onClick:o[16]||(o[16]=e=>(()=>{var e=[];if(ae.value.forEach(((l,a)=>{var t={plate_id:l.id,is_list:l.is_list,is_blank:l.is_blank,show_progress:l.show_progress,sn:a+1};e.push(t)})),0!=e.length){var l={id:N.value.id,jsonstr:JSON.stringify(e)};t(l).then((e=>{"200"==e.code?(s.success(e.msg),W()):s.error(e.msg)}))}else s.warning("请至少选择一个板块")})())},{default:_((()=>o[31]||(o[31]=[g("保存 ")]))),_:1,__:[31]})])])),_:1},8,["modelValue"])])}}});export{R as default};
