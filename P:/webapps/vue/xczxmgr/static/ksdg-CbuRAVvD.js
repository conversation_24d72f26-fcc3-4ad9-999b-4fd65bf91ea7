import{d as e,K as l,r as a,N as o,o as i,a as t,L as d,c as n,b as s,e as r,j as u,h as c,m as p,w as f,p as g,F as v,f as m,t as _,M as w,q as h,O as b,E as y,P as x,_ as k}from"./index-BERmxY3Y.js";import{f as C,h as V,j as S,u as z,s as L,d as j}from"./dzs-DZN_gNG7.js";import{m as U}from"./message-Df6PNwXY.js";import{_ as A}from"./preview_url.vue_vue_type_style_index_0_lang-uW4b37j2.js";const F={class:"page ksdg_page"},M={class:"header"},D={style:{float:"left"}},T={style:{float:"right",color:"#67C23A","font-size":"13px"}},q={class:"body"},N={style:{float:"left"}},O={style:{float:"right",color:"#67C23A","font-size":"13px"}},R={key:0,class:"upload-file-info"},I={class:"dialog-footer"},B={style:{float:"left"}},X={style:{float:"right",color:"#67C23A","font-size":"13px"}},E={key:0,class:"upload-file-info"},J={class:"dialog-footer"},K=["element-loading-text","element-loading-spinner"],P={style:{width:"100%"}},G={class:"editor_div"},H={class:"dialog-footer"},Q={style:{width:"100%"}},W={class:"editor_div"},Y={class:"dialog-footer"},Z=k(e({__name:"ksdg",setup(e){const k=l({dialogShow:!1,title:"",loading:!1,loadingText:"获取数据中，请稍后...",loadingSvg:'\n        <path class="path" d="\n          M 30 15\n          L 28 17\n          M 25.61 25.61\n          A 15 15, 0, 0, 1, 15 30\n          A 15 15, 0, 1, 1, 27.99 7.5\n          L 15 15\n        " style="stroke-width: 4px; fill: rgba(0, 0, 0, 0)"/>\n      ',content:"",id:0}),Z=a([]),$=a({pageLoading:!1,pageLoadingText:"获取数据中，请稍后...",pageLoadingSvg:'\n        <path class="path" d="\n          M 30 15\n          L 28 17\n          M 25.61 25.61\n          A 15 15, 0, 0, 1, 15 30\n          A 15 15, 0, 1, 1, 27.99 7.5\n          L 15 15\n        " style="stroke-width: 4px; fill: rgba(0, 0, 0, 0)"/>\n      ',selected_course_id:null});o();const ee=a(!1),le=a(null),ae=a({visible:!1,progress:0,uploading:!1,form:{title:"",file:null,course_id:null}}),oe=l({visible:!1,content:""}),ie=a({visible:!1,progress:0,uploading:!1,form:{id:null,title:"",file:null,course_id:null,url:""}}),te=a([]),de=async()=>{try{const e=await j();200===e.code?te.value=e.data:U(e.msg||"获取课程列表失败","warning")}catch(e){U("获取课程列表失败","error")}},ne=()=>({height:"40px"}),se=()=>({padding:"4px 0"}),re=(e,l)=>{if(!e)return"-";const a=te.value.find((l=>Number(l.id)===Number(e)));return(null==a?void 0:a[l])||"-"},ue=async()=>{$.value.pageLoading=!0;try{await de();const e={course_id:$.value.selected_course_id},l=await S(e);200===l.code?Z.value=l.data||[]:U(l.msg||"获取考试大纲列表失败","warning")}catch(e){U("获取数据失败","error")}finally{$.value.pageLoading=!1}},ce=async()=>{await de(),ae.value.visible=!0,ae.value.form={title:"",file:null,course_id:null}},pe=e=>{const l=e.raw||e.file;return"application/vnd.openxmlformats-officedocument.wordprocessingml.document"===l.type?(ae.value.form.file=l,ae.value.form.title=l.name.split(".")[0],!1):(U("只能上传DOCX 格式的文件！","warning"),!1)},fe=a(),ge=e=>{fe.value.clearFiles();const l=e[0];l.uid=x(),fe.value.handleStart(l)},ve=e=>e+"%",me=async()=>{if(ae.value.form.title)if(ae.value.form.file)if(ae.value.form.course_id){ae.value.uploading=!0,ae.value.progress=0;try{const e=new FormData;e.append("file",ae.value.form.file);const l=await z(e);if(l.success){const e=await L({id:0,course_jc_id:ae.value.form.course_id,title:ae.value.form.title,url:l.url});200===e.code?(U("上传成功","success"),ae.value.visible=!1,ue()):U(e.msg||"保存数据失败","error")}else U("文件上传失败","error")}catch(e){U("上传失败","error")}finally{ae.value.uploading=!1,ae.value.progress=0}}else U("请选择所属教材课程","warning");else U("请选择要上传的文件","warning");else U("请输入标题","warning")},_e=e=>{switch(e){case"Success":return"success";case"Fail":return"danger";case"Running":return"warning";default:return"info"}},we=e=>{switch(e){case"Success":return"成功";case"Fail":return"失败";case"Running":return"执行中";default:return"未执行"}},he=a(),be=e=>{const l=e.raw||e.file;return"application/vnd.openxmlformats-officedocument.wordprocessingml.document"===l.type?(ie.value.form.file=l,!1):(U("只能上传 DOCX 格式的文件！","warning"),!1)},ye=e=>{he.value.clearFiles();const l=e[0];l.uid=x(),he.value.handleStart(l)},xe=async()=>{if(ie.value.form.title)if(ie.value.form.course_id){ie.value.uploading=!0,ie.value.progress=0;try{let e=ie.value.form.url,l=0;if(ie.value.form.file){const a=new FormData;a.append("file",ie.value.form.file);const o=await z(a);if(!o.success)return void U("文件上传失败","error");e=o.url,l=1}const a=await L({id:ie.value.form.id,course_jc_id:ie.value.form.course_id,title:ie.value.form.title,url:e,url_change:l});200===a.code?(U("更新成功","success"),ie.value.visible=!1,ue()):U(a.msg||"更新失败","error")}catch(e){U("更新失败","error")}finally{ie.value.uploading=!1,ie.value.progress=0}}else U("请选择所属课程","warning");else U("请输入标题","warning")};return i((()=>{ue()})),(e,l)=>{const a=t("el-button"),o=t("el-option"),i=t("el-select"),x=t("el-table-column"),S=t("el-tooltip"),z=t("el-tag"),L=t("el-table"),j=t("el-form-item"),de=t("el-icon"),ke=t("el-upload"),Ce=t("el-progress"),Ve=t("el-form"),Se=t("el-dialog"),ze=t("v-md-editor"),Le=t("v-md-preview"),je=d("loading");return s(),n("div",F,[r("div",M,[u(a,{plain:"",type:"success",size:"small",onClick:ue,icon:"refresh"},{default:f((()=>l[14]||(l[14]=[g("刷新 ")]))),_:1,__:[14]}),u(i,{modelValue:$.value.selected_course_id,"onUpdate:modelValue":l[0]||(l[0]=e=>$.value.selected_course_id=e),placeholder:"请选择课程教材",filterable:"",style:{width:"260px"}},{default:f((()=>[(s(!0),n(v,null,m(te.value,(e=>(s(),c(o,{key:e.id,label:"[ "+e.course_code+" ] "+e.course_name,value:e.id},{default:f((()=>[r("span",D,_("[ "+e.course_code+" ] "+e.course_name),1),r("span",T,_(e.ver),1)])),_:2},1032,["label","value"])))),128))])),_:1},8,["modelValue"]),u(a,{type:"primary",size:"small",onClick:ce,icon:"upload"},{default:f((()=>l[15]||(l[15]=[g("上传考试大纲 ")]))),_:1,__:[15]})]),r("div",q,[w((s(),c(L,{data:Z.value,width:"100%",height:"calc(100vh - 175px)",ref:"multipleTable","highlight-current-row":"",size:"small",border:"","element-loading-text":$.value.pageLoadingText,"element-loading-spinner":$.value.pageLoadingSvg,"element-loading-svg-view-box":"-10, -10, 50, 50","element-loading-background":"rgba(122, 122, 122, 0.8)","row-style":ne,"cell-style":se},{default:f((()=>[u(x,{prop:"",type:"index",label:"序号",align:"center",width:"60","header-align":"center"}),u(x,{label:"课程编码",align:"center",width:"100","header-align":"center",prop:"course_code","show-overflow-tooltip":""},{default:f((e=>[g(_(re(e.row.course_jc_id,"course_code")),1)])),_:1}),u(x,{label:"课程名称",align:"left","min-width":"100","header-align":"center",prop:"course_name","show-overflow-tooltip":""},{default:f((e=>[g(_(re(e.row.course_jc_id,"course_name")),1)])),_:1}),u(x,{label:"教材版本",align:"center",width:"80","header-align":"center",prop:"ver","show-overflow-tooltip":""},{default:f((e=>[g(_(re(e.row.course_jc_id,"ver")),1)])),_:1}),u(x,{label:"标题",align:"left","min-width":"200","header-align":"center","show-overflow-tooltip":"",prop:"title"}),u(x,{label:"内容",align:"center",width:"120","header-align":"center"},{default:f((e=>[u(S,{content:"关闭时自动保存",placement:"top"},{default:f((()=>[e.row.content?(s(),c(a,{key:0,size:"small",type:"primary",onClick:l=>{return a=e.row,k.title="内容编辑",k.dialogShow=!0,k.content=a.content,void(k.id=a.id);var a}},{default:f((()=>l[16]||(l[16]=[g(" 编辑 ")]))),_:2,__:[16]},1032,["onClick"])):p("",!0)])),_:2},1024)])),_:1}),u(x,{label:"文档URL","min-width":"100",align:"center","header-align":"center","show-overflow-tooltip":""},{default:f((e=>[u(a,{link:"",type:"primary",size:"small",onClick:l=>{return a=e.row.url,le.value=a,void(ee.value=!0);var a}},{default:f((()=>l[17]||(l[17]=[g("查看")]))),_:2,__:[17]},1032,["onClick"])])),_:1}),u(x,{label:"创建时间",align:"center",width:"200","header-align":"center",prop:"create_date"}),u(x,{label:"创建人",align:"center",width:"100","header-align":"center",prop:"create_name"}),u(x,{label:"执行状态",align:"center",width:"100","header-align":"center"},{default:f((e=>[u(z,{type:_e(e.row.execute_status)},{default:f((()=>[g(_(we(e.row.execute_status)),1)])),_:2},1032,["type"])])),_:1}),u(x,{label:"操作",align:"center",width:"200","header-align":"center"},{default:f((e=>["Success"!==e.row.execute_status?(s(),c(a,{key:0,size:"small",type:"primary",icon:"magic-stick",disabled:!(!e.row.execute_id||"Running"!==e.row.execute_status),onClick:l=>(async e=>{try{const l={url:e.url,id:e.id},a=await V(l);200===a.code?ue():U(a.msg||"AI内容获取失败","warning")}catch(l){U("AI内容获取失败","error")}})(e.row)},{default:f((()=>l[18]||(l[18]=[g("文档转换 ")]))),_:2,__:[18]},1032,["disabled","onClick"])):p("",!0),"Success"===e.row.execute_status?(s(),c(a,{key:1,size:"small",type:"info",icon:"view",onClick:l=>{return a=e.row,oe.content=a.content,void(oe.visible=!0);var a}},{default:f((()=>l[19]||(l[19]=[g("预览 ")]))),_:2,__:[19]},1032,["onClick"])):p("",!0),u(a,{size:"small",type:"warning",icon:"edit",onClick:l=>{return a=e.row,ie.value.visible=!0,void(ie.value.form={id:a.id,title:a.title,file:null,course_id:a.course_jc_id,url:a.url});var a}},{default:f((()=>l[20]||(l[20]=[g("重传 ")]))),_:2,__:[20]},1032,["onClick"])])),_:1})])),_:1},8,["data","element-loading-text","element-loading-spinner"])),[[je,$.value.pageLoading]])]),u(Se,{modelValue:ae.value.visible,"onUpdate:modelValue":l[3]||(l[3]=e=>ae.value.visible=e),title:"上传考试大纲",width:"600px",draggable:"","close-on-click-modal":!1,"close-on-press-escape":!ae.value.uploading},{footer:f((()=>[r("span",I,[u(a,{onClick:l[2]||(l[2]=e=>ae.value.visible=!1),disabled:ae.value.uploading},{default:f((()=>l[24]||(l[24]=[g("取消")]))),_:1,__:[24]},8,["disabled"]),u(a,{type:"primary",onClick:me,loading:ae.value.uploading,disabled:ae.value.uploading},{default:f((()=>[g(_(ae.value.uploading?"上传中...":"确定"),1)])),_:1},8,["loading","disabled"])])])),default:f((()=>[u(Ve,{model:ae.value.form,"label-width":"120px"},{default:f((()=>[u(j,{label:"所属课程教材",required:""},{default:f((()=>[u(i,{modelValue:ae.value.form.course_id,"onUpdate:modelValue":l[1]||(l[1]=e=>ae.value.form.course_id=e),placeholder:"请选择课程教材",filterable:"",style:{width:"100%"}},{default:f((()=>[(s(!0),n(v,null,m(te.value,(e=>(s(),c(o,{key:e.id,label:"[ "+e.course_code+" ] "+e.course_name,value:e.id},{default:f((()=>[r("span",N,_("[ "+e.course_code+" ] "+e.course_name),1),r("span",O,_(e.ver),1)])),_:2},1032,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),u(j,{label:"考试大纲文件",required:""},{default:f((()=>[u(ke,{class:"upload-demo",drag:"",ref_key:"upload",ref:fe,"show-file-list":!1,action:"#","auto-upload":!1,"on-change":pe,limit:1,multiple:!1,disabled:ae.value.uploading,"on-exceed":ge,accept:"application/msword, .doc, .docx"},{tip:f((()=>l[21]||(l[21]=[r("div",{class:"el-upload__tip"}," 仅支持 docx 格式 ",-1)]))),default:f((()=>[u(de,{class:"el-icon--upload"},{default:f((()=>[u(h(b))])),_:1}),l[22]||(l[22]=r("div",{class:"el-upload__text"},[g(" 将文件拖到此处，或"),r("em",null,"点击上传")],-1))])),_:1,__:[22]},8,["disabled"]),ae.value.form.file?(s(),n("div",R," 已选择: "+_(ae.value.form.file.name)+" ("+_((ae.value.form.file.size/1048576).toFixed(2))+"MB) ",1)):p("",!0)])),_:1}),ae.value.uploading?(s(),c(j,{key:0},{default:f((()=>[u(Ce,{percentage:ae.value.progress,format:ve},null,8,["percentage"]),l[23]||(l[23]=r("div",{class:"upload-status"},"正在上传中，请勿关闭窗口...",-1))])),_:1,__:[23]})):p("",!0)])),_:1},8,["model"])])),_:1},8,["modelValue","close-on-press-escape"]),u(A,{modelValue:ee.value,"onUpdate:modelValue":l[4]||(l[4]=e=>ee.value=e),"doc-url":le.value},null,8,["modelValue","doc-url"]),u(Se,{modelValue:ie.value.visible,"onUpdate:modelValue":l[7]||(l[7]=e=>ie.value.visible=e),title:"编辑考试大纲",draggable:"",width:"600px","close-on-click-modal":!1,"close-on-press-escape":!ie.value.uploading},{footer:f((()=>[r("span",J,[u(a,{onClick:l[6]||(l[6]=e=>ie.value.visible=!1),disabled:ie.value.uploading},{default:f((()=>l[28]||(l[28]=[g("取消")]))),_:1,__:[28]},8,["disabled"]),u(a,{type:"primary",onClick:xe,loading:ie.value.uploading,disabled:ie.value.uploading},{default:f((()=>[g(_(ie.value.uploading?"保存中...":"确定"),1)])),_:1},8,["loading","disabled"])])])),default:f((()=>[u(Ve,{model:ie.value.form,"label-width":"100px"},{default:f((()=>[u(j,{label:"所属课程",required:""},{default:f((()=>[u(i,{modelValue:ie.value.form.course_id,"onUpdate:modelValue":l[5]||(l[5]=e=>ie.value.form.course_id=e),placeholder:"请选择课程",style:{width:"100%"}},{default:f((()=>[(s(!0),n(v,null,m(te.value,(e=>(s(),c(o,{key:e.id,label:" [ "+e.course_code+" ] "+e.course_name,value:e.id},{default:f((()=>[r("span",B,_("[ "+e.course_code+" ] "+e.course_name),1),r("span",X,_(e.ver),1)])),_:2},1032,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),u(j,{label:"考试大纲文件"},{default:f((()=>[u(ke,{class:"upload-demo",drag:"",ref_key:"editUpload",ref:he,"show-file-list":!1,action:"#","auto-upload":!1,"on-change":be,limit:1,multiple:!1,disabled:ie.value.uploading,"on-exceed":ye,accept:"application/msword, .doc, .docx"},{tip:f((()=>l[25]||(l[25]=[r("div",{class:"el-upload__tip"}," 仅支持 docx 格式，不选择文件则保持原文件不变 ",-1)]))),default:f((()=>[u(de,{class:"el-icon--upload"},{default:f((()=>[u(h(b))])),_:1}),l[26]||(l[26]=r("div",{class:"el-upload__text"},[g(" 将文件拖到此处，或"),r("em",null,"点击上传")],-1))])),_:1,__:[26]},8,["disabled"]),ie.value.form.file?(s(),n("div",E," 已选择: "+_(ie.value.form.file.name)+" ("+_((ie.value.form.file.size/1048576).toFixed(2))+"MB) ",1)):p("",!0)])),_:1}),ie.value.uploading?(s(),c(j,{key:0},{default:f((()=>[u(Ce,{percentage:ie.value.progress,format:ve},null,8,["percentage"]),l[27]||(l[27]=r("div",{class:"upload-status"},"正在上传中，请勿关闭窗口...",-1))])),_:1,__:[27]})):p("",!0)])),_:1},8,["model"])])),_:1},8,["modelValue","close-on-press-escape"]),k.dialogShow?(s(),c(Se,{key:0,modelValue:k.dialogShow,"onUpdate:modelValue":l[11]||(l[11]=e=>k.dialogShow=e),"align-center":"",draggable:"","show-close":!0,fullscreen:!0,"close-on-click-modal":!1,"close-on-press-escape":!1},{footer:f((()=>[r("span",H,[u(a,{onClick:l[9]||(l[9]=e=>k.dialogShow=!1),icon:"Close"},{default:f((()=>l[29]||(l[29]=[g("关闭")]))),_:1,__:[29]}),u(a,{type:"success",onClick:l[10]||(l[10]=e=>{return l={content:k.content,id:k.id},void C(l).then((e=>{e.data>0?(k.dialogShow=!1,y({message:"保存成功！",type:"success"}),ue()):y({message:"保存失败！"+e.msg,type:"error"})}));var l}),icon:"CircleCheck"},{default:f((()=>l[30]||(l[30]=[g(" 保存 ")]))),_:1,__:[30]})])])),default:f((()=>[w((s(),n("div",{class:"dialog_content","element-loading-text":k.loadingText,"element-loading-spinner":k.loadingSvg,"element-loading-svg-view-box":"-10, -10, 50, 50","element-loading-background":"rgba(122, 122, 122, 0.8)"},[r("div",P,[r("div",G,[u(ze,{modelValue:k.content,"onUpdate:modelValue":l[8]||(l[8]=e=>k.content=e),height:"calc(100vh - 150px)"},null,8,["modelValue"])])])],8,K)),[[je,k.loading]])])),_:1},8,["modelValue"])):p("",!0),u(Se,{modelValue:oe.visible,"onUpdate:modelValue":l[13]||(l[13]=e=>oe.visible=e),ref:"kdroomDialog",title:"预览内容",draggable:"","show-close":!0,fullscreen:!0,"close-on-click-modal":!1,"close-on-press-escape":!1},{footer:f((()=>[r("span",Y,[u(a,{onClick:l[12]||(l[12]=e=>oe.visible=!1),icon:"Close"},{default:f((()=>l[31]||(l[31]=[g("关闭")]))),_:1,__:[31]})])])),default:f((()=>[r("div",Q,[r("div",W,[u(Le,{text:oe.content,style:{height:"calc(100vh - 150px)",overflow:"auto"}},null,8,["text"])])])])),_:1},8,["modelValue"])])}}}),[["__scopeId","data-v-65fe7f35"]]);export{Z as default};
