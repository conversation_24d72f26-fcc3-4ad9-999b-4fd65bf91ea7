const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["static/preview_question_bank-2pRoVjiV.js","static/preview_question_bank.vue_vue_type_style_index_0_lang-DU1g_tNm.js","static/question-DWIQ7W1h.js","static/index-BERmxY3Y.js","static/index-BjGfTwlR.css","static/tools-CvIkJR06.js","static/preview_question_bank-LVJVPIXS.css"])))=>i.map(i=>d[i]);
import{d as e,r as s,Y as l,S as t,E as a,a as i,c as d,b as o,F as r,j as c,w as u,e as n,f as _,h as v,p,m as f,q as b,a0 as y,t as h,B as m,a1 as g,a2 as k,i as j,a3 as z,a4 as x,a5 as $,a6 as w,a7 as I,a8 as V,D as C,U as R,Q as S,R as D,_ as E}from"./index-BERmxY3Y.js";import{c as O,e as L,f as N,h as T,i as q,j as A,k as U,l as B,s as J}from"./kcmgr-_69jTmcm.js";import{d as P}from"./course-DUm3UmHR.js";import{G as M,m as F}from"./dzs-DZN_gNG7.js";const W={class:"top-selectors"},G={class:"left-selector"},H={class:"right-selector"},Q={class:"dialog-content"},Y={class:"left-panel"},K={class:"panel-content"},X={key:0,style:{padding:"20px","text-align":"center",color:"#999"}},Z={key:1,style:{padding:"20px","text-align":"center",color:"#999"}},ee={key:2,style:{padding:"20px","text-align":"center",color:"#999"}},se={key:3,style:{"margin-bottom":"10px","font-size":"12px",color:"#666"}},le={key:4,style:{"margin-bottom":"10px","font-size":"12px",color:"#666"}},te=["onDragover","onDragleave","onDrop"],ae={class:"node-content"},ie={class:"node-label"},de={key:0,class:"resource-tags"},oe={key:0,class:"job-resource-card"},re={class:"job-card-header"},ce={class:"job-title"},ue={class:"job-actions"},ne={class:"job-card-tags"},_e={key:1,class:"other-resource-card"},ve={class:"tag-title"},pe={class:"resource-actions"},fe={class:"right-panel"},be={class:"panel-header"},ye={class:"panel-content"},he={key:0,style:{padding:"20px","text-align":"center",color:"#999"}},me={key:1,style:{padding:"20px","text-align":"center",color:"#999"}},ge={key:2,style:{padding:"20px","text-align":"center",color:"#999"}},ke={key:3,class:"resource-header"},je={style:{"font-size":"12px",color:"#666","margin-bottom":"8px"}},ze={class:"resource-filters"},xe=["draggable","onDragstart","onDragend"],$e={key:0,class:"resource-content"},we={class:"job-info-section",style:{display:"inline-block"}},Ie={class:"node-label job-title"},Ve={class:"job-extra-info"},Ce={style:{display:"inline-block",float:"right"}},Re={key:1,class:"other-resource-content"},Se={class:"node-label"},De={key:2,class:"node-label"},Ee={class:"dialog-footer"},Oe={class:"batch-add-content"},Le={class:"dialog-footer"},Ne={key:0,class:"job-preview-wrapper"},Te={key:1,class:"job-preview-wrapper"},qe={key:2,class:"ksdg-preview-wrapper"},Ae={class:"ksdg-preview-content"},Ue={key:3,class:"jc-preview-wrapper"},Be={class:"jc-preview-content"},Je={class:"textbook-display"},Pe={class:"textbook-cover"},Me=["src","alt"],Fe={class:"textbook-info"},We={class:"info-item"},Ge={class:"info-value"},He={class:"info-item"},Qe={class:"info-value"},Ye={class:"info-item"},Ke={class:"info-value"},Xe={key:0,class:"info-item"},Ze={class:"info-value"},es={key:1,class:"info-item"},ss={class:"info-value"},ls={key:2,class:"info-item"},ts={class:"info-value"},as={key:3,class:"info-item"},is={class:"info-value"},ds={key:4,class:"action-buttons"},os={key:4,class:"video-preview-wrapper"},rs={class:"video-preview-content"},cs={class:"video-header"},us={class:"video-title"},ns={class:"video-info"},_s={class:"video-player-container"},vs=["src"],ps={key:1,class:"no-video"},fs={class:"video-details"},bs={key:0,class:"detail-item"},ys={class:"detail-value"},hs={key:1,class:"detail-item"},ms={class:"detail-value"},gs={key:2,class:"detail-item"},ks={class:"detail-value"},js={key:3,class:"detail-item"},zs={class:"detail-value"},xs={key:5,class:"dzs-preview-wrapper"},$s={class:"dzs-preview-content"},ws={class:"dzs-header"},Is={class:"dzs-title"},Vs={class:"dzs-info"},Cs={class:"dzs-content"},Rs={key:6,class:"dzs-preview-wrapper"},Ss={class:"dzs-preview-content"},Ds={class:"dzs-header"},Es={class:"dzs-title"},Os={class:"dzs-content"},Ls={key:7,class:"other-preview"},Ns={class:"dialog-footer"},Ts=E(e({name:"AssignResourceDialog",name:"AssignResourceDialog",props:{modelValue:{type:Boolean,default:!1},courseBaseId:{},courseInfoId:{},kcBm:{},plateId:{},title:{default:"指定资料"}},emits:["update:modelValue","close","save"],setup(e,{emit:E}){const Ts=e,qs=E,As=s(Ts.modelValue),Us=s(!1),Bs=s(!1),Js=s(""),Ps=s([]),Ms=s(null),Fs=s([]),Ws=s([]),Gs=s(""),Hs=s(null),Qs=s([]),Ys=s([]),Ks=s([]),Xs=s([]),Zs=s([]),el=s(new Map),sl=s(""),ll=s(""),tl=s(""),al=s(""),il=s([]),dl=s([]),ol=s([]),rl=l((()=>{const e=Ps.value.find((e=>e.id===Ms.value));return(null==e?void 0:e.resource)||""})),cl=s({visible:!1,method:"byOrder",loading:!1}),ul=s({visible:!1,type:"",data:null}),nl={children:"children",label:"label"},_l={children:"children",label:"label"},vl=s(),pl=s(),fl={video:"视频",dzs:"电子书",dzs_s:"单电子书",ksdg:"考试大纲",jc:"教材",job:"试题",job_source:"历年真题"},bl={video:w,dzs:I,ksdg:k,jc:I,job:V,job_source:V,default:$},yl={video:"primary",dzs:"success",ksdg:"info",jc:"success",job:"warning",job_source:"danger"},hl=l((()=>{const e=Ps.value.find((e=>e.id===Ms.value));return e&&1===e.hava_chapter&&Zs.value.length>0})),ml=e=>fl[e]||e,gl=e=>bl[e]||bl.default,kl=e=>yl[e]||"",jl=e=>{const s=`job_${e.course_job_id||e.id}`,l=el.value.get(s);if(!l)return e.title||e.label;let t=`标题: ${l.fullTitle||l.title}`;return l.competence_level&&(t+=`\n能力层次: ${l.competence_level}`),l.difficulty_level&&(t+=`\n难度层次: ${l.difficulty_level}`),l.questiontype&&(t+=`\n题目类型: ${l.questiontype}`),t},zl=(e,s)=>{const l=`job_${e.course_job_id||e.id}`,t=el.value.get(l);return(null==t?void 0:t[s])||""},xl=e=>{const s=`job_${e.id}`,l=el.value.get(s);if(!l)return e.title;return`${l.fullTitle||e.title}`},$l=(e,s)=>{const l=`job_${e.id}`,t=el.value.get(l);return(null==t?void 0:t[s])||""};let wl=null;const Il=(e,s)=>{s.isLeaf,s.resources||(s.resources=[]);const l=e.course_video_id||e.dzs_chapter_id||e.course_dzs_s_id||e.course_job_id||e.course_ksdg_id||e.course_jc_id||e.id;if(s.resources.some((s=>"video"===e.type&&"video"===s.type?s.course_video_id===l:"dzs"===e.type&&"dzs"===s.type?s.course_dzs_id===l||s.id===l:"dzs_s"===e.type&&"dzs_s"===s.type?s.course_dzs_s_id===l||s.id===l:"job"===e.type&&"job"===s.type?s.course_job_id===l||s.id===l:"ksdg"===e.type&&"ksdg"===s.type?s.course_ksdg_id===l||s.id===l:"jc"===e.type&&"jc"===s.type&&(s.course_jc_id===l||s.id===l)))){const e=s.isLeaf?"节":"章";return void a.warning(`该资源已存在于此${e}`)}const t={id:l,title:e.title||e.label,type:e.type,course_video_id:"video"===e.type?l:void 0,course_dzs_id:"dzs"===e.type?e.course_dzs_id:void 0,course_dzs_s_id:"dzs_s"===e.type?e.course_dzs_s_id||l:void 0,course_job_id:"job"===e.type?l:void 0,course_ksdg_id:"ksdg"===e.type?e.course_ksdg_id||l:void 0,course_jc_id:"jc"===e.type?e.course_jc_id||l:void 0,jobbank_source_id:"job_source"===e.type?(()=>{const s=String(e.id).split("_");return parseInt(s[s.length-1])})():void 0,ver:"job_source"===e.type?e.ver:void 0,vertitle:"job_source"===e.type?e.vertitle:void 0,sn:s.resources.length+1};if(s.resources.find((e=>e.type===t.type&&e.id===t.id)))return void a.warning(`资源"${e.title||e.label}"已经存在于"${s.label}"中`);s.resources.push(t),Rl(e);s.isLeaf;a.success(`资源"${e.title||e.label}"已添加到"${s.label}"`)},Vl=(e,s)=>{s.resources||(s.resources=[]);const l=e.course_video_id||e.dzs_chapter_id||e.course_dzs_s_id||e.course_job_id||e.course_ksdg_id||e.course_jc_id||e.id;if(s.resources.some((s=>"video"===e.type&&"video"===s.type?s.course_video_id===l:"dzs"===e.type&&"dzs"===s.type?s.course_dzs_id===l||s.id===l:"dzs_s"===e.type&&"dzs_s"===s.type?s.course_dzs_s_id===l||s.id===l:"job"===e.type&&"job"===s.type?s.course_job_id===l||s.id===l:"ksdg"===e.type&&"ksdg"===s.type?s.course_ksdg_id===l||s.id===l:"jc"===e.type&&"jc"===s.type&&(s.course_jc_id===l||s.id===l))))return!1;let t=e.title||e.label;"video"===e.type&&e.node_name&&(t=`视频_${e.node_name}`);const a={id:l,title:t,type:e.type,course_video_id:"video"===e.type?l:void 0,course_dzs_id:"dzs"===e.type?e.course_dzs_id:void 0,course_dzs_s_id:"dzs_s"===e.type?e.course_dzs_s_id||l:void 0,course_job_id:"job"===e.type?l:void 0,course_ksdg_id:"ksdg"===e.type?e.course_ksdg_id||l:void 0,course_jc_id:"jc"===e.type?e.course_jc_id||l:void 0,jobbank_source_id:"job_source"===e.type?(()=>{const s=String(e.id).split("_");return parseInt(s[s.length-1])})():void 0,ver:"job_source"===e.type?e.ver:void 0,vertitle:"job_source"===e.type?e.vertitle:void 0,sn:s.resources.length+1};return!s.resources.find((e=>e.type===a.type&&e.id===a.id))&&(s.resources.push(a),!0)},Cl=e=>{const s=new Set;for(const l of e)s.add(l.resource.id);Xs.value.forEach((e=>{e.children&&(e.children=e.children.filter((e=>!e.isResource||!s.has(e.id))))}))},Rl=e=>{Zs.value.forEach((s=>{if(s.children){const l=s.children.findIndex((s=>s.id===e.id));l>-1&&s.children.splice(l,1)}})),Xs.value.forEach((s=>{if(s.children){const l=s.children.findIndex((s=>s.id===e.id));l>-1&&s.children.splice(l,1)}}))},Sl=(e,s)=>{const l=t=>{for(const a of t){if(a.chapterId===e.chapterId){const e=a.resources.findIndex((e=>e.id===s.id&&e.type===s.type));if(e>-1)return a.resources.splice(e,1),!0}if(a.children&&a.children.length>0&&l(a.children))return!0}return!1},t=l(Ys.value),i=e.resources.findIndex((e=>e.id===s.id&&e.type===s.type));i>-1&&e.resources.splice(i,1),t||i>-1?(a.success("资源移除成功"),Dl()):a.error("删除资源失败")},Dl=()=>{const e=El();Js.value?Zs.value=e.map((e=>{var s;return{...e,children:(null==(s=e.children)?void 0:s.filter((e=>e.type===Js.value)))||[]}})).filter((e=>e.children&&e.children.length>0)):Zs.value=e,Ks.value=Ys.value.map((e=>{const s={...e};return s.resources&&(s.resources=s.resources.filter((e=>{const s=Qs.value.includes(e.type),l=!Js.value||e.type===Js.value;return s&&l}))),s.children&&(s.children=s.children.map((e=>{const s={...e};return s.resources&&(s.resources=s.resources.filter((e=>{const s=Qs.value.includes(e.type),l=!Js.value||e.type===Js.value;return s&&l}))),s})),Js.value&&(s.children=s.children.filter((e=>e.resources&&e.resources.length>0)))),s})),Zs.value=El(),Ol()},El=()=>{const e=new Set,s=new Set,l=new Set,t=new Set,a=new Set,i=new Set,d=new Set;Ys.value.forEach((o=>{o.resources&&o.resources.forEach((o=>{"video"===o.type&&o.course_video_id?e.add(o.course_video_id):"dzs"===o.type&&o.id?s.add(o.id):"dzs_s"===o.type&&o.course_dzs_s_id?l.add(o.course_dzs_s_id):"job"===o.type&&o.id?t.add(o.id):"ksdg"===o.type&&o.course_ksdg_id?a.add(o.course_ksdg_id):"jc"===o.type&&o.course_jc_id?i.add(o.course_jc_id):"job_source"===o.type&&o.id&&d.add(String(o.id))})),o.children&&o.children.forEach((o=>{o.resources&&o.resources.forEach((o=>{"video"===o.type&&o.course_video_id?e.add(o.course_video_id):"dzs"===o.type&&o.id?s.add(o.id):"dzs_s"===o.type&&o.course_dzs_s_id?l.add(o.course_dzs_s_id):"job"===o.type&&o.id?t.add(o.id):"ksdg"===o.type&&o.course_ksdg_id?a.add(o.course_ksdg_id):"jc"===o.type&&o.course_jc_id?i.add(o.course_jc_id):"job_source"===o.type&&o.id&&d.add(String(o.id))}))}))}));return Xs.value.map((o=>{var r;const c=(null==(r=o.children)?void 0:r.filter((o=>{if("video"===o.type&&o.course_video_id)return!e.has(o.course_video_id);if("dzs"===o.type&&o.dzs_chapter_id)return!s.has(o.dzs_chapter_id);if("dzs_s"===o.type&&o.course_dzs_s_id){return!l.has(o.course_dzs_s_id)}if("job"===o.type&&o.course_job_id)return!t.has(o.course_job_id);if("ksdg"===o.type&&o.course_ksdg_id){return!a.has(o.course_ksdg_id)}if("jc"===o.type&&o.course_jc_id){return!i.has(o.course_jc_id)}if("job_source"===o.type&&o.id){const e=String(o.id),s=e.startsWith("job_source_")?e.substring(11):e;return!d.has(s)}return!0})))||[];return{...o,children:c}})).filter((e=>{var s,l;return 0===((null==(l=null==(s=Xs.value.find((s=>s.id===e.id)))?void 0:s.children)?void 0:l.length)||0)||e.children&&e.children.length>0}))},Ol=()=>{const e=El();if(!e.length)return void(Zs.value=[]);const s=sl.value.toLowerCase().trim(),l=ll.value,t=tl.value,a=al.value;Zs.value=s||l||t||a?e.map((e=>{const i={...e};return e.children&&(i.children=e.children.filter((e=>{if(!e.isResource)return!0;const i=!s||e.label.toLowerCase().includes(s)||e.title.toLowerCase().includes(s);if("job"!==e.type||!rl.value.includes("job"))return i;const d=`job_${e.course_job_id||e.id}`,o=el.value.get(d);if(!o)return i;const r=!l||o.competence_level===l,c=!t||o.difficulty_level===t,u=!a||o.questiontype===a;return i&&r&&c&&u}))),i})).filter((e=>!e.children||e.children.length>0)):e},Ll=e=>{Dl()},Nl=e=>{e.target.src="/src/assets/images/default-textbook.png"},Tl=e=>{e.target;a.error("视频加载失败，请检查网络连接或联系管理员")},ql=()=>{var e;(null==(e=ul.value.data)?void 0:e.url)&&window.open(ul.value.data.url,"_blank")},Al=e=>{let s=null;if("video"===e.type){const l=`video_${e.course_video_id||e.id}`,t=el.value.get(l);t&&(s={...t,mp4_url:t.mp4_url||"",course_video_title:t.course_video_title||t.title,node_name:t.node_name||"",chapter_name:t.chapter_name||"",course_name:t.course_name||"",course_code:t.course_code||"",chapter_code:t.chapter_code||"",node_code:t.node_code||"",course_video_id:t.course_video_id||t.id})}else if("dzs"===e.type){const l=`dzs_${e.id}`,t=el.value.get(l);t&&(s={...t,chap_content:t.chap_content||"",chap_title:t.chap_title||t.title,chap_num:t.chap_num||"",course_dzs_id:t.course_dzs_id||"",id:t.id||e.id})}else if("dzs_s"===e.type){const l=`dzs_s_${e.id}`,t=el.value.get(l);t&&(s={...t,content:t.content||"",title:t.title||t.menu||"",menu:t.menu||"",zsd:t.zsd||"",url:t.url||"",execute_status:t.execute_status||"",execute_url:t.execute_url||"",course_jc_id:t.course_jc_id||"",course_base_id:t.course_base_id||"",id:t.id||e.id})}else if("job"===e.type){const l=`job_${e.course_job_id||e.id}`;if(s=el.value.get(l),s){let e=Ts.courseBaseId;if(!e&&Hs.value){const s=Ws.value.find((e=>e.id===Hs.value));e=s?s.course_base_id:Hs.value}s={...s,course_base_id:e}}}else if("job_source"===e.type){const l=`job_source_${e.id}`,t=el.value.get(l);if(t){let e=Ts.courseBaseId;if(!e&&Hs.value){const s=Ws.value.find((e=>e.id===Hs.value));e=s?s.course_base_id:Hs.value}s={course_base_id:e,jobbank_source_id:t.id.split("_")[1],jobbank_ver:t.ver,jobbank_id:"",...t}}}else if("ksdg"===e.type){const l=`ksdg_${e.id}`,t=el.value.get(l);t&&(s={...t,content:t.content||t.title||"暂无内容"})}else if("jc"===e.type){const l=`jc_${e.id}`,t=el.value.get(l);t&&(s={...t,textbook_name:t.textbook_name||t.course_name||t.title,textbook_editor:t.textbook_editor||"",publication_info:t.publication_info||"",textbook_url:t.textbook_url||"",url:t.url||"",course_code:t.course_code||"",course_credits:t.course_credits||"",course_type:t.course_type||"",ver:t.ver||""})}let l=s||e;if("job"===e.type&&!l.course_base_id){let e=Ts.courseBaseId;if(!e&&Hs.value){const s=Ws.value.find((e=>e.id===Hs.value));e=s?s.course_base_id:Hs.value}l={...l,course_base_id:e}}if("video"!==e.type||s||(l.mp4_url=e.mp4_url||"",l.course_video_title=e.course_video_title||e.title||"",l.node_name=e.node_name||"",l.chapter_name=e.chapter_name||"",l.course_name=e.course_name||"",l.course_code=e.course_code||"",l.chapter_code=e.chapter_code||"",l.node_code=e.node_code||"",l.course_video_id=e.course_video_id||e.id),"dzs"!==e.type||s||(l.chap_content=e.chap_content||e.content||"暂无内容",l.chap_title=e.chap_title||e.title||"",l.chap_num=e.chap_num||"",l.course_dzs_id=e.course_dzs_id||"",l.id=e.id),"dzs_s"!==e.type||s||(l.content=e.content||"暂无内容",l.title=e.title||e.menu||"",l.menu=e.menu||"",l.zsd=e.zsd||"",l.url=e.url||"",l.execute_status=e.execute_status||"",l.execute_url=e.execute_url||"",l.course_jc_id=e.course_jc_id||"",l.course_base_id=e.course_base_id||"",l.id=e.id),"job_source"===e.type&&!s){let s=Ts.courseBaseId;if(!s&&Hs.value){const e=Ws.value.find((e=>e.id===Hs.value));s=e?e.course_base_id:Hs.value}l={...l,course_base_id:s,jobbank_source_id:e.jobbank_source_id||"",jobbank_ver:e.ver||e.jobbank_ver||"",jobbank_id:""}}"ksdg"!==e.type||s||(l={...l,content:e.content||e.title||"暂无内容"}),"jc"!==e.type||s||(l={...l,textbook_name:e.textbook_name||e.title||"未知教材",textbook_editor:e.textbook_editor||"",publication_info:e.publication_info||"",textbook_url:e.textbook_url||"",url:e.url||"",course_code:e.course_code||"",course_credits:e.course_credits||"",course_type:e.course_type||"",ver:e.ver||""}),ul.value={visible:!0,type:e.type,data:l}},Ul=()=>{ul.value.visible=!1,ul.value.type="",ul.value.data=null},Bl=S((()=>D((()=>import("./preview_question_bank-2pRoVjiV.js")),__vite__mapDeps([0,1,2,3,4,5,6]))));t((()=>Ts.modelValue),(e=>{As.value=e,e&&Ql()})),t(As,(e=>{qs("update:modelValue",e)})),t(Js,(()=>{sl.value="",ll.value="",tl.value="",al.value="",Ol()}));const Jl=async()=>{if(Ts.courseInfoId)try{const e=await P({id:Ts.courseInfoId});if(200===e.code)if(Ps.value=e.data||[],Ts.plateId&&Ps.value.length>0){const e=Ps.value.find((e=>e.plate_id===Ts.plateId));Ms.value=e?e.id:Ps.value[0].id}else Ps.value.length>0&&(Ms.value=Ps.value[0].id)}catch(e){}},Pl=async()=>{try{const e=await M({});200===e.code&&(Fs.value=e.data||[],Fs.value.length>0&&(Gs.value=Fs.value[0].ver,await Ml()))}catch(e){}},Ml=async()=>{var e;if(Gs.value)try{const s=await F({ver:Gs.value});if(200===s.code&&(Ws.value=(null==(e=s.data)?void 0:e.list)||[],Ts.courseBaseId&&Ws.value.length>0)){const e=Ws.value.find((e=>e.course_base_id===Ts.courseBaseId));e&&(Hs.value=e.id,Kl())}}catch(s){}},Fl=async e=>{var s,l;if(!Ys.value.length)return;const t=Ys.value[0];t.resources=[];try{const a=await B({courseInfoId:Ts.courseInfoId,have_chapter:null==(s=Ps.value.find((e=>e.id==Ms.value)))?void 0:s.hava_chapter,plate_id:null==(l=Ps.value.find((e=>e.id==Ms.value)))?void 0:l.plate_id});if(200===a.code&&a.data&&a.data.length>0){const s=a.data.find((s=>s.bm_plate_id===e.plate_id&&s.resource_ids));if(s&&s.resource_ids){let e;e="string"==typeof s.resource_ids?JSON.parse(s.resource_ids):s.resource_ids,Object.keys(e).forEach((s=>{const l=e[s];Array.isArray(l)&&l.forEach((e=>{let l;switch(s){case"video":const s=`video_${e.id}`,t=el.value.get(s);let a=`视频_${e.id}`;t?a=t.node_name?`视频_${t.node_name}`:t.title||a:e.title&&(a=e.title),l={id:e.id,title:a,type:"video",sn:e.sn,course_video_id:e.id};break;case"dzs":const i=`dzs_${e.id}`,d=el.value.get(i),o=d?d.title:`电子书_${e.id}`;l={id:e.id,title:o,type:"dzs",sn:e.sn};break;case"job":const r=`job_${e.id}`,c=el.value.get(r);let u=`试题_${e.id}`;c?(u=c.fullTitle||c.title||u,u.length>50&&(u=u.substring(0,50)+"...")):e.title&&(u=e.title),l={id:e.id,title:u,type:"job",sn:e.sn,course_job_id:e.id};break;case"ksdg":const n=`ksdg_${e.id}`,_=el.value.get(n),v=_?_.title:`考试大纲_${e.id}`;l={id:e.id,title:v,type:"ksdg",sn:e.sn,course_ksdg_id:e.id};break;case"jc":const p=`jc_${e.id}`,f=el.value.get(p),b=f?f.title:`教材_${e.id}`;l={id:e.id,title:b,type:"jc",sn:e.sn,course_jc_id:e.id};break;case"job_source":const y=`${e.ver||""}_${e.jobbank_source_id}`,h=`job_source_${y}`,m=el.value.get(h),g=m?m.title:e.title||e.vertitle||`历年真题_${e.jobbank_source_id}`;l={id:y,title:g,type:"job_source",sn:e.sn,jobbank_source_id:e.jobbank_source_id,ver:e.ver||(m?m.ver:""),vertitle:e.title||e.vertitle||g};break;case"dzs_s":const k=`dzs_s_${e.id}`,j=el.value.get(k),z=j?j.title:`单电子书_${e.id}`;l={id:e.id,title:z,type:"dzs_s",sn:e.sn,course_dzs_s_id:e.id};break;default:return}t.resources.find((e=>e.type===l.type&&e.id===l.id))||t.resources.push(l)}))}))}}Dl()}catch(a){}},Wl=async e=>{Ms.value=e,Bs.value=!0;try{const s=Ps.value.find((s=>s.id===e));if(s){if(s.resource?Qs.value=s.resource.split(",").map((e=>e.trim())):Qs.value=["video","dzs","dzs_s","ksdg","jc","job","job_source"],Js.value="",1===s.hava_chapter)Gs.value&&Hs.value&&await Kl(),Yl();else{const e={id:`virtual_chapter_${s.id}`,label:`${s.title} - 资源分配`,isLeaf:!0,chapterId:s.id,courseInfoId:Ts.courseInfoId||0,resources:[]};Ys.value=[e],Dl()}Gs.value&&Hs.value?(await Kl(),0===s.hava_chapter&&await Fl(s)):Ys.value.length>0&&Dl()}}catch(s){a.error("加载数据失败")}finally{Bs.value=!1}},Gl=async e=>{Bs.value=!0;try{Gs.value=e,Hs.value=null,await Ml(),Xs.value=[],Zs.value=[]}catch(s){a.error("加载数据失败")}finally{Bs.value=!1}},Hl=async e=>{Bs.value=!0;try{Hs.value=e,await Kl();const s=Ps.value.find((e=>e.id===Ms.value));s&&0===s.hava_chapter&&await Fl(s)}catch(s){a.error("加载数据失败")}finally{Bs.value=!1}},Ql=async()=>{Bs.value=!0;try{if(await Promise.all([Jl(),Pl()]),!Ts.courseBaseId)return Ys.value=[{id:"chapter_174",label:"第一章 行政法学的基本范畴",isLeaf:!1,chapterId:174,courseInfoId:23,resources:[],children:[{id:"section_192",label:"0101 行政",isLeaf:!0,chapterId:192,courseInfoId:23,resources:[{id:101,title:"[第一章] 已分配的测试视频1",type:"video",course_video_id:101,sn:1}]}]},{id:"chapter_2",label:"第二章 测试空章节",isLeaf:!1,chapterId:2,courseInfoId:23,resources:[],children:[]}],Xs.value=[{id:"resource_chapter_1",label:"第一章 测试资源章节",title:"测试资源章节",type:"chapter",isResource:!1,children:[{id:"video_101",label:"[第一章] 已分配的测试视频1",title:"[第一章] 已分配的测试视频1",type:"video",isResource:!0,course_video_id:101},{id:"video_102",label:"[第一章] 未分配的测试视频2",title:"[第一章] 未分配的测试视频2",type:"video",isResource:!0,course_video_id:102},{id:"dzs_201",label:"[第一章] 测试电子书1",title:"[第一章] 测试电子书1",type:"dzs",isResource:!0,course_dzs_id:201},{id:"dzs_202",label:"[第一章] 测试电子书2",title:"[第一章] 测试电子书2",type:"dzs",isResource:!0,course_dzs_id:202},{id:"job_301",label:"[第一章] 测试试题1",title:"[第一章] 测试试题1",type:"job",isResource:!0,course_job_id:301}]},{id:"resource_chapter_empty",label:"第二章 空章节",title:"空章节",type:"chapter",isResource:!1,children:[]}],void et();Ms.value&&await Wl(Ms.value),Hs.value&&(await Kl(),et())}catch(e){a.error("加载数据失败")}finally{Bs.value=!1}},Yl=async()=>{var e,s;try{Hs.value&&0===el.value.size&&await Kl();const l=await B({courseInfoId:Ts.courseInfoId,have_chapter:null==(e=Ps.value.find((e=>e.id==Ms.value)))?void 0:e.hava_chapter,plate_id:null==(s=Ps.value.find((e=>e.id==Ms.value)))?void 0:s.plate_id});if(200===l.code){const e=Xl(l.data||[]);Ys.value=e,Dl()}}catch(l){}},Kl=async()=>{if(!Hs.value)return Xs.value=[],void(Zs.value=[]);if(0===Qs.value.length)return Xs.value=[],void(Zs.value=[]);try{const e=[],s=[];if(Qs.value.includes("video")&&(e.push(O({course_jc_id:Hs.value})),s.push("video")),Qs.value.includes("dzs")&&(e.push(L({course_jc_id:Hs.value})),s.push("dzs")),Qs.value.includes("dzs_s")&&(e.push(N({course_jc_id:Hs.value})),s.push("dzs_s")),Qs.value.includes("ksdg")&&(e.push(T({course_jc_id:Hs.value})),s.push("ksdg")),Qs.value.includes("jc")){const l=Ws.value.find((e=>e.id===Hs.value)),t=l?l.course_code:Hs.value;e.push(q({course_code:t})),s.push("jc")}if(Qs.value.includes("job")&&(e.push(A({course_jc_id:Hs.value})),s.push("job")),Qs.value.includes("job_source")){const l=Ws.value.find((e=>e.id===Hs.value)),t=l?l.course_base_id:Hs.value;e.push(U({course_base_id:t})),s.push("job_source")}const l=await Promise.all(e),t=[];l.forEach(((e,l)=>{const a=s[l];e&&200===e.code&&e.data&&t.push(...e.data.map((e=>({...e,resourceType:a}))))})),Xs.value=Zl(t),(()=>{const e=new Set,s=new Set,l=new Set;el.value.forEach(((t,a)=>{a.startsWith("job_")&&(t.competence_level&&e.add(t.competence_level),t.difficulty_level&&s.add(t.difficulty_level),t.questiontype&&l.add(t.questiontype))})),il.value=Array.from(e).sort(),dl.value=Array.from(s).sort(),ol.value=Array.from(l).sort()})(),Dl()}catch(e){}},Xl=e=>{if(!e||0===e.length)return[];const s=e.filter((e=>0===e.pid)),l=e.filter((e=>0!==e.pid)),t=new Map;s.forEach((e=>{const s=`chapter_${e.id}`,l=e.chapter_code||"",a=l?parseInt(l,10):0,i=a>0?`第${a}章`:"",d=i?`${i} ${e.chapter_name||""}`.trim():e.chapter_name||`章节_${e.id}`;t.set(s,{id:s,label:d,isLeaf:!1,chapterId:e.id,courseInfoId:e.course_info_id,resources:[],children:[]})})),l.forEach((e=>{const s=`section_${e.id}`,l=`chapter_${e.pid}`,a={id:s,label:`${e.chapter_code||""} ${e.chapter_name||""}`.trim()||`节_${e.id}`,isLeaf:!0,chapterId:e.id,courseInfoId:e.course_info_id,resources:[]},i=t.get(l);i&&i.children&&i.children.push(a)}));const a=new Set;e.forEach((e=>{const s=`${e.id}_${e.pid}`;if(a.has(s))return;let l;if(a.add(s),0===e.pid){const s=`chapter_${e.id}`;l=t.get(s)}else{const s=`section_${e.id}`,a=`chapter_${e.pid}`,i=t.get(a);i&&i.children&&(l=i.children.find((e=>e.id===s)))}if(l&&e.resource_ids)try{let s;s="string"==typeof e.resource_ids?JSON.parse(e.resource_ids):e.resource_ids,Object.keys(s).forEach((e=>{const t=s[e];Array.isArray(t)&&t.forEach((s=>{let t;switch(e){case"video":const e=`video_${s.id}`,l=el.value.get(e);let a=`视频_${s.id}`;l?a=l.node_name?`视频_${l.node_name}`:l.title||a:s.title&&(a=s.title),t={id:s.id,title:a,type:"video",sn:s.sn,course_video_id:s.id};break;case"dzs":const i=`dzs_${s.id}`,d=el.value.get(i),o=d?d.title:`电子书_${s.id}`;t={id:s.id,title:o,type:"dzs",sn:s.sn};break;case"dzs_s":const r=`dzs_s_${s.id}`,c=el.value.get(r),u=c?c.title:`电子书_${s.id}`;t={id:s.id,title:u,type:"dzs_s",sn:s.sn};break;case"job":const n=`job_${s.id}`,_=el.value.get(n);let v=`试题_${s.id}`;_?(v=_.fullTitle||_.title||v,v.length>50&&(v=v.substring(0,50)+"...")):s.title&&(v=s.title),t={id:s.id,title:v,type:"job",sn:s.sn,course_job_id:s.id};break;case"ksdg":const p=`ksdg_${s.id}`,f=el.value.get(p),b=f?f.title:`考试大纲_${s.id}`;t={id:s.id,title:b,type:"ksdg",sn:s.sn,course_ksdg_id:s.id};break;case"jc":const y=`jc_${s.id}`,h=el.value.get(y),m=h?h.title:`教材_${s.id}`;t={id:s.id,title:m,type:"jc",sn:s.sn,course_jc_id:s.id};break;case"dzs_s":const g=`dzs_s_${s.id}`,k=el.value.get(g),j=k?k.title:`电子书_${s.id}`;t={id:s.id,title:j,type:"dzs_s",sn:s.sn,course_dzs_s_id:s.id};break;case"job_source":const z=`${s.ver||""}_${s.jobbank_source_id}`,x=`job_source_${z}`,$=el.value.get(x),w=$?$.title:s.title||s.vertitle||`历年真题_${s.jobbank_source_id}`;t={id:z,title:w,type:"job_source",sn:s.sn,jobbank_source_id:s.jobbank_source_id,ver:s.ver||($?$.ver:""),vertitle:s.title||s.vertitle||w};break;default:return}l.resources.find((e=>e.type===t.type&&e.id===t.id))||l.resources.push(t)}))}))}catch(i){}}));return Array.from(t.values())},Zl=e=>{const s=new Map;return el.value.clear(),e.forEach((e=>{let l="",t="",a="",i="",d=e.resourceType;if("video"===d){t=e.chapter_code||"";const s=t?parseInt(t,10):0,d=s>0?`第${s}章`:"其他";l=e.chapter_name?`${d} ${e.chapter_name}`:d,a=e.course_video_id,i=e.node_name?`视频_${e.node_name}`:e.course_video_title?`${e.course_video_title.split("_")[1]}`:`视频_${e.course_video_id}`}else if("dzs"===d){t=e.chap_num||"";const s=t?parseInt(t,10):0,d=s>0?`第${s}章`:e.ver||"其他";l=e.chap_title?`${d} ${e.chap_title}`.trim():d,a=e.id,i=e.chap_content?e.chap_content.substring(0,50).replace(/[#\n\r]/g,"").trim()+"...":`电子书_${e.id}`}else if("dzs_s"===d)l="电子书",t="",a=e.id,i=e.title?e.title:e.menu?e.menu:e.content?e.content.substring(0,50).replace(/[#\n\r]/g,"").trim()+"...":`电子书_${e.id}`;else if("job"===d){l=e.chapter_mc||e.ver||"其他",t=e.chapter_no||"",a=e.id;try{let s;s="object"==typeof e.title?e.title:JSON.parse(e.title||"{}");const l=s.title||`试题_${e.id}`;i=l.length>50?l.substring(0,50)+"...":l}catch(r){i=`试题_${e.id}`}}else"ksdg"===d?(l="考试大纲",t="",a=e.id,i=e.title||`考试大纲_${e.id}`):"jc"===d?(l=e.ver?`版本 ${e.ver}`:"其他版本",t=e.ver||"",a=e.id,i=e.textbook_name||e.course_name||`教材_${e.id}`):"job_source"===d&&(l=e.ver?`${e.ver}`:"其他版本",t=e.ver||"",a=e.id,i=e.vertitle||`历年真题_${e.id}`);if(!l)return;s.has(l)||s.set(l,{id:`resource_chapter_${d}_${t||Date.now()}`,label:l,title:l,type:"chapter",isResource:!1,children:[]});const o=s.get(l);if(a&&i){const s=`${d}_${a}`,c={id:a,title:i,type:d,chapter_name:l,chapter_code:t};if("video"===d&&(c.node_name=e.node_name||"",c.course_video_title=e.course_video_title||"",c.course_video_id=e.course_video_id,c.node_id=e.id,c.mp4_url=e.mp4_url||"",c.chapter_name=e.chapter_name||"",c.course_name=e.course_name||"",c.course_code=e.course_code||"",c.chapter_code=e.chapter_code||"",c.node_code=e.node_code||"",c.course_jc_id=e.course_jc_id||""),"job"===d)try{let s;s="object"==typeof e.title?e.title:JSON.parse(e.title||"{}"),c.fullTitle=s.title||`试题_${e.id}`,c.competence_level=e.competence_level||"",c.difficulty_level=e.difficulty_level||"",c.questiontype=e.questiontype||""}catch(r){c.fullTitle=`试题_${e.id}`,c.competence_level="",c.difficulty_level="",c.questiontype=""}"job_source"===d&&(c.ver=e.ver||"",c.vertitle=e.vertitle||i),"dzs"===d&&(c.chap_content=e.chap_content||"",c.chap_title=e.chap_title||"",c.chap_num=e.chap_num||"",c.course_dzs_id=e.course_dzs_id||"",c.ver=e.ver||""),"dzs_s"===d&&(c.content=e.content||"",c.title=e.title||"",c.menu=e.menu||"",c.zsd=e.zsd||"",c.url=e.url||"",c.zsd_key=e.zsd_key||"",c.execute_id=e.execute_id||"",c.execute_status=e.execute_status||"",c.execute_url=e.execute_url||"",c.course_jc_id=e.course_jc_id||"",c.course_base_id=e.course_base_id||"",c.status=e.status||"",c.create_date=e.create_date||"",c.create_user=e.create_user||""),"ksdg"===d&&(c.content=e.content||"",c.url=e.url||"",c.execute_status=e.execute_status||"",c.execute_url=e.execute_url||""),"jc"===d&&(c.textbook_name=e.textbook_name||"",c.textbook_editor=e.textbook_editor||"",c.publication_info=e.publication_info||"",c.textbook_url=e.textbook_url||"",c.url=e.url||"",c.course_code=e.course_code||"",c.course_credits=e.course_credits||"",c.course_type=e.course_type||"",c.ver=e.ver||"",c.course_name=e.course_name||"",c.course_base_id=e.course_base_id||"",c.status=e.status||""),el.value.set(s,c);const u={id:s,label:i,title:i,type:d,isResource:!0,course_video_id:"video"===d?Number(a):void 0,course_dzs_id:"dzs"===d?Number(e.course_dzs_id):void 0,course_job_id:"job"===d?Number(a):void 0,course_ksdg_id:"ksdg"===d?Number(a):void 0,course_jc_id:"jc"===d?Number(a):void 0,jobbank_source_id:"job_source"===d?Number(a):void 0,dzs_chapter_id:"dzs"===d?Number(a):void 0,course_dzs_s_id:"dzs_s"===d?Number(a):void 0,node_name:"video"===d?e.node_name:void 0,ver:"job_source"===d?e.ver:void 0,vertitle:"job_source"===d?e.vertitle:void 0};o.children.push(u)}})),Array.from(s.values())},et=()=>{Zs.value=El()},st=async()=>{if(Ts.courseInfoId)try{Us.value=!0;const e=[],s={ksdg:[],jc:[],job_source:[],video:[],dzs:[],dzs_s:[],job:[]},l=Ps.value.find((e=>e.id==Ms.value)),t=0===(null==l?void 0:l.hava_chapter),i=a=>{if(a.resources&&a.resources.length>0){const i={video:[],dzs:[],job:[]};a.resources.forEach(((e,l)=>{const a={id:e.id,sn:e.sn||l+1};if(t){if("video"===e.type&&e.course_video_id)s.video=s.video||[],s.video.push({id:e.course_video_id,sn:e.sn||l+1});else if("dzs"===e.type)s.dzs=s.dzs||[],s.dzs.push(a);else if("dzs_s"===e.type&&e.course_dzs_s_id)s.dzs_s=s.dzs_s||[],s.dzs_s.push({id:e.course_dzs_s_id,sn:e.sn||l+1});else if("job"===e.type)s.job=s.job||[],s.job.push(a);else if("ksdg"===e.type&&e.course_ksdg_id)s.ksdg.push({id:e.course_ksdg_id,sn:e.sn||l+1});else if("jc"===e.type&&e.course_jc_id)s.jc.push({id:e.course_jc_id,sn:e.sn||l+1});else if("job_source"===e.type){let t=e.jobbank_source_id;if(!t&&e.id){const s=String(e.id).split("_");t=parseInt(s[s.length-1])}if("string"==typeof t&&t.includes("_")){const e=t.split("_");t=parseInt(e[e.length-1])||t}t&&s.job_source.push({jobbank_source_id:t,ver:e.ver||"",title:e.title,sn:e.sn||l+1})}}else if("video"===e.type&&e.course_video_id)i.video.push({id:e.course_video_id,sn:e.sn||l+1});else if("dzs"===e.type)i.dzs.push(a);else if("dzs_s"===e.type&&e.course_dzs_s_id)s.dzs_s.push({id:e.course_dzs_s_id,sn:e.sn||l+1});else if("job"===e.type)i.job.push(a);else if("ksdg"===e.type&&e.course_ksdg_id)s.ksdg.push({id:e.course_ksdg_id,sn:e.sn||l+1});else if("jc"===e.type&&e.course_jc_id)s.jc.push({id:e.course_jc_id,sn:e.sn||l+1});else if("job_source"===e.type){let t=e.jobbank_source_id;if(!t&&e.id){const s=String(e.id).split("_");t=parseInt(s[s.length-1])}if("string"==typeof t&&t.includes("_")){const e=t.split("_");t=parseInt(e[e.length-1])||t}t&&s.job_source.push({jobbank_source_id:t,ver:e.ver||"",title:e.title,sn:e.sn||l+1})}}));a.isLeaf;Object.keys(i).forEach((s=>{if(i[s].length>0){const d={};d[s]=i[s];const o={course_info_id:Ts.courseInfoId,ols_course_chapter_id:t?null:a.chapterId,bm_plate_id:null==l?void 0:l.plate_id,bm_plate_type_id:null==l?void 0:l.bm_plate_type_id,resource:s,resource_ids:JSON.stringify(d)};e.push(o)}}))}if(a.children&&a.children.length>0)for(const e of a.children)i(e)};for(const a of Ks.value)i(a);if(Object.keys(s).forEach((t=>{if(s[t].length>0){const a={};a[t]=s[t];const i={course_info_id:Ts.courseInfoId,ols_course_chapter_id:null,bm_plate_id:null==l?void 0:l.plate_id,bm_plate_type_id:null==l?void 0:l.bm_plate_type_id,resource:t,resource_ids:JSON.stringify(a)};e.push(i)}})),e.length>0)await J(e);else{const e={course_info_id:Ts.courseInfoId,ols_course_chapter_id:null,bm_plate_id:null==l?void 0:l.plate_id,bm_plate_type_id:null==l?void 0:l.bm_plate_type_id,resource:"",resource_ids:""};await J([e])}a.success("保存成功"),qs("save")}catch(e){a.error("保存失败")}finally{Us.value=!1}else a.error("缺少课程信息ID")},lt=()=>{cl.value.visible=!0,cl.value.method="byOrder"},tt=async()=>{try{cl.value.loading=!0;const e=Ys.value.filter((e=>!e.isLeaf)),s=Zs.value;if(0===e.length)return void a.warning("没有可用的左侧章节，无法进行批量添加");if(0===s.length)return void a.warning("没有可用的右侧资源，无法进行批量添加");if(0===s.reduce(((e,s)=>{var l;return e+((null==(l=s.children)?void 0:l.filter((e=>e.isResource)).length)||0)}),0))return void a.warning("没有可分配的资源，无法进行批量添加");"byOrder"===cl.value.method?await at():"byName"===cl.value.method&&await it(),a.success("批量添加完成"),cl.value.visible=!1}catch(e){a.error("批量添加失败")}finally{cl.value.loading=!1}},at=async()=>{const e=Ys.value.filter((e=>!e.isLeaf)),s=Zs.value;let l=0;const t=[];for(let i=0;i<s.length;i++){const a=s[i];if(a.children&&a.children.length>0){let s=null;if(i<e.length)s=e[i];else{s=e[i%e.length]}if(s){const e=[...a.children];for(const l of e)l.isResource&&t.push({resource:{...l},targetChapter:s})}else l+=a.children.filter((e=>e.isResource)).length}}for(const i of t)try{Vl(i.resource,i.targetChapter)?0:l++}catch(a){l++}Cl(t),Dl()},it=async()=>{const e=Ys.value.filter((e=>!e.isLeaf)),s=Zs.value;const l=new Set,t=[];for(const i of e){const e=dt(i.label,s);if(e&&e.children&&e.children.length>0){const s=[...e.children];for(const e of s)e.isResource&&t.push({resource:{...e},targetChapter:i});l.add(e.id)}}if(e.length>0){const a=e[0];for(const e of s)if(!l.has(e.id)&&e.children&&e.children.length>0){const s=[...e.children];for(const e of s)e.isResource&&t.push({resource:{...e},targetChapter:a})}}for(const i of t)try{Vl(i.resource,i.targetChapter)?0:0}catch(a){0}Cl(t),Dl()},dt=(e,s)=>{const l=ot(e);for(const t of s){const e=ot(t.label);if(l.number&&e.number&&l.number===e.number)return t;if(l.keywords.length>0&&e.keywords.length>0){if(l.keywords.filter((s=>e.keywords.some((e=>e.includes(s)||s.includes(e))))).length>0)return t}}return null},ot=e=>{const s=e.match(/第([一二三四五六七八九十\d]+)章/);let l=null;if(s){const e=s[1],t={"一":1,"二":2,"三":3,"四":4,"五":5,"六":6,"七":7,"八":8,"九":9,"十":10};t[e]?l=t[e]:/^\d+$/.test(e)&&(l=parseInt(e,10))}return{number:l,keywords:e.replace(/第[一二三四五六七八九十\d]+章\s*/,"").split(/[\s\-_、，,]/).filter((e=>e.length>1)).map((e=>e.trim()))}},rt=()=>{Ms.value=null,Gs.value="",Hs.value=null,Js.value="",Ps.value=[],Fs.value=[],Ws.value=[],Qs.value=[],Ys.value=[],Ks.value=[],Xs.value=[],Zs.value=[],el.value.clear(),Bs.value=!1,Us.value=!1,As.value=!1,qs("close")};return(e,s)=>{const l=i("el-option"),t=i("el-select"),a=i("el-button"),S=i("el-icon"),D=i("el-tooltip"),E=i("el-tag"),O=i("el-tree"),L=i("el-radio"),N=i("el-radio-group"),T=i("el-input"),q=i("el-dialog"),A=i("v-md-preview");return o(),d(r,null,[c(q,{modelValue:As.value,"onUpdate:modelValue":s[8]||(s[8]=e=>As.value=e),title:e.title,fullscreen:"","close-on-click-modal":!1,"close-on-press-escape":!0,"destroy-on-close":"",class:"assign-resource-dialog",onClose:rt},{footer:u((()=>[n("div",Ee,[c(a,{onClick:rt,disabled:Bs.value||Us.value},{default:u((()=>s[28]||(s[28]=[p("取消")]))),_:1,__:[28]},8,["disabled"]),c(a,{type:"primary",onClick:st,loading:Us.value,disabled:Bs.value},{default:u((()=>s[29]||(s[29]=[p("保存")]))),_:1,__:[29]},8,["loading","disabled"])])])),default:u((()=>[n("div",W,[n("div",G,[s[13]||(s[13]=n("label",null,"板块选择：",-1)),c(t,{modelValue:Ms.value,"onUpdate:modelValue":s[0]||(s[0]=e=>Ms.value=e),placeholder:"请选择板块",onChange:Wl,style:{width:"200px"},size:"small"},{default:u((()=>[(o(!0),d(r,null,_(Ps.value,(e=>(o(),v(l,{key:e.id,label:e.title,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])]),n("div",H,[s[15]||(s[15]=n("label",null,"版本选择：",-1)),c(t,{modelValue:Gs.value,"onUpdate:modelValue":s[1]||(s[1]=e=>Gs.value=e),placeholder:"请选择版本",onChange:Gl,style:{width:"120px","margin-right":"10px"},size:"small"},{default:u((()=>[(o(!0),d(r,null,_(Fs.value,(e=>(o(),v(l,{key:e.ver,label:e.ver,value:e.ver},null,8,["label","value"])))),128))])),_:1},8,["modelValue"]),s[16]||(s[16]=n("label",null,"课程选择：",-1)),c(t,{modelValue:Hs.value,"onUpdate:modelValue":s[2]||(s[2]=e=>Hs.value=e),placeholder:"请选择课程",onChange:Hl,style:{width:"300px","margin-right":"10px"},size:"small",filterable:"",disabled:!Gs.value},{default:u((()=>[(o(!0),d(r,null,_(Ws.value,(e=>(o(),v(l,{key:e.id,label:`${e.course_code} ${e.course_name}`,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue","disabled"]),c(a,{type:"primary",size:"small",disabled:!hl.value,onClick:lt},{default:u((()=>s[14]||(s[14]=[p(" 批量添加 ")]))),_:1,__:[14]},8,["disabled"])])]),n("div",Q,[n("div",Y,[s[18]||(s[18]=n("div",{class:"panel-header"},[n("h3",null,"课程章节"),n("span",{class:"subtitle"},"拖拽右侧资源到对应章节")],-1)),n("div",K,[Bs.value?(o(),d("div",X,[c(S,{class:"is-loading"},{default:u((()=>[c(b(y))])),_:1}),s[17]||(s[17]=p(" 正在加载章节数据... "))])):Ms.value?0===Ks.value.length?(o(),d("div",ee," 暂无章节数据 ")):(o(),d("div",se," 共 "+h(Ks.value.length)+" 个章节 ",1)):(o(),d("div",Z," 请先选择板块 ")),!Bs.value&&Ms.value?(o(),d("div",le," 拖拽提示：从右侧资源拖拽到左侧章节 ")):f("",!0),Bs.value?f("",!0):(o(),v(O,{key:5,ref_key:"chapterTreeRef",ref:vl,data:Ks.value,props:nl,"node-key":"id","expand-on-click-node":!1,"default-expand-all":!0,class:"chapter-tree"},{default:u((({node:e,data:s})=>[n("div",{class:m(["tree-node chapter-drop-zone",{"drag-over":s.isDragOver}]),onDragover:e=>{return l=e,t=s,void(wl&&wl.isResource&&(l.preventDefault(),l.dataTransfer.dropEffect="move",t.isDragOver=!0));var l,t},onDragleave:e=>{s.isDragOver=!1},onDrop:e=>{return l=s,e.preventDefault(),l.isDragOver=!1,void(wl&&wl.isResource&&Il(wl,l));var l}},[n("div",ae,[c(S,{class:"node-icon"},{default:u((()=>[s.isLeaf?(o(),v(b(k),{key:1})):(o(),v(b(g),{key:0}))])),_:2},1024),n("span",ie,h(e.label),1)]),s.resources&&s.resources.length>0?(o(),d("div",de,[(o(!0),d(r,null,_(s.resources,(e=>(o(),d(r,{key:e.id},["job"===e.type?(o(),d("div",oe,[n("div",re,[c(S,{class:"job-icon"},{default:u((()=>[(o(),v(j(gl(e.type))))])),_:2},1024),c(D,{content:xl(e),placement:"top","show-after":500},{default:u((()=>[n("span",ce,h(e.title.length>20?e.title.slice(0,20)+"...":e.title),1)])),_:2},1032,["content"]),n("div",ue,[c(a,{type:"primary",size:"small",icon:b(z),circle:"",class:"preview-btn",onClick:s=>Al(e),title:"预览"},null,8,["icon","onClick"]),c(a,{type:"danger",size:"small",icon:b(x),circle:"",class:"remove-btn",onClick:l=>Sl(s,e)},null,8,["icon","onClick"])])]),n("div",ne,[$l(e,"competence_level")?(o(),v(E,{key:0,size:"mini",type:"info"},{default:u((()=>[p(" 能力: "+h($l(e,"competence_level")),1)])),_:2},1024)):f("",!0),$l(e,"difficulty_level")?(o(),v(E,{key:1,size:"mini",type:"warning"},{default:u((()=>[p(" 难度: "+h($l(e,"difficulty_level")),1)])),_:2},1024)):f("",!0),$l(e,"questiontype")?(o(),v(E,{key:2,size:"mini",type:"success"},{default:u((()=>[p(" 类型: "+h($l(e,"questiontype")),1)])),_:2},1024)):f("",!0)])])):(o(),d("div",_e,[c(E,{type:kl(e.type),size:"small",class:"resource-tag"},{default:u((()=>[c(S,{class:"tag-icon"},{default:u((()=>[(o(),v(j(gl(e.type))))])),_:2},1024),n("span",ve,h(e.title.length>10?e.title.slice(0,10)+"...":e.title),1)])),_:2},1032,["type"]),n("div",pe,[c(a,{type:"primary",size:"small",icon:b(z),circle:"",class:"preview-btn",onClick:s=>Al(e),title:"预览"},null,8,["icon","onClick"]),c(a,{type:"danger",size:"small",icon:b(x),circle:"",class:"remove-btn",onClick:l=>Sl(s,e)},null,8,["icon","onClick"])])]))],64)))),128))])):f("",!0)],42,te)])),_:1},8,["data"]))])]),n("div",fe,[n("div",be,[s[26]||(s[26]=n("h3",null,"可用资源",-1)),c(N,{modelValue:Js.value,"onUpdate:modelValue":s[3]||(s[3]=e=>Js.value=e),size:"small",onChange:Ll},{default:u((()=>[c(L,{label:""},{default:u((()=>[c(S,null,{default:u((()=>[c(b($))])),_:1}),s[19]||(s[19]=p(" 全部 "))])),_:1,__:[19]}),c(L,{label:"video",disabled:!Qs.value.includes("video")},{default:u((()=>[c(S,null,{default:u((()=>[c(b(w))])),_:1}),s[20]||(s[20]=p(" 视频 "))])),_:1,__:[20]},8,["disabled"]),c(L,{label:"dzs",disabled:!Qs.value.includes("dzs")},{default:u((()=>[c(S,null,{default:u((()=>[c(b(I))])),_:1}),s[21]||(s[21]=p(" 电子书 "))])),_:1,__:[21]},8,["disabled"]),c(L,{label:"ksdg",disabled:!Qs.value.includes("ksdg")},{default:u((()=>[c(S,null,{default:u((()=>[c(b(k))])),_:1}),s[22]||(s[22]=p(" 考试大纲 "))])),_:1,__:[22]},8,["disabled"]),c(L,{label:"jc",disabled:!Qs.value.includes("jc")},{default:u((()=>[c(S,null,{default:u((()=>[c(b(I))])),_:1}),s[23]||(s[23]=p(" 教材 "))])),_:1,__:[23]},8,["disabled"]),c(L,{label:"job",disabled:!Qs.value.includes("job")},{default:u((()=>[c(S,null,{default:u((()=>[c(b(V))])),_:1}),s[24]||(s[24]=p(" 试题 "))])),_:1,__:[24]},8,["disabled"]),c(L,{label:"job_source",disabled:!Qs.value.includes("job_source")},{default:u((()=>[c(S,null,{default:u((()=>[c(b(V))])),_:1}),s[25]||(s[25]=p(" 历年真题 "))])),_:1,__:[25]},8,["disabled"])])),_:1},8,["modelValue"])]),n("div",ye,[Bs.value?(o(),d("div",he,[c(S,{class:"is-loading"},{default:u((()=>[c(b(y))])),_:1}),s[27]||(s[27]=p(" 正在加载资源数据... "))])):Hs.value?0===Zs.value.length?(o(),d("div",ge," 暂无资源数据 ")):(o(),d("div",ke,[n("div",je," 共 "+h(Zs.value.length)+" 个资源章节，可拖拽资源到左侧 ",1),n("div",ze,[c(T,{modelValue:sl.value,"onUpdate:modelValue":s[4]||(s[4]=e=>sl.value=e),placeholder:"搜索资源...",size:"small",clearable:"",style:{width:"150px","margin-right":"8px"},onInput:Ol},{prefix:u((()=>[c(S,null,{default:u((()=>[c(b(C))])),_:1})])),_:1},8,["modelValue"]),"job"===rl.value||rl.value&&rl.value.includes("job")?(o(),d(r,{key:0},[c(t,{modelValue:ll.value,"onUpdate:modelValue":s[5]||(s[5]=e=>ll.value=e),placeholder:"能力层次",size:"small",clearable:"",style:{width:"100px","margin-right":"8px"},onChange:Ol},{default:u((()=>[(o(!0),d(r,null,_(il.value,(e=>(o(),v(l,{key:e,label:e,value:e},null,8,["label","value"])))),128))])),_:1},8,["modelValue"]),c(t,{modelValue:tl.value,"onUpdate:modelValue":s[6]||(s[6]=e=>tl.value=e),placeholder:"难度层次",size:"small",clearable:"",style:{width:"80px","margin-right":"8px"},onChange:Ol},{default:u((()=>[(o(!0),d(r,null,_(dl.value,(e=>(o(),v(l,{key:e,label:e,value:e},null,8,["label","value"])))),128))])),_:1},8,["modelValue"]),c(t,{modelValue:al.value,"onUpdate:modelValue":s[7]||(s[7]=e=>al.value=e),placeholder:"题目类型",size:"small",clearable:"",style:{width:"100px","margin-right":"8px"},onChange:Ol},{default:u((()=>[(o(!0),d(r,null,_(ol.value,(e=>(o(),v(l,{key:e,label:e,value:e},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])],64)):f("",!0)])])):(o(),d("div",me," 请先选择课程 ")),Bs.value?f("",!0):(o(),v(O,{key:4,ref_key:"resourceTreeRef",ref:pl,data:Zs.value,props:_l,"node-key":"id","expand-on-click-node":!1,"default-expand-all":!0,class:"resource-tree"},{default:u((({node:e,data:s})=>{return[n("div",{class:m(["tree-node resource-node",{"is-resource":s.isResource}]),draggable:s.isResource,onDragstart:e=>((e,s)=>{var l;s.isResource?(wl=s,e.dataTransfer&&(e.dataTransfer.effectAllowed="move",e.dataTransfer.setData("text/plain",JSON.stringify(s))),null==(l=e.target)||l.classList.add("dragging")):e.preventDefault()})(e,s),onDragend:e=>{return wl=null,void(null==(s=e.target)||s.classList.remove("dragging"));var s}},[c(S,{class:m(["node-icon",(l=s.type,{dzs:"dzs-icon",video:"video-icon",job:"job-icon"}[l]||"")])},{default:u((()=>[(o(),v(j(gl(s.type))))])),_:2},1032,["class"]),s.isResource&&"job"===s.type?(o(),d("div",$e,[n("div",we,[c(D,{content:jl(s),placement:"top","show-after":500},{default:u((()=>[n("span",Ie,h(e.label),1)])),_:2},1032,["content"]),n("div",Ve,[zl(s,"competence_level")?(o(),v(E,{key:0,size:"mini",type:"info"},{default:u((()=>[p(" 能力: "+h(zl(s,"competence_level")),1)])),_:2},1024)):f("",!0),zl(s,"difficulty_level")?(o(),v(E,{key:1,size:"mini",type:"warning"},{default:u((()=>[p(" 难度: "+h(zl(s,"difficulty_level")),1)])),_:2},1024)):f("",!0),zl(s,"questiontype")?(o(),v(E,{key:2,size:"mini",type:"success"},{default:u((()=>[p(" 类型: "+h(zl(s,"questiontype")),1)])),_:2},1024)):f("",!0)])]),n("div",Ce,[c(a,{type:"primary",size:"small",icon:b(z),circle:"",class:"preview-btn-right",onClick:R((e=>Al(s)),["stop"]),title:"预览"},null,8,["icon","onClick"])])])):s.isResource?(o(),d("div",Re,[n("span",Se,h(e.label),1),c(a,{type:"primary",size:"small",icon:b(z),circle:"",class:"preview-btn-right",onClick:R((e=>Al(s)),["stop"]),title:"预览"},null,8,["icon","onClick"])])):(o(),d("span",De,h(e.label),1)),s.isResource?(o(),v(E,{key:3,type:kl(s.type),size:"small",class:"resource-type-tag"},{default:u((()=>[p(h(ml(s.type)),1)])),_:2},1032,["type"])):f("",!0)],42,xe)];var l})),_:1},8,["data"]))])])])])),_:1},8,["modelValue","title"]),c(q,{modelValue:cl.value.visible,"onUpdate:modelValue":s[11]||(s[11]=e=>cl.value.visible=e),title:"批量添加资源",width:"400px","close-on-click-modal":!1,"close-on-press-escape":!0,"append-to-body":""},{footer:u((()=>[n("div",Le,[c(a,{onClick:s[10]||(s[10]=e=>cl.value.visible=!1)},{default:u((()=>s[33]||(s[33]=[p("取消")]))),_:1,__:[33]}),c(a,{type:"primary",onClick:tt,loading:cl.value.loading},{default:u((()=>s[34]||(s[34]=[p("确定")]))),_:1,__:[34]},8,["loading"])])])),default:u((()=>[n("div",Oe,[s[32]||(s[32]=n("p",{style:{"margin-bottom":"20px",color:"#666"}},"请选择批量添加的方式：",-1)),c(N,{modelValue:cl.value.method,"onUpdate:modelValue":s[9]||(s[9]=e=>cl.value.method=e),style:{display:"flex","flex-direction":"column",gap:"12px"}},{default:u((()=>[c(L,{value:"byOrder"},{default:u((()=>s[30]||(s[30]=[n("div",{style:{"margin-left":"8px"}},[n("div",{style:{"font-weight":"500"}},"按章节数量对应"),n("div",{style:{"font-size":"12px",color:"#999","margin-top":"4px"}},"按照左右两边表格中的章顺序进行对应分配")],-1)]))),_:1,__:[30]}),c(L,{value:"byName"},{default:u((()=>s[31]||(s[31]=[n("div",{style:{"margin-left":"8px"}},[n("div",{style:{"font-weight":"500"}},"按章节名称对应"),n("div",{style:{"font-size":"12px",color:"#999","margin-top":"4px"}},"按照左右两边表格中的章名称进行对应分配")],-1)]))),_:1,__:[31]})])),_:1},8,["modelValue"])])])),_:1},8,["modelValue"]),c(q,{modelValue:ul.value.visible,"onUpdate:modelValue":s[12]||(s[12]=e=>ul.value.visible=e),title:`预览 - ${"job"===ul.value.type?"试题":ul.value.type}`,fullscreen:!0,"close-on-press-escape":!0,"close-on-click-modal":!1,"destroy-on-close":"",class:"preview-dialog",onClose:Ul},{footer:u((()=>[n("div",Ns,[c(a,{onClick:Ul},{default:u((()=>s[49]||(s[49]=[p("关闭")]))),_:1,__:[49]})])])),default:u((()=>["job"===ul.value.type&&ul.value.data?(o(),d("div",Ne,[(o(),v(j(b(Bl)),{coursebaseid:ul.value.data.course_base_id,jobbanksourceid:"",jobbankver:"",jobbankid:ul.value.data.id},null,8,["coursebaseid","jobbankid"]))])):"job_source"===ul.value.type&&ul.value.data?(o(),d("div",Te,[(o(),v(j(b(Bl)),{coursebaseid:ul.value.data.course_base_id,jobbanksourceid:ul.value.data.jobbank_source_id,jobbankver:ul.value.data.jobbank_ver,jobbankid:""},null,8,["coursebaseid","jobbanksourceid","jobbankver"]))])):"ksdg"===ul.value.type&&ul.value.data?(o(),d("div",qe,[n("div",Ae,[c(A,{text:ul.value.data.content||"暂无内容",style:{height:"calc(100vh - 150px)",overflow:"auto"}},null,8,["text"])])])):"jc"===ul.value.type&&ul.value.data?(o(),d("div",Ue,[n("div",Be,[n("div",Je,[n("div",Pe,[n("img",{src:ul.value.data.textbook_url,alt:ul.value.data.textbook_name,class:"cover-image",onError:Nl},null,40,Me)]),n("div",Fe,[n("div",We,[s[35]||(s[35]=n("span",{class:"info-label"},"教材名称：",-1)),n("span",Ge,h(ul.value.data.textbook_name||ul.value.data.course_name),1)]),n("div",He,[s[36]||(s[36]=n("span",{class:"info-label"},"主编：",-1)),n("span",Qe,h(ul.value.data.textbook_editor),1)]),n("div",Ye,[s[37]||(s[37]=n("span",{class:"info-label"},"出版社：",-1)),n("span",Ke,h(ul.value.data.publication_info),1)]),ul.value.data.course_code?(o(),d("div",Xe,[s[38]||(s[38]=n("span",{class:"info-label"},"课程代码：",-1)),n("span",Ze,h(ul.value.data.course_code),1)])):f("",!0),ul.value.data.course_credits?(o(),d("div",es,[s[39]||(s[39]=n("span",{class:"info-label"},"学分：",-1)),n("span",ss,h(ul.value.data.course_credits),1)])):f("",!0),ul.value.data.course_type?(o(),d("div",ls,[s[40]||(s[40]=n("span",{class:"info-label"},"课程类型：",-1)),n("span",ts,h(ul.value.data.course_type),1)])):f("",!0),ul.value.data.ver?(o(),d("div",as,[s[41]||(s[41]=n("span",{class:"info-label"},"版本：",-1)),n("span",is,h(ul.value.data.ver),1)])):f("",!0),ul.value.data.url?(o(),d("div",ds,[c(a,{type:"primary",icon:b(k),onClick:ql},{default:u((()=>s[42]||(s[42]=[p(" 查看教材PDF ")]))),_:1,__:[42]},8,["icon"])])):f("",!0)])])])])):"video"===ul.value.type&&ul.value.data?(o(),d("div",os,[n("div",rs,[n("div",cs,[n("h3",us,h(ul.value.data.course_video_title||ul.value.data.node_name||"视频预览"),1),n("div",ns,[ul.value.data.course_name?(o(),v(E,{key:0,type:"info",size:"small"},{default:u((()=>[p(h(ul.value.data.course_name),1)])),_:1})):f("",!0),ul.value.data.chapter_name?(o(),v(E,{key:1,type:"success",size:"small"},{default:u((()=>[p(h(ul.value.data.chapter_name),1)])),_:1})):f("",!0),ul.value.data.node_name?(o(),v(E,{key:2,type:"warning",size:"small"},{default:u((()=>[p(h(ul.value.data.node_name),1)])),_:1})):f("",!0)])]),n("div",_s,[ul.value.data.mp4_url?(o(),d("video",{key:0,src:ul.value.data.mp4_url,controls:"",preload:"metadata",class:"video-player",onError:Tl}," 您的浏览器不支持视频播放。 ",40,vs)):(o(),d("div",ps,[c(S,{size:"48",color:"#909399"},{default:u((()=>[c(b(w))])),_:1}),s[43]||(s[43]=n("p",null,"暂无视频资源",-1))]))]),n("div",fs,[ul.value.data.course_code?(o(),d("div",bs,[s[44]||(s[44]=n("span",{class:"detail-label"},"课程代码：",-1)),n("span",ys,h(ul.value.data.course_code),1)])):f("",!0),ul.value.data.chapter_code?(o(),d("div",hs,[s[45]||(s[45]=n("span",{class:"detail-label"},"章节代码：",-1)),n("span",ms,h(ul.value.data.chapter_code),1)])):f("",!0),ul.value.data.node_code?(o(),d("div",gs,[s[46]||(s[46]=n("span",{class:"detail-label"},"节点代码：",-1)),n("span",ks,h(ul.value.data.node_code),1)])):f("",!0),ul.value.data.course_video_id?(o(),d("div",js,[s[47]||(s[47]=n("span",{class:"detail-label"},"视频ID：",-1)),n("span",zs,h(ul.value.data.course_video_id),1)])):f("",!0)])])])):"dzs"===ul.value.type&&ul.value.data?(o(),d("div",xs,[n("div",$s,[n("div",ws,[n("h3",Is,h(ul.value.data.chap_title||"电子书章节预览"),1),n("div",Vs,[ul.value.data.chap_num?(o(),v(E,{key:0,type:"primary",size:"small"},{default:u((()=>[p(" 第"+h(ul.value.data.chap_num)+"章 ",1)])),_:1})):f("",!0),ul.value.data.course_dzs_id?(o(),v(E,{key:1,type:"info",size:"small"},{default:u((()=>[p(" 电子书ID: "+h(ul.value.data.course_dzs_id),1)])),_:1})):f("",!0),ul.value.data.id?(o(),v(E,{key:2,type:"success",size:"small"},{default:u((()=>[p(" 章节ID: "+h(ul.value.data.id),1)])),_:1})):f("",!0)])]),n("div",Cs,[c(A,{text:ul.value.data.chap_content||"暂无内容",style:{height:"calc(100vh - 200px)",overflow:"auto"}},null,8,["text"])])])])):"dzs_s"===ul.value.type&&ul.value.data?(o(),d("div",Rs,[n("div",Ss,[n("div",Ds,[n("h3",Es,h(ul.value.data.title||"电子书预览"),1),s[48]||(s[48]=n("div",{class:"dzs-info"},null,-1))]),n("div",Os,[c(A,{text:ul.value.data.content||"暂无内容",style:{height:"calc(100vh - 200px)",overflow:"auto"}},null,8,["text"])])])])):ul.value.data?(o(),d("div",Ls,[n("h3",null,h(ul.value.data.title),1),n("p",null,"类型: "+h(ul.value.type),1),n("pre",null,h(JSON.stringify(ul.value.data,null,2)),1)])):f("",!0)])),_:1},8,["modelValue","title"])],64)}}}),[["__scopeId","data-v-dbca2d12"]]);export{Ts as A};
