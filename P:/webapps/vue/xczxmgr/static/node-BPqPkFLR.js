import{o as e,p as l,u as a}from"./kcmgr-_69jTmcm.js";import{d as t,N as o,K as d,r as n,o as u,a as r,L as s,c as i,b as _,e as c,j as p,w as v,p as m,t as w,M as b,h as y,m as h,q as g,a9 as k,aa as f,F as V,f as x,E as C,_ as U}from"./index-BERmxY3Y.js";const D={class:"kcnode-list page"},q={class:"header"},P={class:"body"},E={key:0},j={key:1},z={key:2},R={key:0},F={key:1},N={key:2},T={key:0,style:{color:"blue"}},A={key:1,style:{color:"#909399"}},I={key:0},K={key:0},L={key:0},M={style:{width:"100%","text-align":"center"}},B={class:"dialog-footer"},G=U(t({__name:"node",setup(t){o();const U=d({tableDataData:[],loading:!1}),G=n("calc(100vh - 180px)"),H=n(!1),J=n("展开全部"),O=n(),Q=n(!1),S=n(!1),W=n("添加课程节点"),X=n("100px"),Y=n(),Z=n({id:"",course_code:"",course_name:"",chapter_code:"",chapter_name:"",node_code:"",node_name:"",node_status:!0,level_type:"node",display_order:0,create_date:"",create_user:"",is_knowledge_point_linked:!1,knowledgePoints:[],linked_knowledge_point_name:"",question_count:0,text_image:"",video:"",case_study:"",remark:"",parent_id:0}),$=d({course_code:[{required:!0,message:"请输入课程代码",trigger:"blur"}],course_name:[{required:!0,message:"请输入课程名称",trigger:"blur"}],chapter_name:[{required:e=>"chapter"===e.level_type,message:"请输入章节名称",trigger:"blur"}],node_name:[{required:e=>"node"===e.level_type,message:"请输入节点名称",trigger:"blur"}]}),ee=n([{value:"会计基础概念",label:"会计基础概念"},{value:"会计职能",label:"会计职能"},{value:"会计目标",label:"会计目标"},{value:"资产",label:"资产"},{value:"流动资产",label:"流动资产"},{value:"非流动资产",label:"非流动资产"},{value:"经济法基础",label:"经济法基础"},{value:"法律特征",label:"法律特征"},{value:"调整对象",label:"调整对象"},{value:"经济活动",label:"经济活动"},{value:"统计学基础",label:"统计学基础"},{value:"数据分析",label:"数据分析"},{value:"统计学应用",label:"统计学应用"},{value:"数据挖掘",label:"数据挖掘"},{value:"集中趋势",label:"集中趋势"},{value:"平均值",label:"平均值"},{value:"中位数",label:"中位数"},{value:"管理基础",label:"管理基础"},{value:"管理理论",label:"管理理论"},{value:"管理环境",label:"管理环境"},{value:"外部环境",label:"外部环境"},{value:"计划职能",label:"计划职能"},{value:"战略规划",label:"战略规划"},{value:"营销基础",label:"营销基础"},{value:"市场分析",label:"市场分析"},{value:"营销目标",label:"营销目标"},{value:"市场份额",label:"市场份额"},{value:"消费者行为",label:"消费者行为"},{value:"购买决策",label:"购买决策"}]),le=async()=>{U.loading=!0;try{const l={showAll:Q.value?1:0},a=await e(l);a&&a.data?U.tableDataData=a.data:U.tableDataData=[]}catch(l){C.error("获取课程节点数据失败")}finally{U.loading=!1}},ae=()=>{!1===Z.value.node_status&&C({message:"你正在禁用课程节点，禁用后该节点将不可用！",type:"warning"})},te=e=>!1===e.node_status?"disabled-row":"",oe=(e,l)=>{e.forEach((e=>{O.value.toggleRowExpansion(e,l),e.children&&de(e.children,l)}))},de=(e,l)=>{e.forEach((e=>{O.value.toggleRowExpansion(e,l),e.children&&de(e.children,l)}))};return u((()=>{le()})),(e,t)=>{const o=r("el-button"),d=r("el-table-column"),n=r("el-switch"),u=r("el-icon"),Q=r("el-table"),de=r("el-input"),ne=r("el-form-item"),ue=r("el-radio"),re=r("el-radio-group"),se=r("el-option"),ie=r("el-select"),_e=r("el-input-number"),ce=r("el-form"),pe=r("el-dialog"),ve=s("loading");return _(),i("div",D,[c("div",q,[p(o,{type:"success",plain:"",size:"default",icon:"refresh",round:"",onClick:le}),p(o,{type:"primary",plain:"",onClick:t[0]||(t[0]=e=>{H.value?(H.value=!1,J.value="展开全部",oe(U.tableDataData,!1)):(H.value=!0,J.value="全部折叠",oe(U.tableDataData,!0))}),size:"default",round:"",icon:"View","aria-label":""},{default:v((()=>[m(w(J.value),1)])),_:1})]),c("div",P,[b((_(),y(Q,{border:"",height:G.value,data:U.tableDataData,"row-class-name":te,"row-key":"id",class:"kcNodeTable","default-expand-all":!1,"highlight-current-row":"",ref_key:"tablexTree",ref:O,"tree-props":{children:"children",hasChildren:"hasChildren"}},{default:v((()=>[p(d,{type:"index",label:"#",align:"center",width:"60"}),p(d,{label:"课程代码",align:"center","min-width":"130"},{default:v((e=>["course"===e.row.level_type?(_(),i("span",E,w(e.row.course_code),1)):"chapter"===e.row.level_type?(_(),i("span",j,w(e.row.chapter_code),1)):"node"===e.row.level_type?(_(),i("span",z,w(e.row.node_code),1)):h("",!0)])),_:1}),p(d,{label:"名称",align:"center","min-width":"200"},{default:v((e=>["course"===e.row.level_type?(_(),i("span",R,w(e.row.course_name),1)):"chapter"===e.row.level_type?(_(),i("span",F,w(e.row.chapter_name),1)):"node"===e.row.level_type?(_(),i("span",N,w(e.row.node_name),1)):h("",!0)])),_:1}),p(d,{prop:"node_status",label:"状态",align:"center","min-width":"100"},{default:v((e=>[p(n,{modelValue:e.row.node_status,"onUpdate:modelValue":l=>e.row.node_status=l,"active-text":"正常","inactive-text":"禁用","active-value":!0,"inactive-value":!1,onChange:l=>(async e=>{try{const l={id:e.id,node_status:e.node_status},t=await a(l);t&&t.data&&C({message:"更新成功！",type:"success"})}catch(l){C.error("更新课程节点状态失败")}})(e.row)},null,8,["modelValue","onUpdate:modelValue","onChange"])])),_:1}),p(d,{prop:"create_date",label:"添加时间",align:"center","min-width":"150"},{default:v((e=>[m(w(e.row.create_date),1)])),_:1}),p(d,{prop:"create_user",label:"添加人",align:"center","min-width":"100"}),p(d,{label:"是否关联知识点",align:"center","min-width":"120"},{default:v((e=>[e.row.is_knowledge_point_linked?(_(),i("span",T,"是")):(_(),i("span",A,"否"))])),_:1}),p(d,{label:"关联知识点名称",align:"center","min-width":"150","show-overflow-tooltip":""},{default:v((e=>[m(w(e.row.linked_knowledge_point_name||"-"),1)])),_:1}),p(d,{label:"题量",align:"center","min-width":"80"},{default:v((e=>[m(w(e.row.question_count||0),1)])),_:1}),p(d,{label:"图文",align:"center","min-width":"80"},{default:v((e=>[e.row.text_image?(_(),i("span",I,"有")):h("",!0)])),_:1}),p(d,{label:"视频",align:"center","min-width":"80"},{default:v((e=>[e.row.video?(_(),i("span",K,"有")):h("",!0)])),_:1}),p(d,{label:"案例",align:"center","min-width":"80"},{default:v((e=>[e.row.case_study?(_(),i("span",L,"有")):h("",!0)])),_:1}),p(d,{label:"操作",align:"center",width:"280"},{default:v((e=>["course"!==e.row.level_type?(_(),y(o,{key:0,link:"",type:"primary",size:"small",onClick:l=>{return a=e.row,W.value="编辑课程节点",Z.value={...a,knowledgePoints:a.linked_knowledge_point_name?a.linked_knowledge_point_name.split(",").map((e=>e.trim())):[]},void(S.value=!0);var a}},{default:v((()=>[p(u,null,{default:v((()=>[p(g(k))])),_:1}),t[17]||(t[17]=m("编辑"))])),_:2,__:[17]},1032,["onClick"])):h("",!0),"node"!==e.row.level_type?(_(),y(o,{key:1,link:"",type:"primary",size:"small",onClick:l=>(e=>{let l="",a="";"course"===e.level_type?(l="chapter",a="添加章节"):"chapter"===e.level_type&&(l="node",a="添加节点"),W.value=a,Z.value={id:"",course_code:e.course_code,course_name:e.course_name,chapter_code:"node"===l?e.chapter_code:"",chapter_name:"node"===l?e.chapter_name:"",node_code:"",node_name:"",node_status:!0,level_type:l,display_order:0,create_date:"",create_user:"",is_knowledge_point_linked:!1,knowledgePoints:[],linked_knowledge_point_name:"",question_count:0,text_image:"",video:"",case_study:"",remark:"",parent_id:e.id},S.value=!0})(e.row)},{default:v((()=>[p(u,null,{default:v((()=>[p(g(f))])),_:1}),t[18]||(t[18]=m("添加子节点"))])),_:2,__:[18]},1032,["onClick"])):h("",!0)])),_:1})])),_:1},8,["height","data"])),[[ve,U.loading]])]),p(pe,{modelValue:S.value,"onUpdate:modelValue":t[16]||(t[16]=e=>S.value=e),title:W.value,"custom-class":"nodeDialogClass",draggable:"","close-on-press-escape":!1,"close-on-click-modal":!1},{footer:v((()=>[c("span",B,[p(o,{onClick:t[14]||(t[14]=e=>S.value=!1)},{default:v((()=>t[21]||(t[21]=[m("取消")]))),_:1,__:[21]}),p(o,{type:"success",onClick:t[15]||(t[15]=e=>(async()=>{Y.value&&await Y.value.validate((async e=>{if(e)try{const e={...Z.value};e.linked_knowledge_point_name=e.knowledgePoints.join(","),e.is_knowledge_point_linked=e.knowledgePoints.length>0,"course"===e.level_type?(e.chapter_name="",e.node_name=""):"chapter"===e.level_type&&(e.node_name="");const a=await l(e);a&&a.data&&(C({message:"保存成功！",type:"success"}),S.value=!1,le())}catch(a){C.error("保存课程节点失败")}}))})())},{default:v((()=>t[22]||(t[22]=[m("保存")]))),_:1,__:[22]})])])),default:v((()=>[c("div",M,[p(ce,{model:Z.value,rules:$,ref_key:"nodeFormRef",ref:Y,inline:!0,"label-width":X.value},{default:v((()=>[p(ne,{label:"课程代码",prop:"course_code"},{default:v((()=>[p(de,{modelValue:Z.value.course_code,"onUpdate:modelValue":t[1]||(t[1]=e=>Z.value.course_code=e),disabled:!0,style:{width:"235px"}},null,8,["modelValue"])])),_:1}),p(ne,{label:"课程名称",prop:"course_name"},{default:v((()=>[p(de,{modelValue:Z.value.course_name,"onUpdate:modelValue":t[2]||(t[2]=e=>Z.value.course_name=e),disabled:!0,style:{width:"235px"}},null,8,["modelValue"])])),_:1}),"chapter"===Z.value.level_type||"node"===Z.value.level_type?(_(),y(ne,{key:0,label:"章节名称",prop:"chapter_name"},{default:v((()=>[p(de,{modelValue:Z.value.chapter_name,"onUpdate:modelValue":t[3]||(t[3]=e=>Z.value.chapter_name=e),style:{width:"235px"}},null,8,["modelValue"])])),_:1})):h("",!0),"chapter"===Z.value.level_type||"node"===Z.value.level_type?(_(),y(ne,{key:1,label:"节点名称",prop:"node_name"},{default:v((()=>[p(de,{modelValue:Z.value.node_name,"onUpdate:modelValue":t[4]||(t[4]=e=>Z.value.node_name=e),style:{width:"235px"}},null,8,["modelValue"])])),_:1})):h("",!0),p(ne,{label:"节点状态",prop:"node_status"},{default:v((()=>[p(re,{modelValue:Z.value.node_status,"onUpdate:modelValue":t[7]||(t[7]=e=>Z.value.node_status=e),style:{width:"235px"}},{default:v((()=>[p(ue,{label:!0,onChange:t[5]||(t[5]=e=>ae())},{default:v((()=>t[19]||(t[19]=[m("正常")]))),_:1,__:[19]}),p(ue,{label:!1,onChange:t[6]||(t[6]=e=>ae())},{default:v((()=>t[20]||(t[20]=[m("禁用")]))),_:1,__:[20]})])),_:1},8,["modelValue"])])),_:1}),"chapter"===Z.value.level_type||"node"===Z.value.level_type?(_(),i(V,{key:2},[p(ne,{label:"关联知识点",prop:"knowledgePoints"},{default:v((()=>[p(ie,{modelValue:Z.value.knowledgePoints,"onUpdate:modelValue":t[8]||(t[8]=e=>Z.value.knowledgePoints=e),multiple:"",filterable:"","allow-create":"","default-first-option":"",style:{width:"235px"},placeholder:"请选择关联知识点"},{default:v((()=>[(_(!0),i(V,null,x(ee.value,(e=>(_(),y(se,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),p(ne,{label:"题量",prop:"question_count"},{default:v((()=>[p(_e,{modelValue:Z.value.question_count,"onUpdate:modelValue":t[9]||(t[9]=e=>Z.value.question_count=e),min:0,style:{width:"235px"}},null,8,["modelValue"])])),_:1}),p(ne,{label:"图文",prop:"text_image"},{default:v((()=>[p(de,{modelValue:Z.value.text_image,"onUpdate:modelValue":t[10]||(t[10]=e=>Z.value.text_image=e),style:{width:"235px"}},null,8,["modelValue"])])),_:1}),p(ne,{label:"视频",prop:"video"},{default:v((()=>[p(de,{modelValue:Z.value.video,"onUpdate:modelValue":t[11]||(t[11]=e=>Z.value.video=e),style:{width:"235px"}},null,8,["modelValue"])])),_:1}),p(ne,{label:"案例",prop:"case_study"},{default:v((()=>[p(de,{modelValue:Z.value.case_study,"onUpdate:modelValue":t[12]||(t[12]=e=>Z.value.case_study=e),style:{width:"235px"}},null,8,["modelValue"])])),_:1})],64)):h("",!0),p(ne,{label:"备注",prop:"remark"},{default:v((()=>[p(de,{modelValue:Z.value.remark,"onUpdate:modelValue":t[13]||(t[13]=e=>Z.value.remark=e),type:"textarea",style:{width:"590px"}},null,8,["modelValue"])])),_:1})])),_:1},8,["model","rules","label-width"])])])),_:1},8,["modelValue","title"])])}}}),[["__scopeId","data-v-d0d57ed4"]]);export{G as default};
