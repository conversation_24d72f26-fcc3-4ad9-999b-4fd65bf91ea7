import{G as e,e as l,S as a,g as t}from"./question-DWIQ7W1h.js";import{t as s}from"./tools-CvIkJR06.js";import{d as i,ab as n,r as o,o as u,S as d,a as r,L as p,c as v,b as c,j as _,w as m,e as g,t as h,p as y,m as f,h as w,F as b,f as x,M as V,E as k,v as q,K as U,Y as C}from"./index-BERmxY3Y.js";const A={style:{"margin-bottom":"16px",display:"flex","justify-content":"space-between","align-items":"center"}},j={style:{height:"700px","overflow-y":"auto"}},Q={style:{float:"left"}},N={style:{float:"right",color:"#67C23A","font-size":"13px"}},S={style:{width:"900px",height:"410px"}},z={style:{width:"900px",height:"310px"}},B={style:{width:"900px",height:"310px"}},T={key:0,class:"options_div"},R={style:{height:"210px"}},M={style:{width:"100%"}},$={style:{display:"flex","justify-content":"space-between","align-items":"center",float:"left",margin:"10px"}},E={style:{height:"310px",width:"900px",margin:"0 10px",flex:"1"}},I={style:{display:"flex","justify-content":"space-between","align-items":"center",float:"left",margin:"10px"}},P={key:1,class:"options_div"},D={style:{width:"800px",height:"310px",margin:"10px"}},L={key:0,style:{width:"800px"}},F={style:{height:"210px"}},J={style:{width:"100%",margin:"10px"}},G={style:{height:"210px",width:"900px"}},K={style:{width:"100%",margin:"10px"}},O={key:1},Y={style:{width:"100%",margin:"10px"}},H={style:{height:"310px",width:"900px"}},W={style:{width:"100%",margin:"10px"}},X={style:{height:"210px",width:"900px"}},Z={style:{width:"100%",margin:"10px"}},ee={style:{width:"100%",margin:"10px"}},le={style:{"margin-top":"16px","text-align":"right"}},ae=i({__name:"up_question_job",props:{jobbankid:{}},setup(t){const{proxy:i}=n(),U=t,C=o(U.jobbankid),ae=o([]),te=o([]),se=o(!1),ie=o(!1),ne=o([]);function oe(){if(!ae.value||!ae.value.title)return;Array.isArray(ae.value.title.options)||(ae.value.title.options=[]);const e=ae.value.title.options.length;let l="";if(e<26)l=String.fromCharCode(65+e);else{l=String.fromCharCode(65+Math.floor(e/26)-1)+String.fromCharCode(65+e%26)}ae.value.title.options.push({listNo:l,answer:!1,option:""})}function ue(e,l){if(!ae.value||!ae.value.title||!Array.isArray(ae.value.title.options))return;const a=ae.value.title.options.map(((e,l)=>e.answer?l:-1)).filter((e=>-1!==e)).map((e=>String.fromCharCode(65+e))).join(",");ae.value.title.answer=a,ae.value.answer=a}function de(){if(!ae.value||!ae.value.title)return;Array.isArray(ae.value.title.subQuestions)||(ae.value.title.subQuestions=[]),ae.value.title.subQuestions.push({title:"",options:[{listNo:"A",answer:!1,option:""},{listNo:"B",answer:!1,option:""}],answer:"",answer_parsing:""});const e=ae.value.title.subQuestions.length-1;ne.value.push(e+"题号")}function re(e,l,a){if(!ae.value||!ae.value.title||!Array.isArray(ae.value.title.subQuestions))return;const t=ae.value.title.subQuestions[e];if(!t||!Array.isArray(t.options))return;const s=t.options.map(((e,l)=>e.answer?l:-1)).filter((e=>-1!==e)).map((e=>String.fromCharCode(65+e))).join(",");t.answer=s,ae.value.title.subQuestions[e].answer=s,pe()}function pe(){if(!ae.value||!ae.value.title||!Array.isArray(ae.value.title.subQuestions))return;const e=ae.value.title.subQuestions.filter((e=>e&&Array.isArray(e.options))).map(((e,l)=>({sn:l+1,answer:e.options.map(((e,l)=>e.answer?l:-1)).filter((e=>-1!==e)).map((e=>String.fromCharCode(65+e))).join(",")})));ae.value.answer=JSON.stringify(e,null,0)}const ve=async()=>{se.value=!0;try{const e={jobbankid:C.value};l(e).then((e=>{var l,a,t,i;ae.value=e.data,(null==(a=null==(l=ae.value)?void 0:l.title)?void 0:a.title)&&(ae.value.title.title=s.format2Md(ae.value.title.title)),(null==(i=null==(t=ae.value)?void 0:t.title)?void 0:i.subQuestions)&&Array.isArray(ae.value.title.subQuestions)?ne.value=ae.value.title.subQuestions.map(((e,l)=>l+"题号")):ne.value=[],(()=>{if(ae.value&&ae.value.title&&Array.isArray(ae.value.title.options)){const e=ae.value.title.answer;if(e){const l=e.split(",").map((e=>e.trim())).filter(Boolean);ae.value.title.options.forEach(((e,a)=>{const t=String.fromCharCode(65+a);e.answer=l.includes(t)}))}}ae.value&&ae.value.title&&Array.isArray(ae.value.title.subQuestions)&&ae.value.title.subQuestions.forEach((e=>{if(Array.isArray(e.options)&&"string"==typeof e.answer){const l=e.answer.split(",").map((e=>e.trim())).filter(Boolean);e.options.forEach(((e,a)=>{const t=String.fromCharCode(65+a);e.answer=l.includes(t)}))}}))})(),se.value=!1})).catch((e=>{se.value=!1}))}catch(e){k.error("题库获取失败")}finally{se.value=!1}},ce=async()=>{ie.value=!0;try{q.confirm("再次确认是否保存题库编号【"+C.value+"】的数据, 是否继续保存?","提示",{confirmButtonText:"确认保存",cancelButtonText:"取消",type:"warning"}).then((()=>{const e={data:i.$encryptionPlugin.encrypt(ae.value)};i.$encryptionPlugin;a(e).then((e=>{200===e.code&&e.data>0?(k.success("保存成功!"),ve()):k({type:"info",message:"保存失败!"+e.msg}),ie.value=!1})).catch((e=>{ie.value=!1}))})).catch((()=>{k({type:"info",message:"取消保存!"})}))}catch(e){k.error("保存失败")}finally{ie.value=!1}};return u((()=>{e({}).then((e=>{200==e.code?te.value=e.data:k.error(e.msg)})),ve()})),d((()=>U.jobbankid),(e=>{C.value=e,ve()})),(e,l)=>{const a=r("el-tag"),t=r("el-input"),s=r("el-radio"),i=r("el-radio-group"),n=r("el-form-item"),o=r("el-option"),u=r("el-select"),d=r("el-input-number"),k=r("v-md-editor"),q=r("el-form"),U=r("el-button"),ve=r("el-table-column"),_e=r("el-checkbox"),me=r("el-table"),ge=r("el-collapse-item"),he=r("el-collapse"),ye=r("el-card"),fe=p("loading");return c(),v("div",null,[_(ye,null,{default:m((()=>[g("div",A,[g("div",null,[g("span",null,"题库维护（ Job ID: "+h(C.value)+" ） 课程："+h(ae.value.kc_bm)+" "+h(ae.value.kc_mc),1)]),g("div",null,[_(a,{class:"el_tag_5"},{default:m((()=>l[21]||(l[21]=[y("题型")]))),_:1,__:[21]}),_(t,{modelValue:ae.value.questiontype,"onUpdate:modelValue":l[0]||(l[0]=e=>ae.value.questiontype=e),placeholder:"题型",style:{width:"200px",margin:"0 10px"}},null,8,["modelValue"])])]),g("div",j,[_(q,{model:ae.value,inline:!0,"label-width":"100px"},{default:m((()=>[_(n,{label:"类型"},{default:m((()=>[_(i,{modelValue:ae.value.is_autoscore,"onUpdate:modelValue":l[1]||(l[1]=e=>ae.value.is_autoscore=e),style:{width:"200px"}},{default:m((()=>[_(s,{label:1},{default:m((()=>l[22]||(l[22]=[y("客观题")]))),_:1,__:[22]}),_(s,{label:0},{default:m((()=>l[23]||(l[23]=[y("主观题")]))),_:1,__:[23]})])),_:1},8,["modelValue"])])),_:1}),_(n,{label:"状态"},{default:m((()=>[_(i,{modelValue:ae.value.status,"onUpdate:modelValue":l[2]||(l[2]=e=>ae.value.status=e),style:{width:"200px"}},{default:m((()=>[_(s,{label:1},{default:m((()=>l[24]||(l[24]=[y("启用")]))),_:1,__:[24]}),_(s,{label:0},{default:m((()=>l[25]||(l[25]=[y("禁用")]))),_:1,__:[25]})])),_:1},8,["modelValue"])])),_:1}),_(n,{label:"markdown"},{default:m((()=>[_(i,{modelValue:ae.value.in_markdown_section,"onUpdate:modelValue":l[3]||(l[3]=e=>ae.value.in_markdown_section=e),style:{width:"200px"}},{default:m((()=>[_(s,{label:1},{default:m((()=>l[26]||(l[26]=[y("是")]))),_:1,__:[26]}),_(s,{label:0},{default:m((()=>l[27]||(l[27]=[y("否")]))),_:1,__:[27]})])),_:1},8,["modelValue"])])),_:1}),_(n,{label:"试题来源"},{default:m((()=>[_(u,{modelValue:ae.value.jobbank_source_id,"onUpdate:modelValue":l[4]||(l[4]=e=>ae.value.jobbank_source_id=e),clearable:"",filterable:"",style:{width:"200px"},placeholder:"试题来源"},{default:m((()=>[(c(!0),v(b,null,x(te.value,(e=>(c(),w(o,{key:e.id,label:e.title,value:e.id,style:{width:"260px"}},{default:m((()=>[g("span",Q,h(e.id),1),g("span",N,h(e.title),1)])),_:2},1032,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),_(n,{label:"试题能力层次"},{default:m((()=>[_(u,{modelValue:ae.value.competence_level,"onUpdate:modelValue":l[5]||(l[5]=e=>ae.value.competence_level=e),clearable:"",filterable:"",style:{width:"200px"},placeholder:"试题能力层次"},{default:m((()=>[_(o,{label:"识记",value:"识记",style:{width:"200px"}}),_(o,{label:"领会",value:"领会",style:{width:"200px"}}),_(o,{label:"应用",value:"应用",style:{width:"200px"}})])),_:1},8,["modelValue"])])),_:1}),_(n,{label:"难度能力层次"},{default:m((()=>[_(u,{modelValue:ae.value.difficulty_level,"onUpdate:modelValue":l[6]||(l[6]=e=>ae.value.difficulty_level=e),clearable:"",filterable:"",style:{width:"200px"},placeholder:"难度能力层次"},{default:m((()=>[_(o,{label:"易",value:"易",style:{width:"200px"}}),_(o,{label:"较易",value:"较易",style:{width:"200px"}}),_(o,{label:"中等",value:"中等",style:{width:"200px"}}),_(o,{label:"较难",value:"较难",style:{width:"200px"}}),_(o,{label:"难",value:"难",style:{width:"200px"}})])),_:1},8,["modelValue"])])),_:1}),_(n,{label:"章节号"},{default:m((()=>[_(d,{modelValue:ae.value.chapter_no,"onUpdate:modelValue":l[7]||(l[7]=e=>ae.value.chapter_no=e),min:0,placeholder:"章节号",style:{width:"200px"}},null,8,["modelValue"])])),_:1}),_(n,{label:"章节名称"},{default:m((()=>[_(t,{modelValue:ae.value.chapter_mc,"onUpdate:modelValue":l[8]||(l[8]=e=>ae.value.chapter_mc=e),placeholder:"章节名称",style:{width:"520px"}},null,8,["modelValue"])])),_:1}),_(n,{label:"题序号"},{default:m((()=>[_(d,{modelValue:ae.value.sn,"onUpdate:modelValue":l[9]||(l[9]=e=>ae.value.sn=e),min:0,placeholder:"题干序号",style:{width:"200px"},onFocus:l[10]||(l[10]=e=>e.target.select())},null,8,["modelValue"])])),_:1}),_(n,{label:"版本（套题）"},{default:m((()=>[_(t,{modelValue:ae.value.ver,"onUpdate:modelValue":l[11]||(l[11]=e=>ae.value.ver=e),placeholder:"章节名称",style:{width:"200px"}},null,8,["modelValue"])])),_:1}),_(n,{label:"主题干"},{default:m((()=>[g("div",S,[_(k,{modelValue:ae.value.title.title,"onUpdate:modelValue":l[12]||(l[12]=e=>ae.value.title.title=e),height:"400px"},null,8,["modelValue"])])])),_:1}),"subQuestions"in ae.value.title?f("",!0):(c(),w(n,{key:0,label:"主题答案"},{default:m((()=>[g("div",z,[_(k,{modelValue:ae.value.answer,"onUpdate:modelValue":l[13]||(l[13]=e=>ae.value.answer=e),height:"300px"},null,8,["modelValue"])])])),_:1})),_(n,{label:"主题答案解析"},{default:m((()=>[g("div",B,[_(k,{modelValue:ae.value.answer_parsing,"onUpdate:modelValue":l[14]||(l[14]=e=>ae.value.answer_parsing=e),height:"300px"},null,8,["modelValue"])])])),_:1}),_(n,{label:"知识点"},{default:m((()=>[_(t,{modelValue:ae.value.knowledge_point,"onUpdate:modelValue":l[15]||(l[15]=e=>ae.value.knowledge_point=e),type:"textarea",autosize:{minRows:4,maxRows:6},style:{width:"450px"}},null,8,["modelValue"])])),_:1})])),_:1},8,["model"]),"options"in ae.value.title?(c(),v("div",T,[_(U,{type:"primary",plain:"",size:"default",round:"",icon:"CirclePlus",onClick:oe},{default:m((()=>l[28]||(l[28]=[y(" 新增选项")]))),_:1,__:[28]}),V((c(),w(me,{ref:"questionTable",data:ae.value.title.options,border:"",style:{width:"100%",margin:"10px"}},{default:m((()=>[_(ve,{prop:"listNo",label:"选项名",align:"center","header-align":"center",width:"60"},{default:m((e=>[_(t,{modelValue:e.row.listNo,"onUpdate:modelValue":l=>e.row.listNo=l,placeholder:"请输入选项名"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),_(ve,{prop:"answer",label:"是否为答案",align:"center","header-align":"center",width:"80"},{default:m((e=>[_(_e,{modelValue:e.row.answer,"onUpdate:modelValue":l=>e.row.answer=l,onChange:l=>ue(e.$index,e.row)},null,8,["modelValue","onUpdate:modelValue","onChange"])])),_:1}),_(ve,{prop:"option",label:"选项内容","min-width":"250"},{default:m((e=>[g("div",R,[_(k,{modelValue:e.row.option,"onUpdate:modelValue":l=>e.row.option=l,height:"200px"},null,8,["modelValue","onUpdate:modelValue"])])])),_:1}),_(ve,{label:"操作",align:"center","header-align":"center",width:"100"},{default:m((e=>[_(U,{type:"danger",size:"small",onClick:l=>{return a=e.$index,void(ae.value&&ae.value.title&&Array.isArray(ae.value.title.options)&&(ae.value.title.options.splice(a,1),ue()));var a}},{default:m((()=>l[29]||(l[29]=[y("删除")]))),_:2,__:[29]},1032,["onClick"])])),_:1})])),_:1},8,["data"])),[[fe,se.value]]),g("div",M,[g("div",$,[_(a,{class:"el_tag_5"},{default:m((()=>l[30]||(l[30]=[y("选项答案解析")]))),_:1,__:[30]}),g("div",E,[_(k,{modelValue:ae.value.title.answer_parsing,"onUpdate:modelValue":l[16]||(l[16]=e=>ae.value.title.answer_parsing=e),height:"300px"},null,8,["modelValue"])])]),g("div",I,[_(a,{class:"el_tag_5"},{default:m((()=>l[31]||(l[31]=[y("选项答案(A,B)")]))),_:1,__:[31]}),"options"in ae.value.title&&ae.value.title.options.length>0?(c(),w(t,{key:0,modelValue:ae.value.title.answer,"onUpdate:modelValue":l[17]||(l[17]=e=>ae.value.title.answer=e),placeholder:"选项答案",type:"textarea",autosize:{minRows:2,maxRows:4},style:{width:"360px",margin:"0 10px"}},null,8,["modelValue"])):f("",!0)])])])):f("",!0),"subQuestions"in ae.value.title?(c(),v("div",P,[_(U,{type:"primary",plain:"",size:"default",round:"",icon:"CirclePlus",onClick:de},{default:m((()=>l[32]||(l[32]=[y(" 新增子题")]))),_:1,__:[32]}),_(he,{modelValue:ne.value,"onUpdate:modelValue":l[18]||(l[18]=e=>ne.value=e),style:{margin:"10px"}},{default:m((()=>[(c(!0),v(b,null,x(ae.value.title.subQuestions,((e,s)=>(c(),w(ge,{title:"第"+(s+1)+"题",key:s,name:s+"题号"},{default:m((()=>[_(U,{type:"danger",plain:"",size:"default",round:"",icon:"Delete",onClick:e=>{return l=s,void(ae.value&&ae.value.title&&Array.isArray(ae.value.title.subQuestions)&&(ae.value.title.subQuestions.splice(l,1),ne.value=ne.value.filter((e=>parseInt(e.replace("题号",""))!==l)),ne.value=ne.value.map((e=>{const a=parseInt(e.replace("题号",""));return a>l?a-1+"题号":e})),pe()));var l}},{default:m((()=>l[33]||(l[33]=[y(" 删除本子题")]))),_:2,__:[33]},1032,["onClick"]),g("div",D,[_(k,{modelValue:e.title,"onUpdate:modelValue":l=>e.title=l,height:"300px"},null,8,["modelValue","onUpdate:modelValue"])]),"options"in e?(c(),v("div",L,[_(U,{type:"primary",plain:"",size:"default",round:"",icon:"CirclePlus",onClick:e=>function(e){if(!ae.value||!ae.value.title||!Array.isArray(ae.value.title.subQuestions))return;const l=ae.value.title.subQuestions[e];if(!l)return;Array.isArray(l.options)||(l.options=[]);const a=l.options.length;let t="";t=a<26?String.fromCharCode(65+a):String.fromCharCode(65+Math.floor(a/26)-1)+String.fromCharCode(65+a%26);l.options.push({listNo:t,answer:!1,option:""})}(s)},{default:m((()=>l[34]||(l[34]=[y(" 新增选项")]))),_:2,__:[34]},1032,["onClick"]),V((c(),w(me,{data:e.options,border:"",style:{width:"100%",margin:"10px"}},{default:m((()=>[_(ve,{prop:"listNo",label:"选项名",align:"center","header-align":"center",width:"80"},{default:m((e=>[_(t,{modelValue:e.row.listNo,"onUpdate:modelValue":l=>e.row.listNo=l,placeholder:"请输入选项名"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),_(ve,{prop:"answer",label:"是否为答案",align:"center","header-align":"center",width:"80"},{default:m((e=>[_(_e,{modelValue:e.row.answer,"onUpdate:modelValue":l=>e.row.answer=l,onChange:l=>re(s,e.$index,e.row)},null,8,["modelValue","onUpdate:modelValue","onChange"])])),_:2},1024),_(ve,{prop:"option",label:"选项内容",align:"center","header-align":"center","min-width":"250"},{default:m((e=>[g("div",F,[_(k,{modelValue:e.row.option,"onUpdate:modelValue":l=>e.row.option=l,height:"200px"},null,8,["modelValue","onUpdate:modelValue"])])])),_:1}),_(ve,{label:"操作",align:"center","header-align":"center",width:"100"},{default:m((e=>[_(U,{type:"danger",size:"small",onClick:l=>function(e,l){if(!ae.value||!ae.value.title||!Array.isArray(ae.value.title.subQuestions))return;const a=ae.value.title.subQuestions[e];a&&Array.isArray(a.options)&&(a.options.splice(l,1),re(e))}(s,e.$index)},{default:m((()=>l[35]||(l[35]=[y("删除")]))),_:2,__:[35]},1032,["onClick"])])),_:2},1024)])),_:2},1032,["data"])),[[fe,se.value]]),g("div",J,[_(a,{class:"el_tag_5"},{default:m((()=>l[36]||(l[36]=[y("选项答案解析")]))),_:1,__:[36]}),g("div",G,[_(k,{modelValue:e.answer_parsing,"onUpdate:modelValue":l=>e.answer_parsing=l,height:"200px"},null,8,["modelValue","onUpdate:modelValue"])])]),g("div",K,[_(a,{class:"el_tag_5"},{default:m((()=>l[37]||(l[37]=[y("选项答案(A,B,C,D)")]))),_:1,__:[37]}),e.options.length>0?(c(),w(t,{key:0,modelValue:e.answer,"onUpdate:modelValue":l=>e.answer=l,placeholder:"选项答案",type:"textarea",autosize:{minRows:2,maxRows:4},style:{width:"450px",margin:"0 10px"}},null,8,["modelValue","onUpdate:modelValue"])):f("",!0)])])):(c(),v("div",O,[g("div",Y,[_(a,{class:"el_tag_5"},{default:m((()=>l[38]||(l[38]=[y("答案")]))),_:1,__:[38]}),g("div",H,[_(k,{modelValue:e.answer,"onUpdate:modelValue":l=>e.answer=l,height:"300px"},null,8,["modelValue","onUpdate:modelValue"])])]),g("div",W,[_(a,{class:"el_tag_5"},{default:m((()=>l[39]||(l[39]=[y("答案解析")]))),_:1,__:[39]}),g("div",X,[_(k,{modelValue:e.answer_parsing,"onUpdate:modelValue":l=>e.answer_parsing=l,height:"200px"},null,8,["modelValue","onUpdate:modelValue"])])])]))])),_:2},1032,["title","name"])))),128))])),_:1},8,["modelValue"]),g("div",Z,[_(a,{class:"el_tag_5",style:{margin:"10px"}},{default:m((()=>l[40]||(l[40]=[y(' 组合题选项答案([{"sn":1,"answer":"B,D"},{"sn":2,"answer":"B"},{"sn":3,"answer":"C"},{"sn":4,"answer":"B"},{"sn":5,"answer":"B"}]) ')]))),_:1,__:[40]}),l[41]||(l[41]=g("br",null,null,-1)),ae.value.title.subQuestions.length>0?(c(),w(t,{key:0,modelValue:ae.value.answer,"onUpdate:modelValue":l[19]||(l[19]=e=>ae.value.answer=e),disabled:"",placeholder:"选项答案解析",type:"textarea",autosize:{minRows:2,maxRows:4},style:{width:"500px",margin:"0 10px"}},null,8,["modelValue"])):f("",!0)]),g("div",ee,[_(a,{class:"el_tag_5",style:{margin:"10px"}},{default:m((()=>l[42]||(l[42]=[y(" 组合题选项答案解析 ")]))),_:1,__:[42]}),l[43]||(l[43]=g("br",null,null,-1)),ae.value.title.subQuestions.length>0?(c(),w(t,{key:0,modelValue:ae.value.answer_parsing,"onUpdate:modelValue":l[20]||(l[20]=e=>ae.value.answer_parsing=e),placeholder:"选项答案解析",type:"textarea",autosize:{minRows:2,maxRows:4},style:{width:"500px",margin:"0 10px"}},null,8,["modelValue"])):f("",!0)])])):f("",!0)]),g("div",le,[_(U,{type:"primary",onClick:ce,loading:ie.value},{default:m((()=>l[44]||(l[44]=[y("保存题库")]))),_:1,__:[44]},8,["loading"])])])),_:1})])}}}),te=["v-loading","element-loading-text","element-loading-spinner"],se={class:"preview-question-bank"},ie={key:0,class:"main-content"},ne=["data-question-id"],oe={class:"question-header"},ue={class:"question-jobid"},de={class:"question-type"},re={class:"question-jobid"},pe={class:"question-jobid"},ve={class:"question-header"},ce={class:"el_tag_5"},_e={class:"jobbank-title"},me={key:0,class:"question-header"},ge={class:"el_tag_5"},he={class:"jobbank-title"},ye={key:1},fe={class:"question-header"},we={class:"el_tag_5"},be={class:"jobbank-title"},xe={key:0},Ve={class:"question-options"},ke={key:0},qe={class:"opt_options"},Ue={class:"option-label"},Ce={key:1},Ae={class:"opt_options"},je={class:"option-label"},Qe={key:2},Ne={class:"opt_options"},Se={class:"option-label"},ze={class:"question-header"},Be={class:"el_tag_5"},Te={class:"question-title"},Re={class:"question-header"},Me={class:"el_tag_5"},$e={class:"question-title"},Ee={key:1},Ie={class:"question-header"},Pe={class:"el_tag_5"},De={class:"question-title"},Le={key:0},Fe={key:1},Je={key:2,style:{color:"red"}},Ge={class:"question-header"},Ke={class:"el_tag_5"},Oe={class:"question-title"},Ye={key:2},He={class:"question-header"},We={class:"el_tag_5"},Xe={class:"question-title"},Ze={class:"question-header"},el={class:"el_tag_5"},ll={class:"question-title"},al={key:2},tl={class:"question-options"},sl={key:0},il={class:"opt_options"},nl={class:"option-label"},ol={key:1},ul={class:"opt_options"},dl={class:"option-label"},rl={key:2},pl={class:"opt_options"},vl={class:"option-label"},cl={class:"question-header"},_l={class:"el_tag_5"},ml={class:"question-title"},gl={class:"question-header"},hl={class:"el_tag_5"},yl={class:"question-title"},fl={key:3},wl={class:"question-header"},bl={class:"el_tag_5"},xl={class:"question-title"},Vl={key:0},kl={key:1},ql={key:2,style:{color:"red"}},Ul={class:"question-header"},Cl={class:"el_tag_5"},Al={class:"question-title"},jl={key:4},Ql={class:"question-header"},Nl={class:"el_tag_5"},Sl={class:"question-title"},zl={class:"question-header"},Bl={class:"el_tag_5"},Tl={class:"question-title"},Rl={class:"question-header"},Ml={class:"question-header"},$l={class:"question-header"},El={class:"question-header"},Il={class:"question-header"},Pl={key:1,class:"main-content"},Dl={key:0},Ll={key:1},Fl={class:"side-info"},Jl={class:"info-item"},Gl={key:0,class:"info-item"},Kl={key:1},Ol={class:"question-types-card"},Yl={class:"type-list"},Hl={class:"type-header"},Wl={class:"type-info"},Xl={class:"type-name"},Zl={class:"type-count"},ea={class:"sub-questions"},la={class:"question-numbers-div"},aa=["onClick"],ta=["element-loading-text","element-loading-spinner"],sa=i({__name:"preview_question_bank",props:{coursebaseid:{},jobbanksourceid:{},jobbankver:{},jobbankid:{}},setup(e){const l=e,a=o(l.coursebaseid),s=o(l.jobbanksourceid),i=o(l.jobbankver),n=o(l.jobbankid),q=o([]),A=o([]);o([]);const j=o(!1),Q=o(!1),N=o("题库渲染中，请稍后..."),S=U({dialogShow:!1,title:"",loading:!1,loadingText:"获取数据中，请稍后...",loadingSvg:'\n        <path class="path" d="\n          M 30 15\n          L 28 17\n          M 25.61 25.61\n          A 15 15, 0, 0, 1, 15 30\n          A 15 15, 0, 1, 1, 27.99 7.5\n          L 15 15\n        " style="stroke-width: 4px; fill: rgba(0, 0, 0, 0)"/>\n      ',jobbankid:0}),z=o([]),B=async()=>{j.value=!0,Q.value=!0;try{const e={course_base_id:a.value,jobbank_source_id:s.value,jobbank_ver:i.value,jobbank_id:n.value};t(e).then((e=>{q.value=e.data||[],A.value=e,q.value.length>0?((()=>{const e={};q.value.forEach((l=>{const a=l.questiontype;e[a]||(e[a]=[]),e[a].push({id:l.id})})),z.value=e})(),setTimeout((()=>{Q.value=!1,j.value=!1}),2e3)):(Q.value=!1,j.value=!1)})).catch((e=>{j.value=!1}))}catch(e){k.error("题库获取失败")}finally{j.value=!1}};u((()=>{B()})),d((()=>l.coursebaseid),(e=>{a.value=e,B()}));const T=C((()=>{const e={};let l=1;for(const a in z.value)e[a]=l,l+=z.value[a].length;return e}));return(e,l)=>{const a=r("el-button"),t=r("el-tag"),s=r("v-md-preview"),i=r("el-radio"),n=r("el-radio-group"),o=r("el-checkbox"),u=r("el-checkbox-group"),d=r("el-dialog"),k=p("loading");return V((c(),v("div",{class:"preview_bank_global","v-loading":j.value,"element-loading-text":N.value,"element-loading-spinner":S.loadingSvg,"element-loading-svg-view-box":"-10, -10, 50, 50","element-loading-background":"rgba(122, 122, 122, 0.8)"},[g("div",se,[q.value.length>0?(c(),v("div",ie,[(c(!0),v(b,null,x(q.value,((e,d)=>(c(),v("div",{key:e.id,class:"question-group"},[g("div",{class:"question-item","data-question-id":e.id},[g("div",oe,[g("span",ue,h(d+1)+"、",1),g("span",de,"【"+h(e.questiontype)+"】 ",1),g("span",re,"题编号： ( "+h(e.id)+" ) ",1),g("span",pe,"题序号： "+h(e.sn),1),_(a,{type:"success",icon:"Edit",size:"small",circle:"",onClick:l=>{return a=e,S.dialogShow=!0,void(S.jobbankid=a.id);var a}},null,8,["onClick"])]),g("div",ve,[g("div",ce,[_(t,{class:"el_tag_5"},{default:m((()=>l[1]||(l[1]=[y("题干")]))),_:1,__:[1]})]),g("div",_e,[_(s,{text:e.title.title},null,8,["text"])])]),""!=e.score_explain&&null===e.score_explain&&void 0===e.score_explain?(c(),v("div",me,[g("div",ge,[_(t,{class:"el_tag_5"},{default:m((()=>l[2]||(l[2]=[y("得分说明")]))),_:1,__:[2]})]),g("div",he,[_(s,{text:e.score_explain},null,8,["text"])])])):f("",!0),"subQuestions"in e.title?(c(),v("div",ye,[(c(!0),v(b,null,x(e.title.subQuestions,((e,a)=>(c(),v("div",{key:a},[g("div",fe,[g("div",we,[_(t,{class:"el_tag_5"},{default:m((()=>l[3]||(l[3]=[y("小题干")]))),_:1,__:[3]})]),g("div",be,[_(s,{text:e.title},null,8,["text"])])]),"options"in e?(c(),v("div",xe,[g("div",Ve,[e.questionType.includes("单")?(c(),v("div",ke,[_(n,{modelValue:e.answer,"onUpdate:modelValue":l=>e.answer=l,disabled:""},{default:m((()=>[(c(!0),v(b,null,x(e.options,((e,l)=>(c(),w(i,{value:e.listNo,size:"large",key:l},{default:m((()=>[g("div",qe,[g("span",Ue,h(e.listNo),1),_(s,{text:e.option},null,8,["text"])])])),_:2},1032,["value"])))),128))])),_:2},1032,["modelValue","onUpdate:modelValue"])])):e.questionType.includes("多")?(c(),v("div",Ce,[_(u,{"model-value":e.answer&&e.answer.includes(",")?e.answer.split(","):e.answer?e.answer.split(""):[],disabled:""},{default:m((()=>[(c(!0),v(b,null,x(e.options,((e,l)=>(c(),w(o,{value:e.listNo,size:"large",key:l},{default:m((()=>[g("div",Ae,[g("span",je,h(e.listNo),1),_(s,{text:e.option},null,8,["text"])])])),_:2},1032,["value"])))),128))])),_:2},1032,["model-value"])])):(c(),v("div",Qe,[_(u,{"model-value":e.answer&&e.answer.includes(",")?e.answer.split(","):e.answer?e.answer.split(""):[],disabled:""},{default:m((()=>[(c(!0),v(b,null,x(e.options,((e,l)=>(c(),w(o,{value:e.listNo,size:"large",key:l},{default:m((()=>[g("div",Ne,[g("span",Se,h(e.listNo),1),_(s,{text:e.option},null,8,["text"])])])),_:2},1032,["value"])))),128))])),_:2},1032,["model-value"])]))]),g("div",ze,[g("div",Be,[_(t,{class:"el_tag_5"},{default:m((()=>l[4]||(l[4]=[y("选项答案")]))),_:1,__:[4]})]),g("div",Te,[_(s,{text:e.answer},null,8,["text"])])]),g("div",Re,[g("div",Me,[_(t,{class:"el_tag_5"},{default:m((()=>l[5]||(l[5]=[y("答案解析")]))),_:1,__:[5]})]),g("div",$e,[_(s,{text:e.answer_parsing},null,8,["text"])])])])):e.questionType.includes("判")&&(e.answer.includes("0")||e.answer.includes("1"))?(c(),v("div",Ee,[_(n,{modelValue:e.answer,"onUpdate:modelValue":l=>e.answer=l,disabled:""},{default:m((()=>[_(i,{value:"1"},{default:m((()=>l[6]||(l[6]=[y("A、正确")]))),_:1,__:[6]}),_(i,{value:"0"},{default:m((()=>l[7]||(l[7]=[y("B、错误")]))),_:1,__:[7]})])),_:2},1032,["modelValue","onUpdate:modelValue"]),g("div",Ie,[g("div",Pe,[_(t,{class:"el_tag_5"},{default:m((()=>l[8]||(l[8]=[y("选项答案")]))),_:1,__:[8]})]),g("div",De,[e.answer.includes("0")?(c(),v("span",Le,"错误")):f("",!0),e.answer.includes("1")?(c(),v("span",Fe,"正确")):(c(),v("span",Je,h(e.answer),1))])]),g("div",Ge,[g("div",Ke,[_(t,{class:"el_tag_5"},{default:m((()=>l[9]||(l[9]=[y("答案解析")]))),_:1,__:[9]})]),g("div",Oe,[_(s,{text:e.answer_parsing},null,8,["text"])])])])):(c(),v("div",Ye,[g("div",He,[g("div",We,[_(t,{class:"el_tag_5"},{default:m((()=>l[10]||(l[10]=[y("答案")]))),_:1,__:[10]})]),g("div",Xe,[_(s,{text:e.answer},null,8,["text"])])]),g("div",Ze,[g("div",el,[_(t,{class:"el_tag_5"},{default:m((()=>l[11]||(l[11]=[y("答案解析")]))),_:1,__:[11]})]),g("div",ll,[_(s,{text:e.answer_parsing},null,8,["text"])])])]))])))),128))])):"options"in e.title?(c(),v("div",al,[g("div",tl,[e.title.questionType.includes("单")?(c(),v("div",sl,[_(n,{modelValue:e.title.answer,"onUpdate:modelValue":l=>e.title.answer=l,disabled:""},{default:m((()=>[(c(!0),v(b,null,x(e.title.options,((e,l)=>(c(),w(i,{value:e.listNo,size:"large",key:l},{default:m((()=>[g("div",il,[g("span",nl,h(e.listNo),1),_(s,{text:e.option},null,8,["text"])])])),_:2},1032,["value"])))),128))])),_:2},1032,["modelValue","onUpdate:modelValue"])])):e.title.questionType.includes("多")?(c(),v("div",ol,[_(u,{"model-value":e.title.answer?e.title.answer.split(","):[],disabled:""},{default:m((()=>[(c(!0),v(b,null,x(e.title.options,((e,l)=>(c(),w(o,{value:e.listNo,size:"large",key:l},{default:m((()=>[g("div",ul,[g("span",dl,h(e.listNo),1),_(s,{text:e.option},null,8,["text"])])])),_:2},1032,["value"])))),128))])),_:2},1032,["model-value"])])):(c(),v("div",rl,[_(u,{"model-value":e.title.answer?e.title.answer.split(","):[],disabled:""},{default:m((()=>[(c(!0),v(b,null,x(e.title.options,((e,l)=>(c(),w(o,{value:e.listNo,size:"large",key:l},{default:m((()=>[g("div",pl,[g("span",vl,h(e.listNo),1),_(s,{text:e.option},null,8,["text"])])])),_:2},1032,["value"])))),128))])),_:2},1032,["model-value"])]))]),g("div",cl,[g("div",_l,[_(t,{class:"el_tag_5"},{default:m((()=>l[12]||(l[12]=[y("选项答案")]))),_:1,__:[12]})]),g("div",ml,[_(s,{text:e.title.answer},null,8,["text"])])]),g("div",gl,[g("div",hl,[_(t,{class:"el_tag_5"},{default:m((()=>l[13]||(l[13]=[y("答案解析")]))),_:1,__:[13]})]),g("div",yl,[_(s,{text:e.title.answer_parsing},null,8,["text"])])])])):e.title.questionType.includes("判")&&(e.title.answer.includes("0")||e.title.answer.includes("1"))?(c(),v("div",fl,[_(n,{modelValue:e.title.answer,"onUpdate:modelValue":l=>e.title.answer=l,disabled:""},{default:m((()=>[_(i,{value:"1"},{default:m((()=>l[14]||(l[14]=[y("A、正确")]))),_:1,__:[14]}),_(i,{value:"0"},{default:m((()=>l[15]||(l[15]=[y("B、错误")]))),_:1,__:[15]})])),_:2},1032,["modelValue","onUpdate:modelValue"]),g("div",wl,[g("div",bl,[_(t,{class:"el_tag_5"},{default:m((()=>l[16]||(l[16]=[y("选项答案")]))),_:1,__:[16]})]),g("div",xl,[e.answer.includes("0")?(c(),v("span",Vl,"错误")):f("",!0),e.answer.includes("1")?(c(),v("span",kl,"正确")):(c(),v("span",ql,h(e.answer),1))])]),g("div",Ul,[g("div",Cl,[_(t,{class:"el_tag_5"},{default:m((()=>l[17]||(l[17]=[y("答案解析")]))),_:1,__:[17]})]),g("div",Al,[_(s,{text:e.answer_parsing},null,8,["text"])])])])):(c(),v("div",jl,[g("div",Ql,[g("div",Nl,[_(t,{class:"el_tag_5"},{default:m((()=>l[18]||(l[18]=[y("答案")]))),_:1,__:[18]})]),g("div",Sl,[_(s,{text:e.answer},null,8,["text"])])]),g("div",zl,[g("div",Bl,[_(t,{class:"el_tag_5"},{default:m((()=>l[19]||(l[19]=[y("答案解析")]))),_:1,__:[19]})]),g("div",Tl,[_(s,{text:e.answer_parsing},null,8,["text"])])])])),g("div",Rl,[_(t,{class:"el_tag_5"},{default:m((()=>l[20]||(l[20]=[y("知识点")]))),_:1,__:[20]}),g("span",null,h(e.knowledge_point),1)]),g("div",Ml,[_(t,{class:"el_tag_5"},{default:m((()=>l[21]||(l[21]=[y("试题来源")]))),_:1,__:[21]}),g("span",null,h(e.jobbank_source_title),1)]),g("div",$l,[_(t,{class:"el_tag_5"},{default:m((()=>l[22]||(l[22]=[y("试题能力层次")]))),_:1,__:[22]}),g("span",null,h(e.competence_level),1)]),g("div",El,[_(t,{class:"el_tag_5"},{default:m((()=>l[23]||(l[23]=[y("难度能力层次")]))),_:1,__:[23]}),g("span",null,h(e.difficulty_level),1)]),g("div",Il,[_(t,{class:"el_tag_5"},{default:m((()=>l[24]||(l[24]=[y("版本(套题)")]))),_:1,__:[24]}),g("span",null,h(e.ver),1)])],8,ne)])))),128))])):(c(),v("div",Pl,[200==A.value.code?(c(),v("span",Dl,"所选套题，无题库信息！！！请确认")):(c(),v("span",Ll,h(A.value.msg),1))])),g("div",Fl,[g("div",Jl,[_(a,{type:"success",plain:"",size:"default",icon:"refresh",round:"",onClick:B},{default:m((()=>l[25]||(l[25]=[y("刷新")]))),_:1,__:[25]})]),q.value.length>0?(c(),v("div",Gl,[g("span",null,"（ "+h(q.value[0].kc_bm)+" ） "+h(q.value[0].kc_mc)+" (共"+h(q.value.length)+"题)",1)])):f("",!0),q.value.length>0?(c(),v("div",Kl,[g("div",Ol,[l[26]||(l[26]=g("div",{class:"card-title"},"试题选择卡",-1)),g("div",Yl,[(c(!0),v(b,null,x(z.value,((e,l)=>(c(),v("div",{key:l,class:"type-item"},[g("div",Hl,[g("div",Wl,[g("span",Xl,h(l),1),g("span",Zl,"(共"+h(e.length)+"题)",1)])]),g("div",ea,[g("div",la,[(c(!0),v(b,null,x(e,((e,a)=>(c(),v("div",{key:e.id,class:"question-number-indx",onClick:l=>(e=>{const l=document.querySelector(`[data-question-id="${e}"]`);l&&l.scrollIntoView({behavior:"smooth"})})(e.id)},h(((e,l)=>T.value[e]+l)(l,a)),9,aa)))),128))])])])))),128))])])])):f("",!0)])]),S.dialogShow?(c(),w(d,{key:0,modelValue:S.dialogShow,"onUpdate:modelValue":l[0]||(l[0]=e=>S.dialogShow=e),"align-center":"",draggable:"","show-close":!0,fullscreen:!0,"close-on-click-modal":!1,"close-on-press-escape":!1},{default:m((()=>[V((c(),v("div",{class:"dialog_content","element-loading-text":S.loadingText,"element-loading-spinner":S.loadingSvg,"element-loading-svg-view-box":"-10, -10, 50, 50","element-loading-background":"rgba(122, 122, 122, 0.8)"},[_(ae,{jobbankid:S.jobbankid},null,8,["jobbankid"])],8,ta)),[[k,S.loading]])])),_:1},8,["modelValue"])):f("",!0)],8,te)),[[k,Q.value]])}}});export{sa as _,ae as a};
