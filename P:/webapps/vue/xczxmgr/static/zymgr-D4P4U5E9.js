import{b as e,z as l,A as a}from"./exam-jF1-Sa8S.js";import{d as t,r as o,o as d,E as n,a as u,c as s,b as r,e as i,j as p,w as c,p as _,t as m,h as y,v}from"./index-BERmxY3Y.js";const b={class:"examplan",style:{padding:"5px"}},f={key:0},g={key:1},h={style:{height:"auto"}},z={style:{padding:"10px 20px"}},k={class:"dialog-footer"},w=t({__name:"zymgr",setup(t){const w=o([]);o({});const V=o(),x=o(window.innerHeight-200);d((()=>{j()}));const C=o({id:"",zy_bm:"",zy_mc:"",learn_type:"",status:"0"});o("");const U=o(!1),j=()=>{e({}).then((e=>{200==e.code?w.value=e.data:n.error(e.msg)}))},H=e=>{V.value=e};return(e,t)=>{const o=u("el-button"),d=u("el-table-column"),S=u("EditPen"),A=u("el-icon"),B=u("el-link"),D=u("el-tag"),E=u("el-table"),R=u("el-option"),T=u("el-select"),F=u("el-form-item"),K=u("el-input"),P=u("el-radio"),$=u("el-radio-group"),q=u("el-form"),G=u("el-tab-pane"),I=u("el-tabs"),J=u("el-dialog");return r(),s("div",b,[i("div",null,[p(o,{type:"primary",onClick:j,icon:"RefreshRight"}),p(o,{type:"primary",onClick:t[0]||(t[0]=e=>(C.value={id:"",zy_bm:"",zy_mc:"",status:"0"},void(U.value=!0))),plain:"",icon:"DocumentAdd"},{default:c((()=>t[9]||(t[9]=[_("添加")]))),_:1,__:[9]}),p(o,{type:"danger",onClick:t[1]||(t[1]=e=>{V.value?v.confirm("此操作将永久删除该数据, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{a(V.value.id).then((e=>{200==e.code?(n.success(e.msg),j()):n.error(e.msg)}))})).catch((()=>{n({type:"info",message:"已取消删除"})})):n.warning("请先选择要删除的课程")}),plain:"",icon:"DeleteFilled"},{default:c((()=>t[10]||(t[10]=[_("删除")]))),_:1,__:[10]})]),p(E,{tableHeight:x.value,data:w.value,size:"mini",stripe:"",style:{width:"100%","margin-top":"10px"},"highlight-current-row":"",onCurrentChange:H},{default:c((()=>[p(d,{type:"index",width:"50",align:"center"}),p(d,{prop:"learn_type",label:"学习类型",align:"center"},{default:c((e=>[5==e.row.learn_type?(r(),s("span",f,"成人高考")):(r(),s("span",g,"自考"))])),_:1}),p(d,{prop:"zy_bm",label:"专业编码",width:"180",align:"center"}),p(d,{prop:"zy_mc",label:"专业名称",width:"180",align:"center"},{default:c((e=>[p(B,{onClick:l=>{return a=e.$index,C.value=w.value[a],C.value.status=C.value.status.toString(),C.value.learn_type=C.value.learn_type.toString(),void(U.value=!0);var a},type:"primary"},{default:c((()=>[_(m(e.row.zy_mc)+"  ",1),p(A,{style:{"font-size":"13px"}},{default:c((()=>[p(S)])),_:1})])),_:2},1032,["onClick"])])),_:1}),p(d,{prop:"status",label:"状态",align:"center"},{default:c((e=>[1==e.row.status?(r(),y(D,{key:0,type:"success"},{default:c((()=>t[11]||(t[11]=[_("正常")]))),_:1,__:[11]})):(r(),y(D,{key:1,type:"danger"},{default:c((()=>t[12]||(t[12]=[_("禁用")]))),_:1,__:[12]}))])),_:1}),p(d,{prop:"create_name",label:"创建人",align:"center"}),p(d,{prop:"create_date",label:"创建时间",align:"center"})])),_:1},8,["tableHeight","data"]),p(J,{modelValue:U.value,"onUpdate:modelValue":t[8]||(t[8]=e=>U.value=e),title:"添加专业信息",width:"600",top:"6vh","append-to-body":!0,"close-on-click-modal":!1,"close-on-press-escape":!1},{footer:c((()=>[i("span",k,[p(o,{onClick:t[6]||(t[6]=e=>U.value=!1)},{default:c((()=>t[15]||(t[15]=[_("取消")]))),_:1,__:[15]}),p(o,{type:"primary",onClick:t[7]||(t[7]=e=>(C.value.id=C.value.id?C.value.id:0,void l(C.value).then((e=>{"200"==e.code?(n.success(e.msg),U.value=!1,j()):n.error(e.msg)})))),disabled:!C.value.zy_bm||!C.value.zy_mc},{default:c((()=>t[16]||(t[16]=[_("保存 ")]))),_:1,__:[16]},8,["disabled"])])])),default:c((()=>[i("div",h,[p(I,{"tab-position":"top",style:{},class:"demo-tabs"},{default:c((()=>[p(G,{label:"基本设置"},{default:c((()=>[i("div",z,[p(q,{model:C.value,class:"demo-form-inline"},{default:c((()=>[p(F,{label:"学习类型"},{default:c((()=>[p(T,{modelValue:C.value.learn_type,"onUpdate:modelValue":t[2]||(t[2]=e=>C.value.learn_type=e),class:"m-2",style:{width:"170px"},placeholder:"Select"},{default:c((()=>[p(R,{key:"2",label:"自考",value:"2"}),p(R,{key:"1",label:"成人高考",value:"1"})])),_:1},8,["modelValue"])])),_:1}),p(F,{label:"专业编码"},{default:c((()=>[p(K,{modelValue:C.value.zy_bm,"onUpdate:modelValue":t[3]||(t[3]=e=>C.value.zy_bm=e),placeholder:"专业编码",clearable:""},null,8,["modelValue"])])),_:1}),p(F,{label:"专业名称"},{default:c((()=>[p(K,{modelValue:C.value.zy_mc,"onUpdate:modelValue":t[4]||(t[4]=e=>C.value.zy_mc=e),placeholder:"专业名称",clearable:""},null,8,["modelValue"])])),_:1}),p(F,{label:"教材状态"},{default:c((()=>[p($,{modelValue:C.value.status,"onUpdate:modelValue":t[5]||(t[5]=e=>C.value.status=e)},{default:c((()=>[p(P,{label:"1",border:""},{default:c((()=>t[13]||(t[13]=[_("启用")]))),_:1,__:[13]}),p(P,{label:"0",border:""},{default:c((()=>t[14]||(t[14]=[_("禁用")]))),_:1,__:[14]})])),_:1},8,["modelValue"])])),_:1})])),_:1},8,["model"])])])),_:1})])),_:1})])])),_:1},8,["modelValue"])])}}});export{w as default};
