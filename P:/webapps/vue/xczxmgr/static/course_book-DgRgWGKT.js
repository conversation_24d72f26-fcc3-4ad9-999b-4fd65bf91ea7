import{g as e,a as l,b as a,S as t,d as i}from"./kcmgr-_69jTmcm.js";import{C as o}from"./course-DUm3UmHR.js";import{d,r as u,o as n,a as s,c as r,b as c,e as p,j as _,w as v,p as m,t as f,h,m as g,F as b,f as y,E as x,v as k}from"./index-BERmxY3Y.js";const w={class:"course_book",style:{padding:"5px"}},q={style:{"text-align":"right",width:"100%"}},C={style:{height:"auto",display:"flex","justify-content":"center",width:"100%"}},V={style:{padding:"10px 10px"}},$={key:0,style:{color:"#909399"}},z={style:{width:"550px",border:"1px solid #fff","border-radius":"4px"}},E={style:{padding:"10px 0px",height:"300px","overflow-y":"auto"}},U={class:"dialog-footer"},j=d({__name:"course_book",setup(d){const j=u([]);u({});const F=u(),H=u(window.innerHeight-200),N=u(!1),S=u([]),J=u([]),O=u([]),B={multiple:!0},D=u({id:0,course_info_id:"",plate_id:"",question_source:[]});n((()=>{R()}));const R=()=>{e().then((e=>{j.value=e.data}))},T=()=>{o().then((e=>{var l=[];e.data.forEach((e=>{j.value&&j.value.findIndex((l=>l.course_info_id==e.id));var a={id:e.id,title:`【${e.kc_bm}】${e.kc_mc} 版本：${e.ver}`,disabled:!1};l.push(a)})),S.value=l}))},A=()=>{l({id:D.value.course_info_id,type:1}).then((e=>{J.value=e.data,J.value.forEach((e=>{j.value.filter((l=>l.course_info_id==D.value.course_info_id&&l.plate_id==e.id)).length>0&&(e.disabled=!0)}))}))},I=()=>{a({id:D.value.course_info_id}).then((e=>{var l=[];e.data&&(e.data.forEach((e=>{e.label=`${e.title}${e.zsd_key?"【"+e.zsd_key+"】":""}`,e.value=e.id,e.disabled=!1;var a=[];e.children.forEach((e=>{e.label=`${e.ver}【${e.ver_num}】`,e.value=e.ver,a.push(e)})),e.children=a,l.push(e)})),O.value=l)}))},P=e=>{N.value=!0,D.value=j.value[e],D.value.question_source=D.value.question_source?JSON.parse(D.value.question_source):[],T(),I(),A()},G=()=>{I(),A()},K=e=>{F.value=e},L=e=>{D.question_source=e};return(e,l)=>{const a=s("el-button"),o=s("el-table-column"),d=s("EditPen"),u=s("el-icon"),n=s("el-link"),M=s("el-tag"),Q=s("el-popover"),W=s("el-table"),X=s("el-option"),Y=s("el-select"),Z=s("el-form-item"),ee=s("el-radio"),le=s("el-radio-group"),ae=s("el-cascader-panel"),te=s("el-input"),ie=s("el-alert"),oe=s("el-form"),de=s("el-dialog");return c(),r("div",w,[p("div",null,[_(a,{type:"primary",onClick:R,icon:"RefreshRight"}),_(a,{type:"primary",onClick:l[0]||(l[0]=e=>(N.value=!0,D.value={id:0,course_info_id:"",plate_id:"",question_source:[]},I(),A(),void T())),plain:"",icon:"DocumentAdd"},{default:v((()=>l[9]||(l[9]=[m("添加")]))),_:1,__:[9]}),_(a,{type:"danger",onClick:l[1]||(l[1]=e=>{F.value?k.confirm("此操作将永久删除该数据, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{i({id:F.value.id}).then((e=>{200==e.code?(x.success(e.msg),R()):x.error(e.msg)}))})).catch((()=>{x({type:"info",message:"已取消删除"})})):x.warning("请先选择要删除的课程")}),plain:"",icon:"DeleteFilled"},{default:v((()=>l[10]||(l[10]=[m("删除")]))),_:1,__:[10]})]),_(W,{tableHeight:H.value,data:j.value,size:"mini",stripe:"",style:{width:"100%","margin-top":"10px"},"highlight-current-row":"",onCurrentChange:K},{default:v((()=>[_(o,{type:"index",width:"50",align:"center"}),_(o,{prop:"kc_bm",label:"课程编码",width:"180",align:"center"}),_(o,{prop:"kc_mc",label:"课程名称",width:"180",align:"center"},{default:v((e=>[_(n,{onClick:l=>P(e.$index),type:"primary"},{default:v((()=>[m(f(e.row.kc_mc)+"  ",1),_(u,{style:{"font-size":"13px"}},{default:v((()=>[_(d)])),_:1})])),_:2},1032,["onClick"])])),_:1}),_(o,{prop:"done_standard",label:"完成标准(分钟)",width:"180",align:"center"}),_(o,{prop:"ver",label:"在线课程版本",align:"center"}),_(o,{prop:"plate_name",label:"属于板块",align:"center"},{default:v((e=>[_(M,{type:"success"},{default:v((()=>[m(f(e.row.plate_name),1)])),_:2},1024)])),_:1}),_(o,{prop:"question_source",label:"教材信息",align:"center"},{default:v((e=>[e.row.question_source?(c(),h(Q,{key:0,placement:"right",width:400,trigger:"click"},{reference:v((()=>[_(a,{size:"small",type:"success"},{default:v((()=>[m(f(e.row.question_source_mc.length),1)])),_:2},1024)])),default:v((()=>[p("div",null,[p("div",q,[_(n,{onClick:l=>P(e.$index),type:"primary"},{default:v((()=>[_(u,{style:{"font-size":"13px"}},{default:v((()=>[_(d)])),_:1}),l[11]||(l[11]=m("编辑 "))])),_:2,__:[11]},1032,["onClick"])]),(c(!0),r(b,null,y(e.row.question_source_mc,((e,l)=>(c(),r("div",null,[(c(),r("div",{key:l,style:{margin:"5px 0px",width:"100%","text-align":"left",padding:"10px 5px","border-bottom":"1px solid #F2F6FC",display:"flex","align-items":"center"}},f(l+1)+"、 "+f(e.title),1))])))),256))])])),_:2},1024)):g("",!0)])),_:1}),_(o,{prop:"create_name",label:"创建人",align:"center"}),_(o,{prop:"create_date",label:"创建时间",align:"center"})])),_:1},8,["tableHeight","data"]),_(de,{modelValue:N.value,"onUpdate:modelValue":l[8]||(l[8]=e=>N.value=e),title:"添加课程",width:"720",top:"6vh","append-to-body":!0,"close-on-click-modal":!1,"close-on-press-escape":!1},{footer:v((()=>[p("span",U,[_(a,{onClick:l[6]||(l[6]=e=>N.value=!1)},{default:v((()=>l[14]||(l[14]=[m("取消")]))),_:1,__:[14]}),_(a,{type:"primary",onClick:l[7]||(l[7]=e=>(()=>{const e=D.value.question_source.map((([e])=>({course_dzs_id:e})));e.forEach((e=>{e.ver="00"})),D.value.question_source=JSON.stringify(D.value.question_source),D.value.question_source_b=JSON.stringify(e),t(D.value).then((e=>{200==e.code?(R(),N.value=!1,x.success(e.msg)):x.error(e.msg)}))})()),disabled:!D.value.course_info_id||0==D.value.question_source.length},{default:v((()=>l[15]||(l[15]=[m(" 确认 ")]))),_:1,__:[15]},8,["disabled"])])])),default:v((()=>[p("div",C,[p("div",V,[_(oe,{inline:!0,size:"large",model:D.value,class:"demo-form-inline",style:{width:"650px"}},{default:v((()=>[_(Z,{label:"选择课程"},{default:v((()=>[_(Y,{modelValue:D.value.course_info_id,"onUpdate:modelValue":l[2]||(l[2]=e=>D.value.course_info_id=e),class:"m-2",placeholder:"选择课程",style:{width:"270px","margin-bottom":"5px"},onChange:G},{default:v((()=>[(c(!0),r(b,null,y(S.value,(e=>(c(),h(X,{key:e.id,label:e.title,value:e.id,disabled:e.disabled},null,8,["label","value","disabled"])))),128))])),_:1},8,["modelValue"])])),_:1}),l[13]||(l[13]=p("br",null,null,-1)),_(Z,{label:"选择板块"},{default:v((()=>[""==D.value.course_info_id?(c(),r("span",$,"请先选择课程")):g("",!0),_(le,{modelValue:D.value.plate_id,"onUpdate:modelValue":l[3]||(l[3]=e=>D.value.plate_id=e)},{default:v((()=>[(c(!0),r(b,null,y(J.value,(e=>(c(),h(ee,{disabled:e.disabled,title:e.disabled?"【已添加】"+e.title:e.title,key:e.id,label:e.title,value:e.id,border:""},null,8,["disabled","title","label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),_(Z,{label:"教材来源"},{default:v((()=>[p("div",z,[p("div",E,[_(ae,{modelValue:D.value.question_source,"onUpdate:modelValue":l[4]||(l[4]=e=>D.value.question_source=e),props:B,style:{width:"100%",height:"100%"},onChange:L,options:O.value},null,8,["modelValue","options"])])])])),_:1}),_(Z,{label:"完成标准"},{default:v((()=>[_(te,{modelValue:D.value.done_standard,"onUpdate:modelValue":l[5]||(l[5]=e=>D.value.done_standard=e),type:"Number",placeholder:"请输入完成标准",style:{width:"150px"}},null,8,["modelValue"]),l[12]||(l[12]=m(" 分钟 ")),_(ie,{style:{"margin-top":"5px"},closable:!1,title:"根据情况设置学生学习完成标准，如果是练习题可输入最低完成题目数，如果是电子书可以设置最低浏览时长按分钟",type:"info"})])),_:1,__:[12]})])),_:1,__:[13]},8,["model"])])])])),_:1},8,["modelValue"])])}}});export{j as default};
