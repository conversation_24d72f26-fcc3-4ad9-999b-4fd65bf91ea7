import{I as a,J as t}from"./index-BERmxY3Y.js";function n(n){return a.post("examplan/getKcData?"+t.stringify(n))}function e(t){return a.post("examplan/kcSaveData",t)}function s(t){return a.post("examplan/deleteKcData?id="+t)}function r(t){return a.post("examplan/savePlateByCourseBase",t)}function o(n){return a.post("examplan/getTextBookData?"+t.stringify(n))}function p(t){return a.post("examplan/textBookSaveData",t)}function i(t){return a.post("examplan/deletetextBookData?id="+t)}function u(n){return a.post("examplan/getZyData?"+t.stringify(n))}function l(t){return a.post("examplan/zySaveData",t)}function f(t){return a.post("examplan/deleteZyData?id="+t)}function c(n){return a.post("examplan/getPlanData?"+t.stringify(n))}function x(t){return a.post("examplan/planSaveData",t)}function m(t){return a.post("examplan/deletePlanData?id="+t)}function D(n){return a.post("examplan/getCjPlanData?"+t.stringify(n))}function d(t){return a.post("examplan/cjplanSaveData",t)}function g(t){return a.post("examplan/deleteCjPlanData?id="+t)}function P(n){return a.post("examplan/getPlateData?"+t.stringify(n))}function y(t){return a.post("examplan/plateSaveData",t)}function v(t){return a.post("examplan/deletePlateData?id="+t)}function B(t){return a.post("examplan/getPlateTypeData")}function j(t){return a.post("examplan/savePlateByPlanid",t)}function S(t){return a.post("examplan/getPlateDataByCjPlanid?id="+t)}function C(t){return a.post("examplan/savePlateByCjPlanid",t)}function k(n){return a.post("examplan/getOnlineCourse?"+t.stringify(n))}function b(n){return a.post("examplan/getPublicPlanData?"+t.stringify(n))}function z(t){return a.post("examplan/publicPlanSaveData",t)}function K(t){return a.post("examplan/deletePublicPlanData?id="+t)}export{f as A,D as a,u as b,d as c,g as d,S as e,k as f,n as g,s as h,r as i,P as j,e as k,v as l,B as m,b as n,z as o,y as p,K as q,o as r,C as s,p as t,i as u,c as v,x as w,m as x,j as y,l as z};
