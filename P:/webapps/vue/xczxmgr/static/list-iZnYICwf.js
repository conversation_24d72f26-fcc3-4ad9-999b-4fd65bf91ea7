import{d as e,r as l,K as a,o,a as t,L as i,c as r,b as s,j as d,w as u,e as n,M as c,F as p,f as _,h as m,G as b,p as f,q as v,T as y,m as g,U as h,E as k,_ as w}from"./index-BERmxY3Y.js";import{k as V,l as x,G as C,m as U,u as I,E as j,n as z}from"./dzs-DZN_gNG7.js";import{A as B}from"./AssignResourceDialog-C5I0hvnU.js";import"./kcmgr-_69jTmcm.js";import"./course-DUm3UmHR.js";const G={class:"app-container"},N={class:"search-container"},P={class:"action-buttons"},R={key:1},$={key:1},q={class:"image-error"},F=["src"],M={key:0,class:"el-upload__tip"},D={key:0,class:"el-upload__tip"},E={class:"dialog-footer"},L={class:"preview-container"},K=["src"],O=["src"],T={key:2,class:"video-preview"},A=["src"],H=w(e({__name:"list",setup(e){const w=l(!1),H=l([]),J=l(0),S=l([]),W=a({page:1,page_size:10,search_key:"",ver:"",order_by:"id",order_type:"asc"}),Q=l(!1),X=a({visible:!1,url:""}),Y=a({visible:!1,url:""}),Z=a({visible:!1,title:"添加教材",loading:!1}),ee=a({visible:!1,title:"文件预览",loading:!1,type:"",url:""}),le=a({visible:!1,title:"课程资料",loading:!1,courseId:null,materials:[]}),ae=a({visible:!1,title:"指定资料",courseBaseId:null,courseInfoId:null,plateId:void 0}),oe=l(),te=a({id:void 0,course_base_id:void 0,course_code:"",course_name:"",course_credits:2,course_type:"",textbook_name:"",textbook_editor:"",publication_info:"",textbook_url:"",url:""}),ie={kc_bm:[{required:!0,message:"请输入课程代码",trigger:"blur"}],kc_mc:[{required:!0,message:"请输入课程名称",trigger:"blur"}],textbook_name:[{required:!0,message:"请输入教材名称",trigger:"blur"}]},re=l([]),se=async()=>{try{const e=await x();200===e.code?re.value=e.data||[]:k.error(e.msg||"获取课程列表失败")}catch(e){k.error("获取课程列表失败")}},de=e=>{const l=re.value.find((l=>l.id===e));l&&(te.course_code=l.course_code,te.course_name=l.course_name)},ue=async()=>{try{w.value=!0;const e=await U({...W,page:W.page,page_size:W.page_size});200===e.code?(H.value=e.data.list||[],J.value=e.data.total||0):k.error(e.msg||"获取数据失败")}catch(e){k.error("获取数据失败")}finally{w.value=!1}},ne=()=>{W.page=1,ue()},ce=()=>{W.search_key="",W.order_by="id",W.order_type="asc",S.value.length>0&&(W.ver=S.value[0].ver),ne()},pe=e=>{X.url=e,X.visible=!0},_e=e=>{Y.url=e,Y.visible=!0},me=()=>{be(),Z.title="添加教材",Z.visible=!0},be=()=>{var e;null==(e=oe.value)||e.resetFields(),te.id=void 0,te.course_base_id=void 0,te.course_code="",te.course_name="",te.course_credits=2,te.course_type="",te.textbook_name="",te.textbook_editor="",te.publication_info="",te.textbook_url="",te.url=""},fe=e=>{if(Q.value)return;const l="image"===e?ye.value:ge.value;l&&(l.value="",l.click())},ve=async(e,l)=>{var a;const o=null==(a=e.target.files)?void 0:a[0];if(o){if("image"===l){const e="image/jpeg"===o.type||"image/png"===o.type,l=o.size/1024/1024<5;if(!e)return void k.error("只能上传 JPG/PNG 格式的图片!");if(!l)return void k.error("图片大小不能超过 5MB!")}else{const e="application/pdf"===o.type||o.name.endsWith(".pdf"),l=o.size/1024/1024<50;if(!e)return void k.error("只能上传 PDF 格式的文件!");if(!l)return void k.error("文件大小不能超过 50MB!")}try{Q.value=!0;const e=new FormData;e.append("file",o),e.append("type",l);const a=await I(e);if(!0===a.success){const e=a.url;"image"===l?te.textbook_url=e:te.url=e,k.success("上传成功")}else k.error(a.msg||"上传失败")}catch(t){k.error("上传失败，请重试")}finally{Q.value=!1}}},ye=l(null),ge=l(null),he=async()=>{try{await oe.value.validate(),Z.loading=!0;const e=te.id?j:z,l=await e(te);200===l.code?(k.success(te.id?"更新成功":"添加成功"),Z.visible=!1,ue()):k.error(l.msg||"操作失败")}catch(e){}finally{Z.loading=!1}},ke=()=>{ae.visible=!1},we=()=>{k.success("资料指定成功"),ae.visible=!1};return o((async()=>{await(async()=>{try{const e=await C({});200===e.code?(S.value=e.data||[],S.value.length>0&&(W.ver=S.value[0].ver)):k.error(e.msg||"获取版本列表失败")}catch(e){k.error("获取版本列表失败")}})(),ue(),se()})),(e,l)=>{const a=t("el-option"),o=t("el-select"),x=t("el-form-item"),C=t("el-input"),U=t("el-button"),I=t("el-form"),j=t("el-table-column"),z=t("el-link"),J=t("el-table"),ue=t("el-icon"),Ve=t("el-image"),xe=t("el-dialog"),Ce=t("el-input-number"),Ue=t("el-card"),Ie=i("loading");return s(),r("div",G,[d(Ue,{class:"box-card"},{default:u((()=>[n("div",N,[d(I,{inline:!0,model:W,class:"demo-form-inline"},{default:u((()=>[d(x,{label:"版本"},{default:u((()=>[d(o,{modelValue:W.ver,"onUpdate:modelValue":l[0]||(l[0]=e=>W.ver=e),placeholder:"请选择版本",onChange:ne,style:{width:"150px"}},{default:u((()=>[(s(!0),r(p,null,_(S.value,(e=>(s(),m(a,{key:e.ver,label:e.ver,value:e.ver},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),d(x,{label:"搜索关键词"},{default:u((()=>[d(C,{modelValue:W.search_key,"onUpdate:modelValue":l[1]||(l[1]=e=>W.search_key=e),placeholder:"课程代码/名称/教材名称/主编",clearable:"",onClear:ne,onKeyup:b(ne,["enter"])},null,8,["modelValue"])])),_:1}),d(x,null,{default:u((()=>[d(U,{type:"primary",onClick:ne},{default:u((()=>l[21]||(l[21]=[f("查询")]))),_:1,__:[21]}),d(U,{onClick:ce},{default:u((()=>l[22]||(l[22]=[f("刷新")]))),_:1,__:[22]})])),_:1})])),_:1},8,["model"]),n("div",P,[d(U,{type:"success",onClick:me},{default:u((()=>l[23]||(l[23]=[f("添加教材")]))),_:1,__:[23]})])]),c((s(),m(J,{data:H.value,border:"",style:{width:"100%"}},{default:u((()=>[d(j,{prop:"",type:"index",label:"序号",sortable:"",align:"center",width:"60","header-align":"center"}),d(j,{prop:"course_code",label:"课程代码",sortable:"",width:"120"}),d(j,{prop:"course_name",label:"课程名称",sortable:"","min-width":"180"}),d(j,{prop:"course_credits",label:"学分",sortable:"",width:"80",align:"center"}),d(j,{prop:"course_type",label:"课程性质",sortable:"",width:"120"}),d(j,{prop:"textbook_name",label:"教材名称",sortable:"","min-width":"200"}),d(j,{prop:"textbook_editor",label:"主编",sortable:"",width:"120"}),d(j,{prop:"publication_info",label:"出版信息",sortable:"","min-width":"200"}),d(j,{label:"教材封面",width:"100",align:"center"},{default:u((({row:e})=>[e.textbook_url?(s(),m(z,{key:0,type:"primary",onClick:l=>pe(e.textbook_url)},{default:u((()=>l[24]||(l[24]=[f(" 查看封面 ")]))),_:2,__:[24]},1032,["onClick"])):(s(),r("span",R,"-"))])),_:1}),d(j,{label:"教材文件",width:"100",align:"center"},{default:u((({row:e})=>[e.url?(s(),m(z,{key:0,type:"primary",onClick:l=>_e(e.url)},{default:u((()=>l[25]||(l[25]=[f(" 查看文件 ")]))),_:2,__:[25]},1032,["onClick"])):(s(),r("span",$,"-"))])),_:1}),d(j,{label:"操作",width:"200",align:"center"},{default:u((e=>[d(U,{link:"",type:"primary",size:"small",onClick:l=>(async e=>{if(be(),0===re.value.length&&await se(),Object.keys(e).forEach((l=>{void 0!==e[l]&&(te[l]=e[l])})),e.course_base_id){const l=Number(e.course_base_id);te.course_base_id=l;const a=re.value.find((e=>Number(e.id)===l));a&&(te.course_code=a.course_code,te.course_name=a.course_name)}Z.title="编辑教材",Z.visible=!0})(e.row)},{default:u((()=>l[26]||(l[26]=[f("编辑")]))),_:2,__:[26]},1032,["onClick"]),d(U,{link:"",type:"success",size:"small",onClick:l=>(async e=>{if(e.course_base_id)try{le.loading=!0,le.courseId=e.course_base_id,le.title=`${e.course_name} - 课程资料`,le.visible=!0;const l=await V({course_base_id:e.course_base_id,course_jc_id:e.id});200===l.code?le.materials=(e=>{const l=[];if(e.dzs)for(const a of e.dzs)l.push({type:"电子书",title:a.title||"电子书",url:a.url});if(e.video)for(const a of e.video)l.push({type:"视频",title:a.title||"视频资料",url:a.url});if(e.job)for(const a of e.job)l.push({type:"试题",title:a.title||"试题资料",url:a.url});if(e.ksdg)for(const a of e.ksdg)l.push({type:"考试大纲",title:a.title||"考试大纲",url:a.url});return l})(l):k.error(l.msg||"获取资料失败")}catch(l){k.error("获取资料失败")}finally{le.loading=!1}else k.warning("该课程没有关联的资料")})(e.row)},{default:u((()=>l[27]||(l[27]=[f("查看资料")]))),_:2,__:[27]},1032,["onClick"])])),_:1})])),_:1},8,["data"])),[[Ie,w.value]]),d(xe,{modelValue:X.visible,"onUpdate:modelValue":l[2]||(l[2]=e=>X.visible=e),title:"教材封面预览",width:"50%"},{default:u((()=>[d(Ve,{src:X.url,fit:"contain",style:{width:"100%",height:"60vh"}},{error:u((()=>[n("div",q,[d(ue,null,{default:u((()=>[d(v(y))])),_:1}),l[28]||(l[28]=n("span",null,"图片加载失败",-1))])])),_:1},8,["src"])])),_:1},8,["modelValue"]),d(xe,{modelValue:Y.visible,"onUpdate:modelValue":l[3]||(l[3]=e=>Y.visible=e),title:"教材文件预览",width:"80%",top:"5vh"},{default:u((()=>[Y.url?(s(),r("iframe",{key:0,src:`https://xczx7.swufe.edu.cn/vue/pdfjs-5.3.31-dist/web/viewer.html?file=${encodeURIComponent(Y.url)}`,style:{width:"100%",height:"80vh",border:"none"}},null,8,F)):g("",!0)])),_:1},8,["modelValue"]),d(xe,{modelValue:Z.visible,"onUpdate:modelValue":l[17]||(l[17]=e=>Z.visible=e),title:Z.title,width:"50%","close-on-click-modal":!1,onClosed:be},{footer:u((()=>[n("span",E,[d(U,{onClick:l[16]||(l[16]=e=>Z.visible=!1)},{default:u((()=>l[35]||(l[35]=[f("取消")]))),_:1,__:[35]}),d(U,{type:"primary",onClick:he,loading:Z.loading},{default:u((()=>l[36]||(l[36]=[f(" 确定 ")]))),_:1,__:[36]},8,["loading"])])])),default:u((()=>[d(I,{ref_key:"formRef",ref:oe,model:te,rules:ie,"label-width":"100px"},{default:u((()=>[d(x,{label:"选择课程",prop:"course_base_id",rules:[{required:!0,message:"请选择课程",trigger:"change"}]},{default:u((()=>[d(o,{modelValue:te.course_base_id,"onUpdate:modelValue":l[4]||(l[4]=e=>te.course_base_id=e),placeholder:"请选择课程",style:{width:"100%"},onChange:de,filterable:"",clearable:""},{default:u((()=>[(s(!0),r(p,null,_(re.value,(e=>(s(),m(a,{key:e.id,label:`${e.kc_bm} ${e.kc_mc}`,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),d(x,{label:"学分",prop:"course_credits"},{default:u((()=>[d(Ce,{modelValue:te.course_credits,"onUpdate:modelValue":l[5]||(l[5]=e=>te.course_credits=e),min:0,step:.5,precision:1},null,8,["modelValue"])])),_:1}),d(x,{label:"课程性质",prop:"course_type"},{default:u((()=>[d(C,{modelValue:te.course_type,"onUpdate:modelValue":l[6]||(l[6]=e=>te.course_type=e),placeholder:"请输入课程性质"},null,8,["modelValue"])])),_:1}),d(x,{label:"教材名称",prop:"textbook_name"},{default:u((()=>[d(C,{modelValue:te.textbook_name,"onUpdate:modelValue":l[7]||(l[7]=e=>te.textbook_name=e),placeholder:"请输入教材名称"},null,8,["modelValue"])])),_:1}),d(x,{label:"主编",prop:"textbook_editor"},{default:u((()=>[d(C,{modelValue:te.textbook_editor,"onUpdate:modelValue":l[8]||(l[8]=e=>te.textbook_editor=e),placeholder:"请输入主编姓名"},null,8,["modelValue"])])),_:1}),d(x,{label:"出版信息",prop:"publication_info"},{default:u((()=>[d(C,{modelValue:te.publication_info,"onUpdate:modelValue":l[9]||(l[9]=e=>te.publication_info=e),type:"textarea",rows:2,placeholder:"请输入出版信息，如：出版社, 出版年份"},null,8,["modelValue"])])),_:1}),d(x,{label:"教材封面",prop:"textbook_url"},{default:u((()=>[n("div",{class:"upload-area",onClick:l[11]||(l[11]=e=>fe("image"))},[d(U,{type:"primary",loading:Q.value},{default:u((()=>l[29]||(l[29]=[f("点击上传")]))),_:1,__:[29]},8,["loading"]),te.textbook_url?(s(),r("div",M,[d(z,{type:"success",onClick:l[10]||(l[10]=h((e=>pe(te.textbook_url)),["stop"]))},{default:u((()=>l[30]||(l[30]=[f(" 已上传，点击查看 ")]))),_:1,__:[30]})])):g("",!0),l[31]||(l[31]=n("div",{class:"el-upload__tip"}," 支持 JPG/PNG 格式，建议尺寸 3:4 ",-1))]),n("input",{ref_key:"imageInput",ref:ye,type:"file",accept:"image/jpeg,image/png",style:{display:"none"},onChange:l[12]||(l[12]=e=>ve(e,"image"))},null,544)])),_:1}),d(x,{label:"教材文件",prop:"url"},{default:u((()=>[n("div",{class:"upload-area",onClick:l[14]||(l[14]=e=>fe("pdf"))},[d(U,{type:"primary",loading:Q.value},{default:u((()=>l[32]||(l[32]=[f("点击上传")]))),_:1,__:[32]},8,["loading"]),te.url?(s(),r("div",D,[d(z,{type:"success",onClick:l[13]||(l[13]=h((e=>_e(te.url)),["stop"]))},{default:u((()=>l[33]||(l[33]=[f(" 已上传，点击查看 ")]))),_:1,__:[33]})])):g("",!0),l[34]||(l[34]=n("div",{class:"el-upload__tip"}," 支持 PDF 格式，建议文件小于 50MB ",-1))]),n("input",{ref_key:"pdfInput",ref:ge,type:"file",accept:".pdf",style:{display:"none"},onChange:l[15]||(l[15]=e=>ve(e,"pdf"))},null,544)])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue","title"]),d(xe,{modelValue:le.visible,"onUpdate:modelValue":l[18]||(l[18]=e=>le.visible=e),title:le.title,width:"40%","close-on-click-modal":!1},{default:u((()=>[c((s(),m(J,{data:le.materials,border:"",style:{width:"100%"}},{default:u((()=>[d(j,{prop:"type",label:"类型",width:"120"}),d(j,{prop:"title",label:"名称"}),d(j,{label:"操作",width:"100",align:"center"},{default:u((({row:e})=>[d(U,{type:"primary",link:"",onClick:l=>(e=>{var l;const a=e.url,o=(null==(l=a.split(".").pop())?void 0:l.toLowerCase())||"";if(ee.title=e.title||"文件预览",ee.url=a,["doc","docx","xls","xlsx","ppt","pptx"].includes(o))ee.type="office",ee.url=`https://view.officeapps.live.com/op/view.aspx?src=${encodeURIComponent(a)}&wdOrigin=BROWSELINK`;else if("pdf"===o)ee.type="pdf",ee.url=`https://xczx7.swufe.edu.cn/vue/pdfjs-5.3.31-dist/web/viewer.html?file=${encodeURIComponent(a)}`;else{if(!["mp4","webm","ogg"].includes(o))return void window.open(a,"_blank","noopener,noreferrer");ee.type="video"}ee.visible=!0})(e)},{default:u((()=>l[37]||(l[37]=[f(" 查看 ")]))),_:2,__:[37]},1032,["onClick"])])),_:1})])),_:1},8,["data"])),[[Ie,le.loading]])])),_:1},8,["modelValue","title"]),d(xe,{modelValue:ee.visible,"onUpdate:modelValue":l[19]||(l[19]=e=>ee.visible=e),title:ee.title,width:"80%",top:"5vh","close-on-click-modal":!1,"destroy-on-close":""},{default:u((()=>[c((s(),r("div",L,["office"===ee.type?(s(),r("iframe",{key:0,src:ee.url,frameborder:"0",style:{width:"100%",height:"70vh"},allowfullscreen:""},null,8,K)):"pdf"===ee.type?(s(),r("iframe",{key:1,src:ee.url,frameborder:"0",style:{width:"100%",height:"70vh"}},null,8,O)):"video"===ee.type?(s(),r("div",T,[n("video",{controls:"",style:{width:"100%","max-height":"70vh"},src:ee.url}," 您的浏览器不支持 HTML5 视频标签。 ",8,A)])):g("",!0)])),[[Ie,ee.loading]])])),_:1},8,["modelValue","title"]),d(B,{modelValue:ae.visible,"onUpdate:modelValue":l[20]||(l[20]=e=>ae.visible=e),"course-base-id":ae.courseBaseId,"course-info-id":ae.courseInfoId,"plate-id":ae.plateId,title:ae.title,onClose:ke,onSave:we},null,8,["modelValue","course-base-id","course-info-id","plate-id","title"])])),_:1})])}}}),[["__scopeId","data-v-e35d27b1"]]);export{H as default};
