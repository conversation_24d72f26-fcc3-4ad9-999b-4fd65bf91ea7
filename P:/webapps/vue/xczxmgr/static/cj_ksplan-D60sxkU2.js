import{a as e,b as l,c as a,d as t,g as s,e as o,s as d,f as i}from"./exam-jF1-Sa8S.js";import{d as u,r as n,o as r,E as p,a as c,c as _,b as v,e as m,j as f,w as g,p as b,t as h,h as y,F as k,f as x,v as V}from"./index-BERmxY3Y.js";const w={class:"examplan",style:{padding:"5px"}},C={style:{}},U={style:{padding:"10px 20px"}},j={style:{color:"green"}},z={style:{color:"green"}},F={class:"dialog-footer"},E={class:"m-2",style:{display:"flex","justify-content":"center"}},D={style:{padding:"10px 20px",height:"370px",width:"610px"}},S={style:{padding:"10px 0px"}},L={style:{color:"#409EFF","font-weight":"bold"}},$={style:{padding:"0px 20px",height:"380px",width:"610px","overflow-y":"auto"}},M=["onDragstart","onDragenter"],T={style:{width:"100%",display:"flex","align-items":"center"}},B={style:{"margin-left":"15px"}},H={style:{"margin-left":"40px","margin-top":"1px"},class:"cj_ksplan-item"},A={class:"dialog-footer",style:{padding:"10px 20px","text-align":"center","border-top":"1px solid #F2F6FC"}},O=u({__name:"cj_ksplan",setup(u){const O=n([]);n({});const P=n(),R=n(window.innerHeight-200);r((()=>{ee(),ae()}));const J=n({id:"",zy_bm:"",zy_mc:"",status:"0"}),K=n("");n("");const N=n(!1),Z=n([]),q=n([]),G=n([]),I=n([]);n([]);const Q=n({}),W=n(!1),X=n([]),Y=n([]),ee=()=>{e({filterZy:K.value}).then((e=>{200==e.code?O.value=e.data:p.error(e.msg)}))},le=e=>{P.value=e},ae=()=>{l({}).then((e=>{200==e.code?(Z.value=e.data,Z.value.forEach((e=>{e.zy_mcs=`(${e.zy_bm})${e.zy_mc}`}))):p.error(e.msg)}))},te=()=>{s({}).then((e=>{200==e.code?(q.value=e.data.filter((e=>4==e.kc_bm.length)),q.value.forEach((e=>{e.kc_mcs=`(${e.kc_bm})${e.kc_mc}`}))):p.error(e.msg)}))},se=e=>{o(e).then((e=>{if(200==e.code){var l=[];if(e.data){const{allList:a,haveList:t}=e.data;t&&t.forEach((e=>{l.push(e.plate_id)})),I.value=l,G.value=a,G.value.forEach((e=>{e.label=e.title,e.key=e.id})),ie()}else I.value=[]}else p.error(e.msg)}))},oe=e=>{var l=q.value.find((l=>l.id==e));J.value.kc_mc=l.kc_mc,J.value.kc_bm=l.kc_bm,de()},de=()=>{i({id:J.value.course_base_id}).then((e=>{200==e.code?(X.value=e.data,X.value.forEach((e=>{e.mc="版本："+e.ver+"【"+(e.is_online="在线课程")+"】【"+(1==e.is_pub?"已发布":"未发布")+"】"}))):p.error(e.msg)}))},ie=()=>{Y.value=[],I.value.length>0&&G.value.forEach((e=>{I.value.indexOf(e.id)>-1&&(e.is_list=e.is_list?e.is_list:0,e.is_blank=e.is_blank?e.is_blank:0,e.show_progress=e.show_progress?e.show_progress:0,Y.value.push(e))}))};let ue=0;function ne(e){e.preventDefault(),e.dataTransfer.dropEffect="move"}function re(e){e.target.classList.remove("moveing")}return(e,l)=>{const s=c("el-button"),o=c("el-table-column"),i=c("EditPen"),u=c("el-icon"),n=c("el-link"),r=c("el-tag"),K=c("el-table"),Z=c("el-option"),pe=c("el-select"),ce=c("el-form-item"),_e=c("el-input"),ve=c("el-radio"),me=c("el-radio-group"),fe=c("el-form"),ge=c("el-tab-pane"),be=c("el-tabs"),he=c("el-dialog"),ye=c("el-transfer");return v(),_("div",w,[m("div",null,[f(s,{type:"primary",onClick:ee,icon:"RefreshRight"}),f(s,{type:"primary",onClick:l[0]||(l[0]=e=>(J.value={id:"",course_base_id:"",course_info_id:"",ks_lx:"",kc_bm:"",kc_mc:"",ks_sj:"",remark:"",xf:0,is_yy2_tk:"0",status:"0"},ae(),te(),void(N.value=!0))),plain:"",icon:"DocumentAdd"},{default:g((()=>l[18]||(l[18]=[b("添加")]))),_:1,__:[18]}),f(s,{type:"danger",onClick:l[1]||(l[1]=e=>{P.value?V.confirm("此操作将永久删除该计划, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{t(P.value.id).then((e=>{200==e.code?(p.success(e.msg),ee()):p.error(e.msg)}))})).catch((()=>{p({type:"info",message:"已取消删除"})})):p.warning("请先选择要删除的课程")}),plain:"",icon:"DeleteFilled"},{default:g((()=>l[19]||(l[19]=[b("删除")]))),_:1,__:[19]})]),f(K,{tableHeight:R.value,data:O.value,size:"mini",stripe:"",style:{width:"100%","margin-top":"10px"},"highlight-current-row":"",onCurrentChange:le},{default:g((()=>[f(o,{type:"index",width:"50",align:"center"}),f(o,{prop:"kc_bm",label:"课程编码",align:"center"}),f(o,{prop:"kc_mc",label:"课程名称",align:"center"},{default:g((e=>[f(n,{onClick:l=>{return a=e.$index,J.value=O.value[a],J.value.status=J.value.status.toString(),J.value.is_yy2_tk=J.value.is_yy2_tk.toString(),J.value.ks_sj=J.value.ks_sj.split(","),de(),N.value=!0,ae(),void te();var a},type:"primary"},{default:g((()=>[b(h(e.row.kc_mc)+"  ",1),f(u,{style:{"font-size":"13px"}},{default:g((()=>[f(i)])),_:1})])),_:2},1032,["onClick"])])),_:1}),f(o,{prop:"course_page",label:"页面展示形式",align:"center"},{default:g((e=>[1==e.row.course_page?(v(),y(r,{key:0,type:"success"},{default:g((()=>l[20]||(l[20]=[b("课程主页")]))),_:1,__:[20]})):(v(),y(r,{key:1},{default:g((()=>l[21]||(l[21]=[b("课程模块")]))),_:1,__:[21]}))])),_:1}),f(o,{prop:"ks_sj",label:"考试时间（月）",align:"center"}),f(o,{prop:"kc_mc",label:"板块",align:"center"},{default:g((e=>[f(n,{onClick:l=>{return a=e.row,Q.value=a,se(a.id),void(W.value=!0);var a},type:"primary"},{default:g((()=>l[22]||(l[22]=[b("指定板块")]))),_:2,__:[22]},1032,["onClick"])])),_:1}),f(o,{prop:"status",label:"状态",align:"center"},{default:g((e=>[1==e.row.status?(v(),y(r,{key:0,type:"success"},{default:g((()=>l[23]||(l[23]=[b("正常")]))),_:1,__:[23]})):(v(),y(r,{key:1,type:"danger"},{default:g((()=>l[24]||(l[24]=[b("禁用")]))),_:1,__:[24]}))])),_:1}),f(o,{prop:"remark",label:"备注",align:"center"}),f(o,{prop:"create_name",label:"创建人",align:"center"}),f(o,{prop:"create_date",label:"创建时间",align:"center"})])),_:1},8,["tableHeight","data"]),f(he,{modelValue:N.value,"onUpdate:modelValue":l[12]||(l[12]=e=>N.value=e),title:"添加专业信息",width:"750",top:"6vh","append-to-body":!0,"close-on-click-modal":!1,"close-on-press-escape":!1},{footer:g((()=>[m("span",F,[f(s,{onClick:l[10]||(l[10]=e=>N.value=!1)},{default:g((()=>l[31]||(l[31]=[b("取消")]))),_:1,__:[31]}),f(s,{type:"primary",onClick:l[11]||(l[11]=e=>(J.value.id=J.value.id?J.value.id:0,J.value.ks_sj=J.value.ks_sj.join(","),void a(J.value).then((e=>{"200"==e.code?(p.success(e.msg),N.value=!1,ee()):p.error(e.msg)})))),disabled:!J.value.kc_bm},{default:g((()=>l[32]||(l[32]=[b("保存 ")]))),_:1,__:[32]},8,["disabled"])])])),default:g((()=>[m("div",C,[f(be,{"tab-position":"top",style:{},class:"demo-tabs"},{default:g((()=>[f(ge,{label:"基本设置"},{default:g((()=>[m("div",U,[f(fe,{inline:!0,model:J.value,class:"demo-form-inline","label-position":"right","label-width":"110px"},{default:g((()=>[f(ce,{label:"选择课程"},{default:g((()=>[f(pe,{modelValue:J.value.course_base_id,"onUpdate:modelValue":l[2]||(l[2]=e=>J.value.course_base_id=e),class:"m-2",placeholder:"Select",style:{width:"190px"},onChange:oe},{default:g((()=>[(v(!0),_(k,null,x(q.value,(e=>(v(),y(Z,{key:e.id,label:e.kc_mcs,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),l[29]||(l[29]=m("p",null,null,-1)),f(ce,{label:"在线课程版本"},{default:g((()=>[f(pe,{modelValue:J.value.course_info_id,"onUpdate:modelValue":l[3]||(l[3]=e=>J.value.course_info_id=e),class:"m-2",placeholder:"Select",style:{width:"190px"}},{default:g((()=>[(v(!0),_(k,null,x(X.value,(e=>(v(),y(Z,{key:e.id,label:e.mc,value:e.id},{default:g((()=>[m("span",null,[b("版本："+h(e.ver)+"【",1),m("span",j,h(e.is_online="在线课程"),1),l[25]||(l[25]=b("】 【")),m("span",z,h(1==e.is_pub?"已发布":"未发布"),1),l[26]||(l[26]=b("】"))])])),_:2},1032,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),l[30]||(l[30]=m("p",null,null,-1)),f(ce,{label:"课程编码"},{default:g((()=>[f(_e,{modelValue:J.value.kc_bm,"onUpdate:modelValue":l[4]||(l[4]=e=>J.value.kc_bm=e),disabled:!0,placeholder:"请输入课程名称",style:{width:"190px"}},null,8,["modelValue"])])),_:1}),f(ce,{label:"课程名称"},{default:g((()=>[f(_e,{modelValue:J.value.kc_mc,"onUpdate:modelValue":l[5]||(l[5]=e=>J.value.kc_mc=e),disabled:!0,placeholder:"请输入课程名称",style:{width:"190px"}},null,8,["modelValue"])])),_:1}),f(ce,{label:"课程展示方式"},{default:g((()=>[f(pe,{modelValue:J.value.course_page,"onUpdate:modelValue":l[6]||(l[6]=e=>J.value.course_page=e),style:{width:"190px"},placeholder:"Select"},{default:g((()=>[f(Z,{label:"课程主页",value:1}),f(Z,{label:"课程模块",value:0})])),_:1},8,["modelValue"])])),_:1}),f(ce,{label:"考试月份"},{default:g((()=>[f(pe,{modelValue:J.value.ks_sj,"onUpdate:modelValue":l[7]||(l[7]=e=>J.value.ks_sj=e),style:{width:"190px"},multiple:"","collapse-tags":"","collapse-tags-tooltip":"",placeholder:"Select"},{default:g((()=>[(v(),_(k,null,x(["1","2","3","4","5","6","7","8","9","10","11","12"],(e=>f(Z,{key:e,label:e+"月",value:e},null,8,["label","value"]))),64))])),_:1},8,["modelValue"])])),_:1}),f(ce,{label:"状态"},{default:g((()=>[f(me,{modelValue:J.value.status,"onUpdate:modelValue":l[8]||(l[8]=e=>J.value.status=e)},{default:g((()=>[f(ve,{label:"1"},{default:g((()=>l[27]||(l[27]=[b("启用")]))),_:1,__:[27]}),f(ve,{label:"0"},{default:g((()=>l[28]||(l[28]=[b("禁用")]))),_:1,__:[28]})])),_:1},8,["modelValue"])])),_:1}),f(ce,{label:"备注"},{default:g((()=>[f(_e,{modelValue:J.value.remark,"onUpdate:modelValue":l[9]||(l[9]=e=>J.value.remark=e),rows:2,type:"textarea",style:{width:"500px"}},null,8,["modelValue"])])),_:1})])),_:1,__:[29,30]},8,["model"])])])),_:1})])),_:1})])])),_:1},8,["modelValue"]),f(he,{modelValue:W.value,"onUpdate:modelValue":l[17]||(l[17]=e=>W.value=e),width:"700","close-on-click-modal":!1,"close-on-press-escape":!1,"append-to-body":!0,top:"6vh"},{default:g((()=>[m("div",E,[f(be,{"tab-position":"top",style:{},class:"demo-tabs"},{default:g((()=>[f(ge,{label:"板块设置"},{default:g((()=>[m("div",D,[m("div",S,[l[33]||(l[33]=b(" 为【")),m("span",L,h(Q.value.kc_mc),1),l[34]||(l[34]=b("】课程指定板块 "))]),f(ye,{modelValue:I.value,"onUpdate:modelValue":l[13]||(l[13]=e=>I.value=e),data:G.value,titles:["未开通板块","已开通板块"],onClick:l[14]||(l[14]=e=>ie())},null,8,["modelValue","data"])])])),_:1}),f(ge,{label:"板块详细设置"},{default:g((()=>[m("div",$,[(v(!0),_(k,null,x(Y.value,((e,a)=>(v(),_("div",{class:"item",key:e.id,draggable:"true",onDragstart:e=>{return t=a,(l=e).stopPropagation(),ue=t,void setTimeout((()=>{l.target.classList.add("moveing")}),0);var l,t},onDragenter:e=>function(e,l){if(e.preventDefault(),ue!==l){const e=Y.value[ue];Y.value.splice(ue,1),Y.value.splice(l,0,e),ue=l}}(e,a),onDragend:re,onDragover:ne},[m("div",T,[l[35]||(l[35]=m("div",{style:{"margin-top":"4px"},class:"dragCurr"},[m("svg",{viewBox:"0 0 1024 1024",width:"30",height:"30"},[m("path",{d:"M512 0a512 512 0 0 1 512 512 512 512 0 0 1-512 512A512 512 0 0 1 0 512 512 512 0 0 1 512 0z",fill:"#FFFFFF","p-id":"7425"}),m("path",{d:"M509.5424 720.6912L593.92 636.5184l35.2256 35.2256-119.1936 118.784-118.784-118.784 35.2256-35.2256zM509.952 245.76l118.784 118.784-34.816 35.2256-83.7632-84.1728-84.1728 84.1728L389.12 364.544l119.808-118.784zM307.2 482.304v-49.7664h409.6v49.7664z m0 112.8448v-49.7664h409.6v49.7664z",fill:"#2693FF","p-id":"7426"})])],-1)),m("div",B," 【"+h(a+1)+"】 "+h(e.title),1)]),m("div",H,[f(pe,{modelValue:e.is_list,"onUpdate:modelValue":l=>e.is_list=l,placeholder:"展示方式",size:"mini",style:{width:"100px"}},{default:g((()=>[(v(),y(Z,{key:1,label:"有列表",value:1})),(v(),y(Z,{key:0,label:"无列表",value:0}))])),_:2},1032,["modelValue","onUpdate:modelValue"]),f(pe,{modelValue:e.is_blank,"onUpdate:modelValue":l=>e.is_blank=l,placeholder:"打开方式",size:"mini",style:{width:"150px","margin-left":"15px"}},{default:g((()=>[(v(),y(Z,{key:1,label:"新标签页打开",value:1})),(v(),y(Z,{key:0,label:"当前页打开",value:0}))])),_:2},1032,["modelValue","onUpdate:modelValue"]),f(pe,{modelValue:e.show_progress,"onUpdate:modelValue":l=>e.show_progress=l,placeholder:"显示进度条",size:"mini",style:{width:"150px","margin-left":"15px"}},{default:g((()=>[(v(),y(Z,{key:1,label:"显示进度条",value:1})),(v(),y(Z,{key:0,label:"不显示进度条",value:0}))])),_:2},1032,["modelValue","onUpdate:modelValue"])])],40,M)))),128))])])),_:1})])),_:1})]),m("div",A,[f(s,{onClick:l[15]||(l[15]=e=>W.value=!1)},{default:g((()=>l[36]||(l[36]=[b("取消")]))),_:1,__:[36]}),f(s,{type:"primary",onClick:l[16]||(l[16]=e=>(()=>{var e=[];if(Y.value.forEach(((l,a)=>{var t={plate_id:l.id,is_list:l.is_list,is_blank:l.is_blank,show_progress:l.show_progress,sn:a+1};e.push(t)})),0!=e.length){var l={id:Q.value.id,jsonstr:JSON.stringify(e)};d(l).then((e=>{"200"==e.code?(p.success(e.msg),ee()):p.error(e.msg)}))}else p.warning("请至少选择一个板块")})())},{default:g((()=>l[37]||(l[37]=[b("保存 ")]))),_:1,__:[37]})])])),_:1},8,["modelValue"])])}}});export{O as default};
