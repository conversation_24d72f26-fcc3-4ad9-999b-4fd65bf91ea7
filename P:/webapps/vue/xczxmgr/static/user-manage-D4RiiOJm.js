import{h as e,i as l,j as a,k as t,G as i,l as o}from"./settings-CpWN36lq.js";import{d,K as u,r,o as n,a as s,L as p,c,b as m,e as g,j as h,w as _,p as v,F as f,f as y,h as b,t as w,M as V,E as x,v as k}from"./index-BERmxY3Y.js";const z={class:"userManage page"},C={class:"header"},U={style:{float:"left"}},S={style:{float:"right",color:"#8492a6","font-size":"13px"}},T={class:"body"},j={style:{width:"100%"}},D={class:"role_titleClass"},P=["innerHTML"],q={key:0,style:{color:"blue"}},H={key:1,style:{color:"Red"}},I={style:{"margin-top":"16px","text-align":"right"}},M={style:{width:"550px","margin-left":"40px"}},F={style:{width:"100%","text-align":"center"}},L={style:{width:"100%","text-align":"center"}},B={class:"dialog-footer"},G=d({__name:"user-manage",setup(d){const G=u({loading:!1,tableHeight:window.innerHeight-220,keySearch:"",user_type_id:2,dataTable:[],total:0,pageSize:30,currentPage:1}),N=r([]),E=r([]),K=r(!1),O=r([]),R=r([]),X=r([]),A=r({}),J=r(!1),Q=r(""),W=r({uid:[{required:!0,message:"请输入登录名(UID)",trigger:"blur"}],user_type_id:[{required:!0,message:"请选择用户类型",trigger:"blur"}],status:[{required:!0,message:"请选择用户状态",trigger:"blur"}],name:[{required:!0,message:"请输入名称(姓名)",trigger:"blur"}],mobile:[{required:!0,message:"请输入联系电话",trigger:"blur"}],pwd:[{required:!0,maxlength:16,minlength:3,message:"请输入密码3~16位之间",trigger:"blur"}]}),Y=()=>{G.loading=!0;const e={keySearch:G.keySearch,user_type_id:G.user_type_id,currentPage:G.currentPage,pageSize:G.pageSize};l(e).then((e=>{G.dataTable=e.data||[],G.total=e.total||G.dataTable.length,G.loading=!1})).catch((e=>{G.loading=!1}))},Z=()=>{A.value={id:0,uid:"",pwd:"",pwd_md5:"",status:1},Q.value="新增用户",J.value=!0},$=()=>{if(null==E.value||1!==E.value.length)return x({message:"请勾选一条数据进行编辑！",type:"error"}),!1;Q.value="编辑【"+E.value[0].uid+"】",J.value=!0,A.value=E.value[0]},ee=()=>{if(!(null!=E&&E.value.length>0))return x({message:"请至少勾选一条数据进行删除！",type:"error"}),!1;{const e=E.value;k.confirm("此操作将永久删除【"+e.length+"】条数据, 是否继续?","提示",{confirmButtonText:"删除",cancelButtonText:"取消",type:"warning"}).then((()=>{const l=[];for(var a=0;a<e.length;a++)l.push(e[a].id);var i=l.join(",");t({id:i}).then((e=>{e.data>0?(x({type:"success",message:"成功删除!"+e.data+"条"}),Y()):x({type:"info",message:"删除失败!"+e.msg})})).catch((e=>{x({type:"info",message:"删除失败!"+e})}))})).catch((()=>{x({type:"info",message:"取消删除!"})}))}},le=()=>{i({key:""}).then((e=>{X.value=e.data})).catch((e=>{}))},ae=e=>{E.value=e};n((()=>{G.loading=!0,e({}).then((e=>{N.value=e.data,G.user_type_id=e.data.length>0?e.data[0].id:"",Y()})).catch((e=>{x({type:"error",message:"出错了!"+e})}))}));const te=e=>{G.pageSize=e,Y()},ie=e=>{G.currentPage=e,Y()};return(e,l)=>{const t=s("el-button"),i=s("el-input"),d=s("el-tag"),u=s("el-option"),r=s("el-select"),n=s("el-table-column"),k=s("CirclePlusFilled"),E=s("el-icon"),oe=s("el-tooltip"),de=s("el-table"),ue=s("el-pagination"),re=s("el-checkbox"),ne=s("el-checkbox-group"),se=s("el-dialog"),pe=s("el-form-item"),ce=s("el-radio"),me=s("el-radio-group"),ge=s("el-form"),he=p("loading");return m(),c("div",z,[g("div",C,[h(t,{type:"success",plain:"",size:"default",icon:"refresh",round:"",onClick:Y}),h(i,{modelValue:G.keySearch,"onUpdate:modelValue":l[0]||(l[0]=e=>G.keySearch=e),placeholder:"用户名|姓名|身份证|手机号",size:"default",style:{width:"260px",margin:"0 10px"},clearable:"",onChange:Y},{append:_((()=>[h(t,{icon:"Search",onClick:Y})])),_:1},8,["modelValue"]),h(d,{class:"el_tag_5"},{default:_((()=>l[18]||(l[18]=[v("用户类型")]))),_:1,__:[18]}),h(r,{modelValue:G.user_type_id,"onUpdate:modelValue":l[1]||(l[1]=e=>G.user_type_id=e),clearable:"",filterable:"",style:{width:"160px"},placeholder:"用户类型",onChange:Y},{default:_((()=>[(m(!0),c(f,null,y(N.value,(e=>(m(),b(u,{key:e.id,label:e.title,value:e.id,style:{width:"260px"}},{default:_((()=>[g("span",U,w(e.id),1),g("span",S,w(e.title),1)])),_:2},1032,["label","value"])))),128))])),_:1},8,["modelValue"]),h(t,{type:"primary",plain:"",size:"default",round:"",icon:"CirclePlus",onClick:Z},{default:_((()=>l[19]||(l[19]=[v(" 新增")]))),_:1,__:[19]}),h(t,{type:"success",plain:"",size:"default",round:"",icon:"edit",onClick:$},{default:_((()=>l[20]||(l[20]=[v("编辑")]))),_:1,__:[20]}),h(t,{type:"danger",plain:"",size:"default",round:"",icon:"delete",onClick:ee},{default:_((()=>l[21]||(l[21]=[v("删除")]))),_:1,__:[21]})]),g("div",T,[V((m(),b(de,{data:G.dataTable,border:"",class:"modTable",height:G.tableHeight,style:{width:"100%"},onSelectionChange:ae},{default:_((()=>[h(n,{type:"selection",width:"50",align:"center"}),h(n,{prop:"user_type_title",label:"用户类型","min-width":"100",align:"center","header-align":"center","show-overflow-tooltip":""}),h(n,{prop:"uid",label:"用户名","min-width":"120",align:"center","show-overflow-tooltip":""}),h(n,{prop:"pwd",label:"密码","min-width":"120",align:"left","header-align":"center"}),h(n,{prop:"pwd_md5",label:"密码MD5","min-width":"120",align:"left","header-align":"center","show-overflow-tooltip":""}),h(n,{prop:"name",label:"姓名","min-width":"120",align:"center","header-align":"center","show-overflow-tooltip":""}),h(n,{prop:"status",label:"系统角色",align:"center","header-align":"center","min-width":"140","show-overflow-tooltip":""},{default:_((e=>[g("div",j,[h(t,{link:"",type:"primary",style:{float:"right",color:"#1890FF",cursor:"pointer"},size:"small",onClick:l=>(e=>{K.value=!0,O.value=e,R.value=[];for(var l=O.value.role_id.split(","),a=0;a<l.length;a++){var t=parseInt(l[a]);R.value.indexOf(t)<0&&R.value.push(t)}le()})(e.row)},{default:_((()=>[h(E,null,{default:_((()=>[h(k)])),_:1})])),_:2},1032,["onClick"]),g("div",D,[h(oe,{class:"item",effect:"dark",content:e.row.role_title.replace(/,/g,"  | "),placement:"bottom-start"},{default:_((()=>[g("span",{innerHTML:e.row.role_title.replace(/,/g,"  |  ")},null,8,P)])),_:2},1032,["content"])])])])),_:1}),h(n,{prop:"mobile",label:"手机号","min-width":"100",align:"center","header-align":"center"}),h(n,{prop:"sfzh",label:"身份证号","min-width":"140",align:"center","header-align":"center"}),h(n,{prop:"email",label:"邮箱","min-width":"100",align:"center","header-align":"center","show-overflow-tooltip":""}),h(n,{prop:"status",label:"状态",align:"center","min-width":"60","show-overflow-tooltip":""},{default:_((e=>[1===e.row.status?(m(),c("span",q,"启用")):(m(),c("span",H,"禁用"))])),_:1}),h(n,{prop:"create_date",label:"创建时间","min-width":"140",align:"center","header-align":"center"}),h(n,{prop:"create_user",label:"创建人","min-width":"80",align:"center","header-align":"center"}),h(n,{prop:"zd_bm",label:"站点编码","min-width":"80",align:"center","header-align":"center"}),h(n,{prop:"zd_mc",label:"站点名称","min-width":"140",align:"left","header-align":"center","show-overflow-tooltip":""})])),_:1},8,["data","height"])),[[he,G.loading]]),g("div",I,[h(ue,{background:"",layout:"total, sizes, prev, pager, next, jumper",total:G.total,"page-size":G.pageSize,"current-page":G.currentPage,"page-sizes":[20,50,100,500,G.total],onSizeChange:te,onCurrentChange:ie},null,8,["total","page-size","current-page","page-sizes"])])]),h(se,{title:"添加角色",modelValue:K.value,"onUpdate:modelValue":l[4]||(l[4]=e=>K.value=e),ref:"DialogroleVisible",draggable:"",width:"600px","close-on-press-escape":!1,"close-on-click-modal":!1},{default:_((()=>[g("div",M,[h(ne,{modelValue:R.value,"onUpdate:modelValue":l[2]||(l[2]=e=>R.value=e),class:"roleCheckGroup"},{default:_((()=>[(m(!0),c(f,null,y(X.value,(e=>(m(),b(re,{style:{width:"150px"},key:e.id,label:e.id},{default:_((()=>[v(w(e.id)+"-"+w(e.title),1)])),_:2},1032,["label"])))),128))])),_:1},8,["modelValue"]),l[22]||(l[22]=g("p",null,null,-1))]),g("div",F,[h(t,{size:"mini",type:"primary",onClick:l[3]||(l[3]=e=>(()=>{var e=R.value.join(",").replace("NaN,","");const l={user_id:O.value.id,role_ids:e};o(l).then((e=>{e.data>0?(K.value=!1,x({message:"保存成功！",type:"success"}),Y()):x({message:"保存失败！"+e.msg,type:"error"})})).catch((e=>{}))})())},{default:_((()=>l[23]||(l[23]=[v("保存")]))),_:1,__:[23]})])])),_:1},8,["modelValue"]),h(se,{modelValue:J.value,"onUpdate:modelValue":l[17]||(l[17]=e=>J.value=e),ref:"userDialog",title:Q.value,draggable:"",width:"840px","close-on-press-escape":!1,"close-on-click-modal":!1},{footer:_((()=>[g("span",B,[h(t,{onClick:l[15]||(l[15]=e=>J.value=!1),icon:"Close"},{default:_((()=>l[26]||(l[26]=[v("关闭")]))),_:1,__:[26]}),h(t,{type:"success",onClick:l[16]||(l[16]=e=>{""!==A.value.uid&&null!==A.value.uid&&void 0!==A.value.uid?""!==A.value.user_type_id&&null!==A.value.user_type_id&&void 0!==A.value.user_type_id?""!==A.value.name&&null!==A.value.name&&void 0!==A.value.name?a(A.value).then((e=>{e.data>0?(J.value=!1,x({message:"保存成功！",type:"success"}),Y()):x({message:"保存失败！"+e.msg,type:"error"})})):x({message:"名称不能为空",type:"info"}):x({message:"用户类型不能为空",type:"info"}):x({message:"登录名(UID)不能为空",type:"info"})}),icon:"CircleCheck"},{default:_((()=>l[27]||(l[27]=[v(" 保存 ")]))),_:1,__:[27]})])])),default:_((()=>[g("div",L,[h(ge,{model:A.value,inline:!0,"label-width":"100px",rules:W.value},{default:_((()=>[h(pe,{label:"用户类型",prop:"user_type_id"},{default:_((()=>[h(r,{modelValue:A.value.user_type_id,"onUpdate:modelValue":l[5]||(l[5]=e=>A.value.user_type_id=e),clearable:"",filterable:"",style:{width:"250px"},placeholder:"用户类型",onChange:Y},{default:_((()=>[(m(!0),c(f,null,y(N.value,(e=>(m(),b(u,{key:e.id,label:e.title,value:e.id,style:{width:"250px"}},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),h(pe,{label:"是否启用",prop:"status"},{default:_((()=>[h(me,{modelValue:A.value.status,"onUpdate:modelValue":l[6]||(l[6]=e=>A.value.status=e),style:{width:"235px"}},{default:_((()=>[h(ce,{label:1},{default:_((()=>l[24]||(l[24]=[v("启用")]))),_:1,__:[24]}),h(ce,{label:0},{default:_((()=>l[25]||(l[25]=[v("禁用")]))),_:1,__:[25]})])),_:1},8,["modelValue"])])),_:1}),h(pe,{label:"登录名(UID)",prop:"uid"},{default:_((()=>[h(i,{modelValue:A.value.uid,"onUpdate:modelValue":l[7]||(l[7]=e=>A.value.uid=e),placeholder:"登录名(UID)",style:{width:"250px"}},null,8,["modelValue"])])),_:1}),h(pe,{label:"密码",prop:"pwd"},{default:_((()=>[h(i,{modelValue:A.value.pwd,"onUpdate:modelValue":l[8]||(l[8]=e=>A.value.pwd=e),placeholder:"密码",style:{width:"250px"}},null,8,["modelValue"])])),_:1}),h(pe,{label:"名称(姓名)",prop:"name"},{default:_((()=>[h(i,{modelValue:A.value.name,"onUpdate:modelValue":l[9]||(l[9]=e=>A.value.name=e),placeholder:"名称(姓名)",style:{width:"250px"}},null,8,["modelValue"])])),_:1}),h(pe,{label:"联系电话",prop:"mobile"},{default:_((()=>[h(i,{modelValue:A.value.mobile,"onUpdate:modelValue":l[10]||(l[10]=e=>A.value.mobile=e),placeholder:"联系电话",style:{width:"250px"}},null,8,["modelValue"])])),_:1}),h(pe,{label:"身份证号",prop:"sfzh"},{default:_((()=>[h(i,{modelValue:A.value.sfzh,"onUpdate:modelValue":l[11]||(l[11]=e=>A.value.sfzh=e),placeholder:"身份证号",style:{width:"250px"}},null,8,["modelValue"])])),_:1}),h(pe,{label:"电子邮箱",prop:"email"},{default:_((()=>[h(i,{modelValue:A.value.email,"onUpdate:modelValue":l[12]||(l[12]=e=>A.value.email=e),placeholder:"电子邮箱",style:{width:"250px"}},null,8,["modelValue"])])),_:1}),h(pe,{label:"站点编码"},{default:_((()=>[h(i,{modelValue:A.value.zd_bm,"onUpdate:modelValue":l[13]||(l[13]=e=>A.value.zd_bm=e),placeholder:"站点编码",style:{width:"250px"}},null,8,["modelValue"])])),_:1}),h(pe,{label:"站点名称"},{default:_((()=>[h(i,{modelValue:A.value.zd_mc,"onUpdate:modelValue":l[14]||(l[14]=e=>A.value.zd_mc=e),placeholder:"站点名称",style:{width:"250px"}},null,8,["modelValue"])])),_:1})])),_:1},8,["model","rules"])])])),_:1},8,["modelValue","title"])])}}});export{G as default};
