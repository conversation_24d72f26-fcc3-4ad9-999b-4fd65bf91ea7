import{I as t,J as n}from"./index-BERmxY3Y.js";async function s(s){return await t.get("kcmgr/getKcNodeList?"+n.stringify(s))}async function r(n){return await t.post("kcmgr/saveKcNodeData",n)}async function a(n){return await t.post("kcmgr/updateKcNodeStatus",n)}async function o(){return t.post("kcmgr/getCourseJobbank",{})}async function e(s){return t.post("kcmgr/getCoursePlate?"+n.stringify(s))}async function i(s){return t.post("kcmgr/getJobbankSourse?"+n.stringify(s))}async function c(n){return t.post("kcmgr/SaveCourseJobbank",n)}async function u(s){return t.post("kcmgr/deleteCourseJobbank?"+n.stringify(s))}async function g(n){return t.post("kcmgr/getVideoList",n)}async function f(n){return t.post("kcmgr/insertVideoList",n)}async function p(){return t.post("mgr_course/infojclist")}function y(s){return t.post("mgr_ppt/GetVideoPPTInfoData?"+n.stringify(s))}async function m(n){return t.post("mgr_ppt/SaveVideoPPTInfoData",n)}async function k(n){return t.post("/file/upload_to_drive",n)}async function _(n){return t.post("mgr_ppt/SetVideoPPTFileList",n)}function d(s){return t.post("mgr_ppt/Extract_PPT_content?"+n.stringify(s))}async function C(){return t.post("kcmgr/getCourseBook",{})}async function w(s){return t.post("kcmgr/getBook?"+n.stringify(s))}function b(s){return t.post("mgr_ppt/GetVideoPPTFileImgsList?"+n.stringify(s))}async function v(n){return await t.post("kcmgr/sync_video_course",n)}async function P(n){return await t.post("kcmgr/getCourseChapterResource",n)}async function l(s){return await t.post("kcmgr/getCourseChapterVideos?"+n.stringify(s))}async function J(n){return await t.post("kcmgr/saveCourseResource",n)}async function S(s){return await t.post("kcmgr/getCourseChapterDzs?"+n.stringify(s))}async function V(s){return await t.post("kcmgr/getCourseChapterDzs_s?"+n.stringify(s))}async function h(s){return await t.post("kcmgr/getCourseChapterJobs?"+n.stringify(s))}async function D(s){return await t.post("kcmgr/getCourseJc?"+n.stringify(s))}async function L(s){return await t.post("kcmgr/getCourseKsdg?"+n.stringify(s))}async function T(s){return await t.post("kcmgr/getCourseJobSource?"+n.stringify(s))}async function x(n){return await t.post("vcation_mgr/get_verification_config_all",n)}async function I(n){return await t.post("vcation_mgr/update_verification_config",n)}export{x as A,I as B,d as E,y as G,c as S,e as a,w as b,l as c,u as d,S as e,V as f,C as g,L as h,D as i,h as j,T as k,P as l,o as m,i as n,s as o,r as p,g as q,v as r,J as s,p as t,a as u,f as v,m as w,k as x,_ as y,b as z};
