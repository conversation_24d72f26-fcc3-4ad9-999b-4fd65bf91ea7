import{I as t,J as n}from"./index-BERmxY3Y.js";function a(a){return t.post("mgr_setting/getModuleData?"+n.stringify(a))}async function s(n){return await t.post("mgr_setting/updateModuleData",n)}async function e(a){return await t.post("/mgr_setting/delModuleData?"+n.stringify(a))}async function i(a){return await t.post("mgr_setting/getRoleData?"+n.stringify(a))}async function r(n){return await t.post("/mgr_setting/updateRoleData",n)}async function o(a){return await t.post("/mgr_setting/delRoleData?"+n.stringify(a))}async function g(a){return await t.post("/mgr_setting/getUserBetweenRole?"+n.stringify(a))}async function u(a){return await t.post("/mgr_setting/setUserOfRole?"+n.stringify(a))}async function f(a){return await t.post("mgr_setting/getRoleAuthInfo?"+n.stringify(a))}async function c(n){return await t.post("mgr_setting/setRoleAuth",n)}async function y(a){return await t.post("mgr_setting/getUserInfoList?"+n.stringify(a))}async function p(a){return await t.post("/mgr_setting/setRoleOfUserData?"+n.stringify(a))}async function m(n){return await t.post("/mgr_setting/saveUserInfoFromData",n)}async function w(a){return await t.post("/mgr_setting/delUserInfoData?"+n.stringify(a))}async function _(a){return await t.post("mgr_setting/getUserTypeData?"+n.stringify(a))}async function l(n){return await t.post("mgr_setting/updateUserTypeData",n)}export{e as D,i as G,c as S,f as a,a as b,s as c,r as d,o as e,g as f,u as g,_ as h,y as i,m as j,w as k,p as l,l as m};
