var e,t,r,n=Object.defineProperty,o=e=>{throw TypeError(e)},a=(e,t,r)=>((e,t,r)=>t in e?n(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r)(e,"symbol"!=typeof t?t+"":t,r),s=(e,t,r)=>(((e,t,r)=>{t.has(e)||o("Cannot "+r)})(e,t,"read from private field"),r?r.call(e):t.get(e)),i=(e,t,r)=>t.has(e)?o("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,r);let l;e=new WeakMap,t=new WeakMap,r=new WeakMap;const d=new class{constructor(){a(this,"timeSeconds2Str",(e=>{const t=Math.floor(e/3600),r=Math.floor(e%3600/60),n=e%60,o=String(t).padStart(2,"0"),a=String(r).padStart(2,"0"),s=String(n).padStart(2,"0");return"00"==o?`${a}:${s}`:`${o}:${a}:${s}`})),a(this,"timeStr2Seconds",(e=>{let[t,r,n,o]=e.split(/[:.,]/).map(Number);return 3600*t+60*r+n+Math.ceil(o/1e3)})),a(this,"scrollToElement",((e,t)=>{e.scrollIntoView({behavior:t||"smooth",block:"start",inline:"nearest"})})),a(this,"getImg",((e,t)=>{let r=new FileReader;r.onload=function(t){let r=new Image;r.src=t.target.result,r.onload=function(){e(r)}},r.readAsDataURL(t)})),a(this,"getBrowerData",(()=>{const e=navigator.userAgent,t=e.indexOf("Chrome/"),r=e.substring(t+7,e.length),n=r.indexOf("."),o=r.substring(0,n);return e.includes("Chrome")?{is_chrome:!0,version:parseInt(o)}:{is_chrome:!1,version:parseInt(o)}})),a(this,"getMinutesBySeconds",(e=>0===e?0:Math.ceil(e/60))),a(this,"isExpired",(e=>{if(e){return new Date(e)<new Date}return!0})),a(this,"getCurrTime",(()=>{let e=new Date;return`${e.getFullYear()}-${String(e.getMonth()+1).padStart(2,"0")}-${String(e.getDate()).padStart(2,"0")} ${String(e.getHours()).padStart(2,"0")}:${String(e.getMinutes()).padStart(2,"0")}:${String(e.getSeconds()).padStart(2,"0")}`})),a(this,"getExpiredTime",(()=>{const e=new Date((new Date).getTime()+6048e5);return`${e.getFullYear()}-${String(e.getMonth()+1).padStart(2,"0")}-${String(e.getDate()).padStart(2,"0")} ${String(e.getHours()).padStart(2,"0")}:${String(e.getMinutes()).padStart(2,"0")}:${String(e.getSeconds()).padStart(2,"0")}`})),a(this,"openUrl",((e,t="_blank")=>{let r=document.createElement("a");e.includes("?")?r.setAttribute("href",`${e}&r=${(new Date).getTime()}`):r.setAttribute("href",`${e}?r=${(new Date).getTime()}`),r.setAttribute("style","display:none"),r.setAttribute("target",t),document.body.appendChild(r),r.click(),r.parentNode.removeChild(r)})),a(this,"trimSpace",(e=>e.replace(/\s+/g,""))),a(this,"replaceSpace",(e=>e.replace(/\s+/g,"+"))),a(this,"replaceBr2Newline",(e=>e.replace(/<br\s*\/?>/gi,"\n"))),a(this,"removeAllEscapes",(e=>e.replace(/\\\\/gi,"\\"))),a(this,"format2Md",(e=>this.replaceBr2Newline(this.removeAllEscapes(e)))),i(this,e,(e=>{try{e.preventDefault()}catch(t){}})),i(this,t,(e=>{const{ctrlKey:t,key:r}=e;t&&["61","107","173","109","187","189","+","-","p"].includes(r)&&e.preventDefault()})),a(this,"disabledCtr",((r=!0)=>{document.removeEventListener("selectstart",s(l,e)),document.removeEventListener("contextmenu",s(l,e)),document.removeEventListener("keydown",s(l,t)),document.removeEventListener("mousewheel",s(l,e)),document.removeEventListener("DOMMouseScroll",s(l,e)),document.removeEventListener("dragover",s(l,e)),r||(document.removeEventListener("mousewheel",s(l,e)),document.removeEventListener("DOMMouseScroll",s(l,e))),document.addEventListener("contextmenu",s(l,e)),document.addEventListener("keydown",s(l,t)),document.addEventListener("dragover",s(l,e)),r||(document.addEventListener("mousewheel",s(l,e),{passive:!1}),document.addEventListener("DOMMouseScroll",s(l,e),{passive:!1}))})),a(this,"HexToBase64",(e=>{for(var t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",r="",n=0,o=0,a=0;a<e.length;++a)n=n<<4|(e[a]>="A"&&e[a]<="Z"?e.charCodeAt(a)-55:e[a]>="a"&&e[a]<="z"?e.charCodeAt(a)-87:e.charCodeAt(a)-48),(o+=4)>=6&&(r+=t[n>>>(o-=6)],n&=~(-1<<o));o>0&&(r+=t[n<<=6-o]);var s=r.length%4;if(s>0)for(a=0;a<4-s;++a)r+="=";return r})),a(this,"isMobile",(()=>/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent))),a(this,"downloadFileByUrlName",((e,t)=>{let r,n=new XMLHttpRequest;n.addEventListener("readystatechange",(function(e){if(4==n.readyState){r=URL.createObjectURL(n.response);let e=document.createElement("a");e.setAttribute("href",r),e.setAttribute("download",t),e.click()}})),n.responseType="blob",n.open("get",`${e}?${(new Date).getTime()}`),n.send()})),a(this,"getGreeting",(()=>{const e=(new Date).getHours();return e>=5&&e<12?"上午好。":e>=12&&e<14?"中午好。":e>=14&&e<18?"下午好。":"晚上好。"})),a(this,"hasZmAndNumber",(e=>/^(?=.*[a-zA-Z])(?=.*\d).+$/.test(e))),a(this,"showMaskFull",(()=>{let e=document.querySelector(".ref_mask_full");e&&(e.style.display="block")})),a(this,"hideMaskFull",(()=>{let e=document.querySelector(".ref_mask_full");e&&(e.style.display="none")})),a(this,"delIndexedDB",(async e=>new Promise(((t,r)=>{const n=indexedDB.deleteDatabase(e),o={msg:"",code:""};n.onsuccess=()=>{o.msg="本站点浏览器数据删除成功",o.code="200",t(o)},n.onerror=()=>{o.msg="本站点浏览器数据删除失败",o.code="500",r(o)},n.onblocked=()=>{o.msg="本站点浏览器数据删除操作被阻止，可能有其他连接未关闭，请关闭浏览器后重试",o.code="204",r(o)}})))),i(this,r,(e=>{let t=e;for(;t&&t!==document.body;){const{overflowY:e}=window.getComputedStyle(t);if("auto"===e||"scroll"===e)return t;t=t.parentElement}return window})),a(this,"highlightMatch",(e=>{const t=e.style.backgroundColor;e.style.backgroundColor="#ffff99",e.style.transition="background-color 0.3s",setTimeout((()=>{e.style.backgroundColor=t,setTimeout((()=>{e.style.backgroundColor="#ffff99",setTimeout((()=>e.style.backgroundColor=t),300)}),300)}),1e3)})),a(this,"scrollToFirstMatch",(e=>{const t=document.createTreeWalker(document.body,NodeFilter.SHOW_TEXT,{acceptNode:e=>{var t;return["SCRIPT","STYLE"].includes(null==(t=e.parentElement)?void 0:t.tagName)?NodeFilter.FILTER_SKIP:NodeFilter.FILTER_ACCEPT}});let n=null;for(;t.nextNode();){const r=t.currentNode.textContent;if(r&&r.includes(e)){n=t.currentNode;break}}if(n){const e=s(this,r).call(this,n.parentElement),t=n?n.parentElement.getBoundingClientRect():null;return e.scrollTo({top:t.top+e.scrollTop-100,behavior:"smooth"}),this.highlightMatch(n.parentElement),!0}return!1})),l=this,l.timer=null}getRandomRange(e,t){return Math.floor(Math.random()*(e-t)+t)}getCurrTimeWithNumStr(){let e=new Date,t=e.getFullYear(),r=e.getMonth()+1<10?"0"+(e.getMonth()+1):e.getMonth()+1,n=e.getDate()<10?"0"+e.getDate():e.getDate(),o=e.getHours()<10?"0"+e.getHours():e.getHours(),a=e.getMinutes()<10?"0"+e.getMinutes():e.getMinutes(),s=e.getSeconds()<10?"0"+e.getSeconds():e.getSeconds(),i=e.getMilliseconds()<1e3?"000"+e.getMilliseconds():e.getMilliseconds();return t.toString()+r.toString()+n.toString()+o.toString()+a.toString()+s+i.toString()}generateMarkdownToc(e){const t=e.querySelectorAll("h1, h2, h3"),r=Array.from(t).filter((e=>{var t;return null==(t=e.textContent)?void 0:t.trim()})).map((e=>({type:e.tagName.toLowerCase(),title:e.innerHTML,lineIndex:parseInt(e.getAttribute("data-v-md-line")||"0"),level:parseInt(e.tagName.charAt(1)),element:e})));if(0===r.length)return[];const n={level:0,children:[]},o={0:n};return r.forEach((e=>{let t=e.level-1;for(;t>0&&!o[t];)t--;const r=o[t]||n;r.children||(r.children=[]);const{element:a,...s}=e,i={...s,children:[]};r.children.push(i),o[e.level]=i})),n.children}};export{d as t};
