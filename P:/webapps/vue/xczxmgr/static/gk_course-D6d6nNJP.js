import{I as e,J as a,d as l,r as t,o as s,E as o,a as u,L as d,c as r,b as i,j as c,M as n,w as _,p as m,e as p,h as g,F as v,f,n as b,v as h}from"./index-BERmxY3Y.js";const k={class:"gk_course",style:{padding:"5px"}},y={style:{width:"300px",margin:"0 auto"}},w={class:"dialog-footer"},V=l({__name:"gk_course",setup(l){const V=t([]),x=t(!1),C=t("right"),L=t(!1),U=t([]),z=window.innerHeight-200,B=t({id:0,course_base_id:"",kc_bm:"",kc_mc:"",status:"1"});s((()=>{M()}));const M=()=>{var l;x.value=!0,(l={},e.post("mgr_gk/getGkCourse?"+a.stringify(l))).then((e=>{200===e.code?V.value=e.data:o({message:e.msg,type:"error"})})).catch((e=>{o({message:e.message,type:"error"})})).finally((()=>{x.value=!1}))},j=()=>{e.post("mgr_gk/getCourseBase").then((e=>{200===e.code?U.value=e.data:o({message:e.msg,type:"error"})})).catch((e=>{o({message:e.message,type:"error"})}))},G=()=>{var e=U.value.find((e=>e.id==B.value.course_base_id));B.value.kc_bm=e.kc_bm,B.value.kc_mc=e.kc_mc},H=()=>{var a;(a=B.value,e.post("mgr_gk/gkSaveCourse",a)).then((e=>{200===e.code?(o({message:e.msg,type:"success"}),L.value=!1,M()):o({message:e.msg,type:"error"})})).catch((e=>{o({message:e.message,type:"error"})}))},S=l=>{h.confirm("此操作将永久删除该课程, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{var t;(t={id:l.id},e.post("mgr_gk/deleteGkCourse?"+a.stringify(t))).then((e=>{200==e.code?(o({message:e.msg,type:"success"}),M()):o({message:e.msg,type:"error"})})).catch((e=>{o({message:e.message,type:"error"})}))})).catch((()=>{o({type:"info",message:"已取消删除"})}))};return(e,a)=>{const l=u("el-button"),t=u("el-table-column"),s=u("el-tag"),o=u("el-table"),h=u("el-option"),T=u("el-select"),E=u("el-form-item"),F=u("el-input"),I=u("el-radio"),J=u("el-radio-group"),q=u("el-form"),A=u("el-dialog"),D=d("loading");return i(),r("div",k,[c(l,{type:"primary",onClick:M},{default:_((()=>a[7]||(a[7]=[m("刷新")]))),_:1,__:[7]}),c(l,{type:"success",onClick:a[0]||(a[0]=e=>(L.value=!0,j(),void b((()=>{B.value={id:0,course_base_id:"",kc_bm:"",kc_mc:"",status:"1"}}))))},{default:_((()=>a[8]||(a[8]=[p("svg",{viewBox:"0 0 1024 1024",width:"18",height:"18"},[p("path",{d:"M687.496192 742.724608l0 152.704-101.801984 0L585.694208 742.724608 432.991232 742.724608l0-101.801984 152.704 0L585.695232 488.219648l101.801984 0 0 152.702976 152.701952 0 0 101.801984L687.496192 742.724608zM687.496192 182.813696 229.387264 182.813696l0 559.910912 152.702976 0L382.09024 844.526592l-254.50496 0L127.58528 81.011712l661.71392 0 0 356.306944L687.496192 437.318656 687.496192 182.813696z","p-id":"7133",fill:"#ffffff"})],-1),m(" 添加 ")]))),_:1,__:[8]}),n((i(),g(o,{data:V.value,Height:z,stripe:"",style:{width:"100%","margin-top":"5px"}},{default:_((()=>[c(t,{type:"index",label:"#",align:"center",width:"60"}),c(t,{prop:"kc_bm",label:"课程编码",width:"180",align:"center"}),c(t,{prop:"kc_mc",label:"课程名称",width:"180",align:"center"}),c(t,{prop:"chapter_num",width:"100",label:"状态",align:"center"},{default:_((e=>[1===e.row.status?(i(),g(s,{key:0,type:"success"},{default:_((()=>a[9]||(a[9]=[m("启用")]))),_:1,__:[9]})):(i(),g(s,{key:1,type:"danger"},{default:_((()=>a[10]||(a[10]=[m("禁用")]))),_:1,__:[10]}))])),_:1}),c(t,{prop:"chapter_num",label:"",align:"center"},{default:_((e=>[c(l,{type:"primary",size:"small",onClick:a=>{return l=e.row,L.value=!0,j(),l.status=l.status.toString(),void(B.value=l);var l}},{default:_((()=>a[11]||(a[11]=[m("编辑")]))),_:2,__:[11]},1032,["onClick"]),c(l,{type:"danger",size:"small",onClick:a=>S(e.row)},{default:_((()=>a[12]||(a[12]=[m("删除")]))),_:2,__:[12]},1032,["onClick"])])),_:1})])),_:1},8,["data"])),[[D,x.value]]),c(A,{modelValue:L.value,"onUpdate:modelValue":a[6]||(a[6]=e=>L.value=e),title:"编辑数据",width:"500"},{footer:_((()=>[p("span",w,[c(l,{onClick:a[5]||(a[5]=e=>L.value=!1)},{default:_((()=>a[16]||(a[16]=[m("取消")]))),_:1,__:[16]}),c(l,{type:"primary",onClick:H},{default:_((()=>a[17]||(a[17]=[m(" 保存 ")]))),_:1,__:[17]})])])),default:_((()=>[p("div",y,[c(q,{inline:!0,model:B.value,"label-position":C.value,"label-width":"80px"},{default:_((()=>[c(E,{label:"选择课程"},{default:_((()=>[c(T,{modelValue:B.value.course_base_id,"onUpdate:modelValue":a[1]||(a[1]=e=>B.value.course_base_id=e),class:"m-2",placeholder:"请选择课程",style:{width:"200px"},onChange:G},{default:_((()=>[(i(!0),r(v,null,f(U.value,(e=>(i(),g(h,{key:e.id,label:e.kc_mc,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),a[15]||(a[15]=p("br",null,null,-1)),c(E,{label:"课程编码"},{default:_((()=>[c(F,{modelValue:B.value.kc_bm,"onUpdate:modelValue":a[2]||(a[2]=e=>B.value.kc_bm=e),disabled:"",placeholder:"请选择",style:{width:"200px"}},null,8,["modelValue"])])),_:1}),c(E,{label:"课程名称"},{default:_((()=>[c(F,{modelValue:B.value.kc_mc,"onUpdate:modelValue":a[3]||(a[3]=e=>B.value.kc_mc=e),disabled:"",placeholder:"请选择",style:{width:"200px"}},null,8,["modelValue"])])),_:1}),c(E,{label:"状态"},{default:_((()=>[c(J,{modelValue:B.value.status,"onUpdate:modelValue":a[4]||(a[4]=e=>B.value.status=e)},{default:_((()=>[c(I,{label:"1",border:""},{default:_((()=>a[13]||(a[13]=[m("启用")]))),_:1,__:[13]}),c(I,{label:"0",border:""},{default:_((()=>a[14]||(a[14]=[m("禁用")]))),_:1,__:[14]})])),_:1},8,["modelValue"])])),_:1})])),_:1,__:[15]},8,["model","label-position"])])])),_:1},8,["modelValue"])])}}});export{V as default};
