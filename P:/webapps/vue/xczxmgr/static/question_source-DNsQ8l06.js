import{G as e,f as l}from"./question-DWIQ7W1h.js";import{d as a,K as t,r as i,o,a as n,L as d,c as s,b as u,e as r,j as c,w as p,p as m,M as h,h as g,t as v,E as f}from"./index-BERmxY3Y.js";const b={class:"page"},y={class:"header"},_={class:"body"},k={style:{width:"100%","text-align":"center"}},w={class:"dialog-footer"},C=a({__name:"question_source",setup(a){const C=t({loading:!1,tableHeight:window.innerHeight-180,keySearch:"",dataTable:[]}),x=i(!1),V=i(""),S=i([]),z=i({}),T=()=>{C.loading=!0;const l={key:C.keySearch};e(l).then((e=>{C.dataTable=e.data,C.loading=!1})).catch((e=>{C.loading=!1}))},j=e=>{S.value=e};return o((()=>{T()})),(e,a)=>{const t=n("el-button"),i=n("el-input"),o=n("el-table-column"),H=n("el-link"),U=n("el-table"),q=n("el-form-item"),D=n("el-form"),E=n("el-dialog"),G=d("loading");return u(),s("div",b,[r("div",y,[c(t,{type:"success",plain:"",size:"default",icon:"refresh",round:"",onClick:T}),c(i,{modelValue:C.keySearch,"onUpdate:modelValue":a[0]||(a[0]=e=>C.keySearch=e),placeholder:"名称",size:"default",style:{width:"260px",margin:"0 10px"},clearable:"",onChange:T},{append:p((()=>[c(t,{icon:"Search",onClick:T})])),_:1},8,["modelValue"]),c(t,{type:"primary",plain:"",size:"default",round:"",icon:"CirclePlus",onClick:a[1]||(a[1]=e=>(z.value={id:0,name:"",title:"",remark:""},V.value="新增用户类型",void(x.value=!0)))},{default:p((()=>a[7]||(a[7]=[m("新增")]))),_:1,__:[7]}),c(t,{type:"success",plain:"",size:"default",round:"",icon:"edit",onClick:a[2]||(a[2]=e=>(()=>{if(null==S.value||1!==S.value.length)return f({message:"请勾选一条数据进行编辑！",type:"error"}),!1;V.value="编辑【"+S.value[0].title+"】",x.value=!0,z.value=S.value[0]})())},{default:p((()=>a[8]||(a[8]=[m("编辑")]))),_:1,__:[8]})]),r("div",_,[h((u(),g(U,{ref:"multipleRolesTable",class:"tb-edit",data:C.dataTable,border:"",height:C.tableHeight,size:"mini",onSelectionChange:j},{default:p((()=>[c(o,{type:"selection",width:"60",align:"center"}),c(o,{prop:"",type:"index",label:"序号",align:"center",width:"60","header-align":"center"}),c(o,{prop:"id",label:"编码","min-width":"120",align:"center",sortable:""}),c(o,{prop:"title",label:"名称","min-width":"120",align:"center",sortable:""}),c(o,{prop:"num",label:"总题量","min-width":"120",align:"center",sortable:""},{default:p((e=>[c(H,{type:"primary",underline:!1},{default:p((()=>[m(v(e.row.num),1)])),_:2},1024)])),_:1})])),_:1},8,["data","height"])),[[G,C.loading]])]),c(E,{modelValue:x.value,"onUpdate:modelValue":a[6]||(a[6]=e=>x.value=e),ref:"kdroomDialog",title:V.value,draggable:"",width:"600px","close-on-press-escape":!1,"close-on-click-modal":!1},{footer:p((()=>[r("span",w,[c(t,{onClick:a[4]||(a[4]=e=>x.value=!1),icon:"Close"},{default:p((()=>a[9]||(a[9]=[m("关闭")]))),_:1,__:[9]}),c(t,{type:"success",onClick:a[5]||(a[5]=e=>{""!==z.value.title&&null!==z.value.title&&void 0!==z.value.title?l(z.value).then((e=>{e.data>0?(x.value=!1,f({message:"保存成功！",type:"success"}),T()):f({message:"保存失败！"+e.msg,type:"error"})})):f({message:"名称不能为空",type:"info"})}),icon:"CircleCheck"},{default:p((()=>a[10]||(a[10]=[m(" 保存 ")]))),_:1,__:[10]})])])),default:p((()=>[r("div",k,[c(D,{model:z.value,inline:!0,"label-width":"100px"},{default:p((()=>[c(q,{label:"试题来源名称"},{default:p((()=>[c(i,{modelValue:z.value.title,"onUpdate:modelValue":a[3]||(a[3]=e=>z.value.title=e),placeholder:"试题来源名称",style:{width:"400px"}},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])])])),_:1},8,["modelValue","title"])])}}});export{C as default};
