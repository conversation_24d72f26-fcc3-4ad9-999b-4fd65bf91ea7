import{d as e,c as l,m as t,b as a,e as n,F as s,f as i,_ as u,r as o,o as c,x as d,a as v,y as p,h as r,j as _,w as h,p as g,z as f,t as x,A as m,l as y,B as w,q as k,C as b,i as z,D as C,G as S,H as V,n as I}from"./index-BERmxY3Y.js";import{m as M}from"./message-Df6PNwXY.js";import{t as T}from"./tools-CvIkJR06.js";import{C as A}from"./dzs-DZN_gNG7.js";const $={key:0,class:"page_loading animate-pulse"},j={class:"page_loading_left"},U={src:"/vue/xczxmgr/static/book-DujjYxMQ.png",alt:""},R={class:"page_loading_right"},q={src:"/vue/xczxmgr/static/paragraph-DcYzuUrE.png",alt:""},D=u(e({__name:"img_2_paragraph",props:{is_loading:{type:Boolean}},setup:e=>(e,u)=>e.is_loading?(a(),l("div",$,[n("div",j,[(a(),l(s,null,i(5,(e=>n("img",U))),64))]),n("div",R,[(a(),l(s,null,i(5,(e=>n("img",q))),64))])])):t("",!0)}),[["__scopeId","data-v-81100e30"]]),H={class:"voice-control-content"},L={class:"voice-select"},N={class:"voice-controls"},Y=u(e({__name:"TextSpeech",props:{content:{}},setup(e){const u=o(null),m=o(!1),y=o({top:0,left:0}),w=o(null);o(null);const k=o(!1),b=o(0),z=o([]),C=o(!1),S=o(!1),V=o(""),I=o(-1),M=o([]),T=o(null),A=o(""),$=o([]),j=()=>{const e=window.speechSynthesis.getVoices();return z.value=e.filter((e=>"zh-CN"===e.lang)),z.value[0]};c((()=>{void 0!==speechSynthesis.onvoiceschanged&&(speechSynthesis.onvoiceschanged=()=>{const e=j();e&&(w.value=e)});const e=j();e&&(w.value=e)})),d((()=>{E()}));const U=e=>{const l=window.getSelection(),t=(null==l?void 0:l.toString().trim())||"";if(t.length>0){V.value=t,y.value={top:e.clientY+window.scrollY+10,left:e.clientX+window.scrollX+10},m.value=!0;const a=l.getRangeAt(0);if(T.value=a.commonAncestorContainer.parentElement,!T.value)return;A.value=T.value.innerHTML;const n=T.value.innerText,s=n.indexOf(t);if(-1===s)return;const i=(e=>{const l=e.split(/([。！？.!?])/).filter((e=>e.trim().length>0)),t=[];for(let a=0;a<l.length;a+=2)a+1<l.length?t.push(l[a]+l[a+1]):t.push(l[a]);return t})(t);$.value=[];let u=s;for(const e of i){const l=n.indexOf(e,u);-1!==l&&($.value.push({start:l,end:l+e.length,text:e}),u=l+e.length)}M.value=i,k.value=!0,z.value.length>0?B(0):speechSynthesis.onvoiceschanged=()=>{z.value=window.speechSynthesis.getVoices().filter((e=>"zh-CN"===e.lang)),z.value.length>0&&B(0)}}else m.value=!1},R=()=>{T.value&&A.value&&(T.value.innerHTML=A.value)},q=()=>{k.value=!0,V.value&&M.value.length>0&&(z.value.length>0?B(0):speechSynthesis.onvoiceschanged=()=>{z.value=window.speechSynthesis.getVoices().filter((e=>"zh-CN"===e.lang)),z.value.length>0&&B(0)})},D=()=>{E();const e=I.value>=0?I.value:0;M.value.length>0&&B(e)},Y=()=>{S.value?(window.speechSynthesis.resume(),S.value=!1):(window.speechSynthesis.pause(),S.value=!0)},E=()=>{window.speechSynthesis.cancel(),C.value=!1,S.value=!1,I.value=-1,R()},B=e=>{if(e<0||e>=M.value.length)return;I.value=e;const l=M.value[e],t=z.value[b.value];if(!t)return void alert("未找到可用中文语音");(e=>{if(!T.value||!A.value)return;R();const l=$.value[e];if(!l)return;const t=l.text.replace(/[.*+?^${}()|[\\]\\\\]/g,"\\$&"),a=new RegExp(t),n=T.value.innerHTML.replace(a,`<span class="reading-highlight">${l.text}</span>`);T.value.innerHTML=n})(e),window.speechSynthesis.cancel();const a=new SpeechSynthesisUtterance(l);a.voice=t,a.lang=t.lang,a.onstart=()=>{},a.onend=()=>{I.value<M.value.length-1?B(I.value+1):(C.value=!1,S.value=!1,k.value=!1,I.value=-1,R())},window.speechSynthesis.speak(a),C.value=!0,S.value=!1};return(e,o)=>{const c=v("el-button"),d=v("el-option"),w=v("el-select"),V=v("el-dialog");return a(),l("div",{ref_key:"containerRef",ref:u,onMouseup:U},[p(e.$slots,"default",{},void 0,!0),m.value?(a(),r(c,{key:0,style:f({position:"absolute",top:`${y.value.top}px`,left:`${y.value.left}px`,zIndex:1e3}),class:"px-3 py-1 bg-blue-600 text-white rounded shadow",round:"",onClick:q},{default:h((()=>o[2]||(o[2]=[g(" 朗读文字 ")]))),_:1,__:[2]},8,["style"])):t("",!0),_(V,{modelValue:k.value,"onUpdate:modelValue":o[1]||(o[1]=e=>k.value=e),style:f({position:"absolute",top:`${y.value.top+40}px`,left:`${y.value.left}px`,margin:0,padding:"10px"}),width:"300px","show-close":!1,modal:!1,class:"voice-control-panel"},{default:h((()=>[n("div",H,[n("div",L,[o[3]||(o[3]=n("span",null,"选择语音：",-1)),_(w,{modelValue:b.value,"onUpdate:modelValue":o[0]||(o[0]=e=>b.value=e),onChange:D},{default:h((()=>[(a(!0),l(s,null,i(z.value,((e,l)=>(a(),r(d,{key:l,label:e.name,value:l},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])]),n("div",N,[_(c,{onClick:Y,disabled:!C.value},{default:h((()=>[g(x(S.value?"继续":"暂停"),1)])),_:1},8,["disabled"]),_(c,{onClick:E,disabled:!C.value},{default:h((()=>o[4]||(o[4]=[g("停止")]))),_:1,__:[4]},8,["disabled"])])])])),_:1},8,["modelValue","style"])],544)}}}),[["__scopeId","data-v-fdc6d396"]]),E={key:1},B={key:4,class:"page_cg_kc_dzs_header"},F={class:""},K={style:{"letter-spacing":"5px","font-size":"22px",color:"#00008b","font-weight":"bold","text-shadow":"0 4px 4px rgba(0, 0, 0, 0.25)"}},O={class:"info",style:{"font-size":"14px"}},X={class:"info",style:{"font-size":"14px"}},G={class:"d_flex_e"},J={key:0},Q={key:0},P={key:5,class:"menu_ls"},W={class:"w100 d_flex_s flex_w gap_25"},Z={class:"w100 d_flex_s flex_w_un gap_10"},ee={class:"c_page_menu",style:{display:"flex","align-items":"center"}},le=["title","onClick"],te={key:0,style:{"margin-top":"15px","padding-left":"15px"},class:"w100 d_flex_s flex_w gap_15"},ae={class:"w100 d_flex_s flex_w_un gap_10"},ne={class:"c_page_menu",style:{display:"flex","align-items":"center"}},se=["title","onClick"],ie={key:0,style:{"margin-top":"15px","padding-left":"15px"},class:"w100 d_flex_s flex_w gap_10"},ue={class:"w100 d_flex_s flex_w_un gap_5 info"},oe={class:"c_page_menu",style:{display:"flex","align-items":"center"}},ce=["title","onClick"],de={key:6,class:"point_ls"},ve=u(e({__name:"dzs_info",setup(e){const u=o({id:"",key_menu:"",key_point:"",kc_mc:"课程名称xxx",kc_bm:"",num_chapter:0,num_point:0}),p=o({}),f=o([]),$=o([]),j=o([]),U=o([]),R=o(!0),q=o({ext:!0,type:"menu"}),H=o(null);o();const L=o(""),N=o("");m(q,(()=>{H.value&&(q.value.ext?H.value.style.width=(q.value.type,"calc(100% - 420px)"):H.value.style.width="100%")}),{throttle:500,deep:!0}),c((()=>{const e=y().query.id;u.value.id=e,pe()})),d((()=>{}));const ve=()=>{setTimeout((()=>{R.value=!1,setTimeout((()=>{he()}),500)}),360)},pe=()=>{re()},re=()=>{R.value=!0,p.value={};var e={id:u.value.id};A(e).then((e=>{const{code:l,msg:t,data:a}=e;"200"===l.toString()?(p.value=a,u.value.kc_bm=p.value.kc_bm,u.value.kc_mc=p.value.kc_mc,$.value=p.value.zsd,U.value=$.value,f.value=p.value.menu,j.value=f.value,ve()):(ve(),M(t,"warning"))})).catch((e=>{ve(),M("获取电子书信息出错啦。","error")}))},_e=()=>{q.value.ext=!q.value.ext},he=async()=>{await I();let e=document.querySelector(".doc_v_md_content");e&&(f.value=T.generateMarkdownToc(e),j.value=f.value)},ge=e=>{if(e){L.value=e;let l=document.querySelector(".doc_v_md_content");if(l){let t=l.querySelector(`[data-v-md-line="${e}"]`);t&&(t.scrollIntoView({behavior:"smooth"}),T.highlightMatch(t))}}else L.value="",M("抱歉，未找到定位节点","warning")},fe=(e,l)=>e.reduce(((e,t)=>{const a=t.title.includes(l),n=t.children?fe(t.children,l):[];if(a||n.length>0){const l={...t};n.length>0?l.children=n:delete l.children,e.push(l)}return e}),[]),xe=()=>{j.value=fe(f.value,u.value.key_menu)},me=()=>{U.value=$.value.filter((e=>e.includes(u.value.key_point)))};return(e,o)=>{const c=v("v-md-preview"),d=v("el-backtop"),f=v("el-icon"),m=v("el-radio-button"),y=v("el-radio-group"),I=v("el-tag"),M=v("el-tooltip"),A=v("el-input");return a(),l(s,null,[R.value?(a(),r(D,{key:0,is_loading:R.value},null,8,["is_loading"])):(a(),l("div",E,[n("div",{ref_key:"ref_page_cg_kc_dzs_content",ref:H,class:w("menu"===q.value.type?"ref_page_cg_kc_dzs_content_chapter":"ref_page_cg_kc_dzs_content_point")},[_(Y,null,{default:h((()=>[_(c,{text:p.value.content,class:"doc_v_md_content"},null,8,["text"])])),_:1})],2)])),R.value||"menu"!==q.value.type?t("",!0):(a(),r(d,{key:2,target:".ref_page_cg_kc_dzs_content_chapter",right:q.value.ext?425:5,bottom:5,"visibility-height":200},null,8,["right"])),R.value||"point"!==q.value.type?t("",!0):(a(),r(d,{key:3,target:".ref_page_cg_kc_dzs_content_point",right:q.value.ext?425:5,bottom:5,"visibility-height":200},null,8,["right"])),R.value?t("",!0):(a(),l("div",B,[n("div",F,[n("span",K,x(p.value.kc_mc),1),_(f,{class:"m_r_5"},{default:h((()=>[_(k(b))])),_:1}),n("span",O,x(p.value.plate_title),1),_(f,{class:"m_r_5"},{default:h((()=>[_(k(b))])),_:1}),n("span",X,x(p.value.course_dzs_title),1)]),n("div",G,[_(y,{modelValue:q.value.type,"onUpdate:modelValue":o[0]||(o[0]=e=>q.value.type=e),size:"small"},{default:h((()=>[_(m,{label:"menu"},{default:h((()=>[o[3]||(o[3]=g("按目录结构")),u.value.num_chapter>0?(a(),l("span",J,"（"+x(u.value.num_chapter)+"章）",1)):t("",!0)])),_:1,__:[3]}),_(m,{label:"point"},{default:h((()=>[o[4]||(o[4]=g("按知识点")),u.value.num_point>0?(a(),l("span",Q,"（"+x(u.value.num_point)+"个）",1)):t("",!0)])),_:1,__:[4]})])),_:1},8,["modelValue"]),_(M,{content:q.value.ext?"点击可关闭列表":"点击可开启列表",placement:"top"},{default:h((()=>[_(I,{class:"hand",onClick:_e,size:"large"},{default:h((()=>[_(f,null,{default:h((()=>[(a(),r(z(q.value.ext?"ArrowRight":"ArrowLeft")))])),_:1})])),_:1})])),_:1},8,["content"])])])),!R.value&&q.value.ext&&"menu"===q.value.type?(a(),l("div",P,[_(A,{style:{width:"410px",position:"fixed","z-index":"2",right:"10px",top:"60px"},size:"default",modelValue:u.value.key_menu,"onUpdate:modelValue":o[1]||(o[1]=e=>u.value.key_menu=e),clearable:"",placeholder:"可输入章名称关键字后,回车查询",onKeyup:S(xe,["enter"]),onClear:xe},{prefix:h((()=>[_(f,null,{default:h((()=>[_(k(C))])),_:1})])),_:1},8,["modelValue"]),n("div",W,[(a(!0),l(s,null,i(j.value,(e=>(a(),l("div",{key:e.title,class:"w100"},[n("div",Z,[n("div",ee,[_(f,{class:"hand",onClick:l=>e.is_ext=!e.is_ext,style:{"font-size":"18px","font-weight":"bold"}},{default:h((()=>[(a(),r(z(e.is_ext?"ArrowDown":"ArrowRight")))])),_:2},1032,["onClick"]),n("span",{title:e.title,class:w(L.value&&L.value===e.lineIndex?"warning hand flex_ellipsis":"hand flex_ellipsis"),style:{"font-size":"18px","font-weight":"bold"},onClick:l=>ge(e.lineIndex)},[_(c,{text:e.title},null,8,["text"])],10,le)])]),e.children&&e.children.length>0&&e.is_ext?(a(),l("div",te,[(a(!0),l(s,null,i(e.children,(e=>(a(),l("div",{key:e.title,class:"w100"},[n("div",ae,[n("div",ne,[_(f,{class:"m_r_5 hand",onClick:l=>e.is_ext=!e.is_ext,style:{"font-size":"16px","font-weight":"bold"}},{default:h((()=>[(a(),r(z(e.is_ext?"ArrowDown":"ArrowRight")))])),_:2},1032,["onClick"]),n("span",{title:e.title,class:w(L.value&&L.value===e.lineIndex?"warning hand flex_ellipsis":"hand flex_ellipsis"),style:{"font-size":"16px","font-weight":"bold"},onClick:l=>ge(e.lineIndex)},[_(c,{text:e.title},null,8,["text"])],10,se)])]),e.children&&e.children.length>0&&e.is_ext?(a(),l("div",ie,[(a(!0),l(s,null,i(e.children,(e=>(a(),l("div",{key:e.title,class:"w100 d_flex_s flex_w gap_5"},[n("div",ue,[n("div",oe,[_(f,{style:{"font-size":"14px"},class:"primary"},{default:h((()=>[_(k(V))])),_:1}),n("span",{title:e.title,class:w(L.value&&L.value===e.lineIndex?"warning hand flex_ellipsis":"hand flex_ellipsis"),style:{"font-size":"14px"},onClick:l=>ge(e.lineIndex)},[_(c,{text:e.title},null,8,["text"])],10,ce)])])])))),128))])):t("",!0)])))),128))])):t("",!0)])))),128))])])):t("",!0),!R.value&&q.value.ext&&"point"===q.value.type?(a(),l("div",de,[_(A,{style:{width:"410px",position:"fixed","z-index":"2",right:"10px",top:"60px"},size:"default",modelValue:u.value.key_point,"onUpdate:modelValue":o[2]||(o[2]=e=>u.value.key_point=e),clearable:"",placeholder:"可输入知识点关键字后,回车查询",onKeyup:S(me,["enter"]),onClear:me},{prefix:h((()=>[_(f,null,{default:h((()=>[_(k(C))])),_:1})])),_:1},8,["modelValue"]),(a(!0),l(s,null,i(U.value,(e=>(a(),r(I,{key:e,onClick:l=>{return t=e,N.value=t,void T.scrollToFirstMatch(t);var t},class:"hand",type:N.value===e?"warning":"info"},{default:h((()=>[g(x(e),1)])),_:2},1032,["onClick","type"])))),128))])):t("",!0)],64)}}}),[["__scopeId","data-v-bf16467e"]]);export{ve as default};
