import{d as e,R as l,_ as t,a,c as n,b as o,e as s,m as u,j as r,B as i,F as c,f as d,h as v,r as m,Y as p,S as f,o as y,w as _,q as h,a4 as w,v as g,E as S}from"./index-BERmxY3Y.js";import{w as V}from"./dzs-DZN_gNG7.js";const b=e({name:"MenuItem",props:{item:{type:Object,required:!0},level:{type:Number,default:0}},components:{MenuItem:()=>l((()=>Promise.resolve().then((()=>T))),void 0)}}),O={key:0,class:"menu-children"};const j=t(b,[["render",function(e,l,t,m,p,f){const y=a("v-md-preview"),_=a("menu-item",!0);return o(),n("div",null,[s("div",{class:i(["menu-title","level-"+e.level,e.item.type])},[r(y,{text:e.item.title,class:"md-preview"},null,8,["text"])],2),e.item.children&&e.item.children.length>0?(o(),n("div",O,[(o(!0),n(c,null,d(e.item.children,((l,t)=>(o(),v(_,{key:t,item:l,level:e.level+1},null,8,["item","level"])))),128))])):u("",!0)])}],["__scopeId","data-v-83b97c9a"]]),T=Object.freeze(Object.defineProperty({__proto__:null,default:j},Symbol.toStringTag,{value:"Module"})),k={class:"preview-container"},x={class:"preview-panel left-panel"},N={class:"panel-content"},I={class:"preview-panel"},J={class:"panel-content markdown-preview"},P={key:0,class:"empty-hint"},A={key:1,class:"menu-tree"},E=e({name:"PreviewDzsMenu",components:{MenuItem:()=>l((()=>Promise.resolve().then((()=>T))),void 0)}}),M=t(e({...E,props:{modelValue:{type:Boolean,default:!1},id:{},title:{default:"菜单预览"},content:{default:"[]"}},emits:["update:content","update:modelValue","close","save"],setup(e,{emit:l}){const t=()=>{let e=document.querySelector(".el-textarea__inner"),l=document.querySelector(".markdown-preview");l&&e&&(l.scrollTop=e.scrollTop)},u=e,i=l,b=m(u.modelValue),O=m(""),T=m(""),E=p((()=>{try{let e=O.value;return"string"==typeof e&&(e=JSON.parse(e)),Array.isArray(e)?e.filter((e=>e&&"object"==typeof e)):e&&"object"==typeof e?[e]:[]}catch(e){return[]}}));let M=null;const q=e=>{O.value=e,M&&clearTimeout(M),M=setTimeout((()=>{i("update:content",e)}),300)};f((()=>u.content),(e=>{e!==O.value&&(O.value="object"==typeof e&&null!==e?JSON.stringify(e,null,2):e||"",b.value&&(T.value=O.value))}),{immediate:!0}),y((()=>{u.content&&("object"==typeof u.content?O.value=JSON.stringify(u.content,null,2):O.value=u.content,T.value=O.value)})),f((()=>u.modelValue),(e=>{b.value=e,e&&(T.value=O.value)}));const z=async()=>{if(O.value!==T.value)try{await g.confirm("内容已修改，是否保存更改？","提示",{confirmButtonText:"保存",cancelButtonText:"不保存",type:"warning",distinguishCancelAndClose:!0});try{let e;e="string"==typeof O.value?JSON.parse(O.value):O.value;const l=await V({id:u.id,menu:e});if(200!==l.code)return void S.error(l.msg||"保存失败");T.value=O.value,i("update:content",JSON.stringify(e,null,2)),i("save",JSON.stringify(e,null,2)),S.success("保存成功")}catch(e){return void S.error("保存失败，请检查数据格式")}}catch(l){if("cancel"===l)O.value=T.value;else if("close"===l)return}i("update:modelValue",!1),i("close"),M&&(clearTimeout(M),M=null)};return(e,l)=>{const u=a("el-icon"),i=a("el-input"),m=a("el-dialog");return o(),v(m,{modelValue:b.value,"onUpdate:modelValue":l[1]||(l[1]=e=>b.value=e),fullscreen:"","close-on-click-modal":!1,"close-on-press-escape":!0,"show-close":!1,"destroy-on-close":"",class:"preview-dialog",onClose:z},{default:_((()=>[s("div",{class:"dialog-close",onClick:z},[r(u,null,{default:_((()=>[r(h(w))])),_:1})]),s("div",k,[s("div",x,[l[2]||(l[2]=s("div",{class:"panel-header"},"原始内容",-1)),s("div",N,[r(i,{modelValue:O.value,"onUpdate:modelValue":l[0]||(l[0]=e=>O.value=e),type:"textarea",resize:"none",class:"content-textarea",onInput:q,onScroll:t},null,8,["modelValue"])])]),s("div",I,[l[3]||(l[3]=s("div",{class:"panel-header"},"菜单结构",-1)),s("div",J,[0===E.value.length?(o(),n("div",P," 输入有效的JSON格式菜单数据 ")):(o(),n("div",A,[(o(!0),n(c,null,d(E.value,((e,l)=>(o(),n("div",{key:l,class:"menu-item"},[r(j,{item:e,level:0},null,8,["item"])])))),128))]))])])])])),_:1},8,["modelValue"])}}}),[["__scopeId","data-v-d4bce89a"]]);export{M as default};
