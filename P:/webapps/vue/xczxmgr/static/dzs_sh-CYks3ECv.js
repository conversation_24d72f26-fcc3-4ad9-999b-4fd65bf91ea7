const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["static/preview_dzs-DmGi4ZTD.js","static/index-BERmxY3Y.js","static/index-BjGfTwlR.css","static/dzs-DZN_gNG7.js","static/preview_dzs-BS5eTEZM.css","static/preview_dzs_menu-MTxdndbp.js","static/preview_dzs_menu-DfKACW31.css","static/preview_dzs_zsd-BFu0dKYc.js","static/preview_dzs_zsd-BQ2nVfPI.css","static/CompareDocDialog-Cv24SIEP.js","static/CompareDocDialog-BUX-opDB.css"])))=>i.map(i=>d[i]);
import{d as e,r as l,N as a,o as t,a as n,L as o,c as i,b as u,e as r,h as s,m as d,j as c,w as v,p,F as _,f as g,M as m,t as w,q as f,Q as h,R as y,E as b,_ as k}from"./index-BERmxY3Y.js";import{a as z,b as V}from"./dzs-DZN_gNG7.js";import{m as L}from"./message-Df6PNwXY.js";const x={class:"page"},C={class:"header"},j={class:"body"},E=k(e({__name:"dzs_sh",setup(e){const k=h((()=>y((()=>import("./preview_dzs-DmGi4ZTD.js")),__vite__mapDeps([0,1,2,3,4])))),E=h((()=>y((()=>import("./preview_dzs_menu-MTxdndbp.js")),__vite__mapDeps([5,1,2,3,6])))),R=h((()=>y((()=>import("./preview_dzs_zsd-BFu0dKYc.js")),__vite__mapDeps([7,1,2,3,8])))),S=h((()=>y((()=>import("./CompareDocDialog-Cv24SIEP.js")),__vite__mapDeps([9,1,2,10])))),A=l([]),T=l({pageLoading:!1,pageLoadingText:"获取数据中，请稍后...",pageLoadingSvg:'\n        <path class="path" d="\n          M 30 15\n          L 28 17\n          M 25.61 25.61\n          A 15 15, 0, 0, 1, 15 30\n          A 15 15, 0, 1, 1, 27.99 7.5\n          L 15 15\n        " style="stroke-width: 4px; fill: rgba(0, 0, 0, 0)"/>\n      ',selected_course_id:null});a();const D=l([]),U=()=>({height:"40px"}),I=()=>({padding:"4px 0"}),N=e=>{if(!e)return"-";const l=D.value.find((l=>Number(l.id)===Number(e)));return(null==l?void 0:l.course_name)||"-"},O=e=>{if(K.value){const l=A.value.find((e=>e.id===K.value));l&&(l.content=e)}b.success("保存成功")},P=e=>{if(K.value){const l=A.value.find((e=>e.id===K.value));l&&(l.menu=e)}b.success("保存成功")},F=e=>{if(K.value){const l=A.value.find((e=>e.id===K.value));l&&(l.zsd=e)}b.success("保存成功")},M=async()=>{T.value.pageLoading=!0;try{await(async()=>{try{const e=await V();200===e.code?D.value=e.data:L(e.msg||"获取课程列表失败","warning")}catch(e){L("获取课程列表失败","error")}})();const e={course_id:T.value.selected_course_id},l=await z(e);200===l.code?A.value=l.data:L(l.msg||"获取电子书列表失败","warning")}catch(e){L("获取数据失败","error")}finally{T.value.pageLoading=!1}},J=()=>{M()},Q=e=>{switch(e){case"Success":return"success";case"Fail":return"danger";case"Running":return"warning";default:return"info"}},$=e=>{switch(e){case"Success":return"成功";case"Fail":return"失败";case"Running":return"执行中";default:return"未执行"}},q=l(!1),B=l(""),G=l(""),H=l("content"),K=l(null),W=l(!1),X=l(""),Y=l(""),Z=(e,l,a)=>{B.value=e,K.value=a.id,"目录"===e?(H.value="menu",G.value="string"==typeof l?l:JSON.stringify(l,null,2)):"知识点"===e?(H.value="zsd",Array.isArray(l)?G.value=l.join(","):G.value="string"==typeof l?l:""):(H.value="content",G.value=l||""),q.value=!0},ee=()=>{W.value=!1,X.value="",Y.value=""};return t((()=>{M()})),(e,l)=>{const a=n("el-button"),t=n("el-option"),h=n("el-select"),y=n("el-table-column"),z=n("el-tooltip"),V=n("el-tag"),L=n("el-table"),le=o("loading");return u(),i(_,null,[r("div",x,[r("div",C,[c(a,{plain:"",type:"success",size:"small",onClick:M,icon:"refresh"},{default:v((()=>l[8]||(l[8]=[p("刷新 ")]))),_:1,__:[8]}),c(h,{modelValue:T.value.selected_course_id,"onUpdate:modelValue":l[0]||(l[0]=e=>T.value.selected_course_id=e),placeholder:"请选择课程",clearable:"",filterable:"",size:"small",style:{width:"260px"},onChange:J},{default:v((()=>[(u(!0),i(_,null,g(D.value,(e=>(u(),s(t,{key:e.id,label:`${e.course_code} ${e.course_name}`,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])]),r("div",j,[m((u(),s(L,{data:A.value,width:"100%",height:"calc(100vh - 135px)",ref:"multipleTable","highlight-current-row":"",size:"small",border:"","element-loading-text":T.value.pageLoadingText,"element-loading-spinner":T.value.pageLoadingSvg,"element-loading-svg-view-box":"-10, -10, 50, 50","element-loading-background":"rgba(122, 122, 122, 0.8)","row-style":U,"cell-style":I},{default:v((()=>[c(y,{prop:"",type:"index",label:"序号",align:"center",width:"60","header-align":"center"}),c(y,{label:"课程名称",align:"left","min-width":"100","header-align":"center","show-overflow-tooltip":""},{default:v((e=>[p(w(N(e.row.course_jc_id)),1)])),_:1}),c(y,{label:"标题",align:"left","min-width":"200","header-align":"center","show-overflow-tooltip":"",prop:"title"}),c(y,{label:"知识点标记",align:"left","min-width":"100","header-align":"center","show-overflow-tooltip":"",prop:"zsd_key"}),c(y,{label:"内容",align:"center",width:"80","header-align":"center"},{default:v((e=>[c(z,{content:"关闭时自动保存",placement:"top"},{default:v((()=>[e.row.content?(u(),s(a,{key:0,size:"small",type:"primary",onClick:l=>Z("内容",e.row.content,e.row)},{default:v((()=>l[9]||(l[9]=[p(" 编辑 ")]))),_:2,__:[9]},1032,["onClick"])):d("",!0)])),_:2},1024)])),_:1}),c(y,{label:"目录",align:"center",width:"80","header-align":"center"},{default:v((e=>[c(z,{content:"关闭时自动保存",placement:"top"},{default:v((()=>[e.row.menu?(u(),s(a,{key:0,size:"small",type:"primary",onClick:l=>Z("目录",e.row.menu,e.row)},{default:v((()=>l[10]||(l[10]=[p(" 编辑 ")]))),_:2,__:[10]},1032,["onClick"])):d("",!0)])),_:2},1024)])),_:1}),c(y,{label:"知识点",align:"center",width:"80","header-align":"center"},{default:v((e=>[c(z,{content:"关闭时自动保存",placement:"top"},{default:v((()=>[e.row.zsd?(u(),s(a,{key:0,size:"small",type:"primary",onClick:l=>Z("知识点",e.row.zsd,e.row)},{default:v((()=>l[11]||(l[11]=[p(" 编辑 ")]))),_:2,__:[11]},1032,["onClick"])):d("",!0)])),_:2},1024)])),_:1}),c(y,{label:"对比",align:"center",width:"100","header-align":"center"},{default:v((e=>[c(a,{type:"primary",size:"small",disabled:!e.row.url,onClick:l=>{var a;(a=e.row).url?(X.value=a.content||"",Y.value=a.url,W.value=!0):b.warning("没有可对比的文档URL")}},{default:v((()=>l[12]||(l[12]=[p(" 原文件对比 ")]))),_:2,__:[12]},1032,["disabled","onClick"])])),_:1}),c(y,{label:"修改时间",align:"center",width:"200","header-align":"center",prop:"create_date"}),c(y,{label:"修改人",align:"center",width:"100","header-align":"center",prop:"name"}),c(y,{label:"执行状态",align:"center",width:"100","header-align":"center"},{default:v((e=>[c(V,{type:Q(e.row.execute_status)},{default:v((()=>[p(w($(e.row.execute_status)),1)])),_:2},1032,["type"])])),_:1})])),_:1},8,["data","element-loading-text","element-loading-spinner"])),[[le,T.value.pageLoading]])])]),"content"===H.value?(u(),s(f(k),{key:0,modelValue:q.value,"onUpdate:modelValue":l[1]||(l[1]=e=>q.value=e),id:K.value||0,title:B.value,content:G.value,onClose:l[2]||(l[2]=e=>q.value=!1),onSave:O},null,8,["modelValue","id","title","content"])):"menu"===H.value?(u(),s(f(E),{key:1,modelValue:q.value,"onUpdate:modelValue":l[3]||(l[3]=e=>q.value=e),id:K.value||0,title:B.value,content:G.value,onClose:l[4]||(l[4]=e=>q.value=!1),onSave:P},null,8,["modelValue","id","title","content"])):"zsd"===H.value?(u(),s(f(R),{key:2,modelValue:q.value,"onUpdate:modelValue":l[5]||(l[5]=e=>q.value=e),id:K.value||0,title:B.value,content:G.value,onClose:l[6]||(l[6]=e=>q.value=!1),onSave:F},null,8,["modelValue","id","title","content"])):d("",!0),c(f(S),{modelValue:W.value,"onUpdate:modelValue":l[7]||(l[7]=e=>W.value=e),content:X.value,"doc-url":Y.value,onClose:ee},null,8,["modelValue","content","doc-url"])],64)}}}),[["__scopeId","data-v-4483dfe9"]]);export{E as default};
