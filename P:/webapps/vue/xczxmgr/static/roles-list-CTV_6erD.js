import{G as e,d as l,e as a,f as t,g as i}from"./settings-CpWN36lq.js";import{d as n,K as o,r as d,o as s,a as r,L as u,c,b as p,e as m,j as g,w as h,p as v,M as f,h as y,t as _,E as b,v as w}from"./index-BERmxY3Y.js";const k={class:"page"},V={class:"header"},x={class:"body"},C={style:{width:"100%","text-align":"center"}},z={class:"dialog-footer"},U={style:{width:"100%","text-align":"center"}},S={class:"dialog-footer"},T={style:{width:"100%","text-align":"center"}},j=n({__name:"roles-list",setup(n){const j=o({loading:!1,tableHeight:window.innerHeight-180,keySearch:"",dataTable:[]}),H=d(!1),B=d(""),D=d([]),O=d({}),P=d(!1),R=d([]),E=d([]),G=d(""),K=d(""),L=()=>{j.loading=!0;const l={key:j.keySearch};e(l).then((e=>{j.dataTable=e.data,j.loading=!1})).catch((e=>{j.loading=!1}))},M=e=>{D.value=e},X=(e,l)=>(null===l.zd_bm&&(l.zd_bm=""),l.name.indexOf(e)>-1||l.uid.indexOf(e)>-1),q=e=>{const l={role_id:e.id};t(l).then((l=>{G.value="角色【"+e.title+"】",K.value=e.id,R.value=l.data.nolist,E.value=l.data.yeslist,P.value=!0}))};return s((()=>{L()})),(e,t)=>{const n=r("el-button"),o=r("el-input"),d=r("el-table-column"),s=r("el-link"),A=r("el-table"),F=r("el-form-item"),I=r("el-form"),J=r("el-dialog"),N=r("el-transfer"),Q=u("loading");return p(),c("div",k,[m("div",V,[g(n,{type:"success",plain:"",size:"default",icon:"refresh",round:"",onClick:L}),g(o,{modelValue:j.keySearch,"onUpdate:modelValue":t[0]||(t[0]=e=>j.keySearch=e),placeholder:"用户名|名称",size:"default",style:{width:"260px",margin:"0 10px"},clearable:"",onChange:L},{append:h((()=>[g(n,{icon:"Search",onClick:L})])),_:1},8,["modelValue"]),g(n,{type:"primary",plain:"",size:"default",round:"",icon:"CirclePlus",onClick:t[1]||(t[1]=e=>(O.value={id:0,name:"",title:"",remark:""},B.value="新增角色",void(H.value=!0)))},{default:h((()=>t[13]||(t[13]=[v("新增")]))),_:1,__:[13]}),g(n,{type:"success",plain:"",size:"default",round:"",icon:"edit",onClick:t[2]||(t[2]=e=>(()=>{if(null==D.value||1!==D.value.length)return b({message:"请勾选一条数据进行编辑！",type:"error"}),!1;B.value="编辑【"+D.value[0].title+"】",H.value=!0,O.value=D.value[0]})())},{default:h((()=>t[14]||(t[14]=[v("编辑")]))),_:1,__:[14]}),g(n,{type:"danger",plain:"",size:"default",round:"",icon:"delete",onClick:t[3]||(t[3]=e=>(()=>{if(!(null!=D&&D.value.length>0))return b({message:"请至少勾选一条数据进行删除！",type:"error"}),!1;{const e=D.value;w.confirm("此操作将永久删除【"+e.length+"】条数据, 是否继续?","提示",{confirmButtonText:"删除",cancelButtonText:"取消",type:"warning"}).then((()=>{const l=[];for(var t=0;t<e.length;t++)l.push(e[t].id);var i=l.join(",");a({id:i}).then((e=>{e.data>0?(b({type:"success",message:"成功删除!"+e.data+"条"}),L()):b({type:"info",message:"删除失败!"+e.msg})})).catch((e=>{b({type:"info",message:"删除失败!"+e})}))})).catch((()=>{b({type:"info",message:"取消删除!"})}))}})())},{default:h((()=>t[15]||(t[15]=[v("删除")]))),_:1,__:[15]})]),m("div",x,[f((p(),y(A,{ref:"multipleRolesTable",class:"tb-edit",data:j.dataTable,border:"",height:j.tableHeight,size:"mini",onSelectionChange:M},{default:h((()=>[g(d,{type:"selection",width:"60",align:"center"}),g(d,{prop:"",type:"index",label:"序号",align:"center",width:"60","header-align":"center"}),g(d,{prop:"name",label:"角色名","min-width":"100",align:"center",sortable:""}),g(d,{prop:"title",label:"名称","min-width":"120",align:"center",sortable:""}),g(d,{prop:"num",label:"人数","min-width":"120",align:"center",sortable:""},{default:h((e=>[g(s,{type:"primary",underline:!1,onClick:l=>q(e.row)},{default:h((()=>[v(_(e.row.num),1)])),_:2},1032,["onClick"])])),_:1}),g(d,{prop:"date_created",label:"创建时间","min-width":"160",align:"center",sortable:""},{default:h((e=>[m("span",null,_(e.row.date_created),1)])),_:1}),g(d,{prop:"remark",label:"备注","min-width":"120",align:"left",sortable:""}),g(d,{prop:"create_name",label:"操作人","min-width":"100",align:"center",sortable:""}),g(d,{prop:"user_created",label:"用户组","min-width":"100",align:"center",sortable:""},{default:h((e=>[g(s,{type:"primary",underline:!1,icon:"CirclePlus",onClick:l=>q(e.row)},{default:h((()=>t[16]||(t[16]=[v("添加用户")]))),_:2,__:[16]},1032,["onClick"])])),_:1})])),_:1},8,["data","height"])),[[Q,j.loading]])]),g(J,{modelValue:H.value,"onUpdate:modelValue":t[9]||(t[9]=e=>H.value=e),ref:"kdroomDialog",title:B.value,draggable:"",width:"600px","close-on-press-escape":!1,"close-on-click-modal":!1},{footer:h((()=>[m("span",z,[g(n,{onClick:t[7]||(t[7]=e=>H.value=!1),icon:"Close"},{default:h((()=>t[17]||(t[17]=[v("关闭")]))),_:1,__:[17]}),g(n,{type:"success",onClick:t[8]||(t[8]=e=>{""!==O.value.name&&null!==O.value.name&&void 0!==O.value.name?""!==O.value.title&&null!==O.value.title&&void 0!==O.value.title?l(O.value).then((e=>{e.data>0?(H.value=!1,b({message:"保存成功！",type:"success"}),L()):b({message:"保存失败！"+e.msg,type:"error"})})):b({message:"角色名称不能为空",type:"info"}):b({message:"角色名(编码)不能为空",type:"info"})}),icon:"CircleCheck"},{default:h((()=>t[18]||(t[18]=[v(" 保存 ")]))),_:1,__:[18]})])])),default:h((()=>[m("div",C,[g(I,{model:O.value,inline:!0,"label-width":"100px"},{default:h((()=>[g(F,{label:"角色名(编码)"},{default:h((()=>[g(o,{modelValue:O.value.name,"onUpdate:modelValue":t[4]||(t[4]=e=>O.value.name=e),placeholder:"角色名(编码)",style:{width:"400px"}},null,8,["modelValue"])])),_:1}),g(F,{label:"角色名称"},{default:h((()=>[g(o,{modelValue:O.value.title,"onUpdate:modelValue":t[5]||(t[5]=e=>O.value.title=e),placeholder:"角色名称",style:{width:"400px"}},null,8,["modelValue"])])),_:1}),g(F,{label:"备注"},{default:h((()=>[g(o,{modelValue:O.value.remark,"onUpdate:modelValue":t[6]||(t[6]=e=>O.value.remark=e),type:"textarea",style:{width:"400px"}},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])])])),_:1},8,["modelValue","title"]),g(J,{modelValue:P.value,"onUpdate:modelValue":t[12]||(t[12]=e=>P.value=e),ref:"rolesUserVisible",class:"addRolsDia",title:G.value,draggable:"",width:"1000","close-on-press-escape":!1,"close-on-click-modal":!1},{footer:h((()=>[m("span",S,[m("div",T,[g(n,{type:"primary",style:{width:"150px"},onClick:t[11]||(t[11]=e=>(()=>{const e={user_ids:E.value.join(","),role_id:K.value};i(e).then((e=>{e.data>0?(P.value=!1,b({message:"保存成功！",type:"success"}),L()):b({message:"保存失败！"+e.msg,type:"error"})}))})())},{default:h((()=>t[19]||(t[19]=[v("保存")]))),_:1,__:[19]})])])])),default:h((()=>[m("div",U,[g(N,{modelValue:E.value,"onUpdate:modelValue":t[10]||(t[10]=e=>E.value=e),data:R.value,filterable:"","filter-method":X,style:{width:"98%",margin:"0 auto",height:"500px"},titles:["用户列表","已添加"],"button-texts":["删除","添加"],props:{key:"id",label:"name",disabled:!1}},{default:h((({option:e})=>[m("span",null,_(e.name)+" - "+_(e.uid),1)])),_:1},8,["modelValue","data"])])])),_:1},8,["modelValue","title"])])}}});export{j as default};
