import{a as e}from"./question-DWIQ7W1h.js";import{d as a,K as l,r as t,o as n,x as r,a as i,L as o,c as s,b as d,e as c,j as p,w as g,p as h,M as u,h as b}from"./index-BERmxY3Y.js";const m={class:"quesbank page"},w={class:"header"},_={class:"body"},v={style:{"margin-top":"16px","text-align":"right"}},y=a({__name:"question-bank",setup(a){const y=l({loading:!1,tableHeight:window.innerHeight-200,keySearch:"",learn_type:0,dataTable:[],total:0,pageSize:30,currentPage:1}),f=t([]),z=()=>{y.loading=!0;const a={keySearch:y.keySearch,learn_type:y.learn_type,currentPage:y.currentPage,pageSize:y.pageSize};e(a).then((e=>{y.dataTable=e.data||[],y.total=e.total||y.dataTable.length,y.loading=!1})).catch((e=>{y.loading=!1}))},S=e=>{f.value=e},k=e=>{y.pageSize=e,z()},x=e=>{y.currentPage=e,z()},C=()=>{y.tableHeight=window.innerHeight-200};return n((()=>{z(),C(),window.addEventListener("resize",C)})),r((()=>{window.removeEventListener("resize",C)})),(e,a)=>{const l=i("el-button"),t=i("el-input"),n=i("el-tag"),r=i("el-option"),f=i("el-select"),C=i("el-table-column"),j=i("el-table"),V=i("el-pagination"),H=o("loading");return d(),s("div",m,[c("div",w,[p(l,{type:"success",plain:"",size:"default",icon:"refresh",round:"",onClick:z}),p(t,{modelValue:y.keySearch,"onUpdate:modelValue":a[0]||(a[0]=e=>y.keySearch=e),placeholder:"课程编码|课程名称",size:"default",style:{width:"260px",margin:"0 10px"},clearable:"",onChange:z},{append:g((()=>[p(l,{icon:"Search",onClick:z})])),_:1},8,["modelValue"]),p(n,{class:"el_tag_5"},{default:g((()=>a[2]||(a[2]=[h("课程类型")]))),_:1,__:[2]}),p(f,{modelValue:y.learn_type,"onUpdate:modelValue":a[1]||(a[1]=e=>y.learn_type=e),clearable:"",filterable:"",style:{width:"160px"},placeholder:"课程类型",onChange:z},{default:g((()=>[p(r,{label:"全部",value:0}),p(r,{label:"成人高考",value:1}),p(r,{label:"自考",value:2}),p(r,{label:"微课",value:3})])),_:1},8,["modelValue"])]),c("div",_,[u((d(),b(j,{data:y.dataTable,border:"",class:"modTable",height:y.tableHeight,style:{width:"100%"},onSelectionChange:S},{default:g((()=>[p(C,{prop:"",type:"index",label:"序号",align:"center",width:"60","header-align":"center"}),p(C,{prop:"learn_title",label:"课程类型","min-width":"100",align:"center","header-align":"center","show-overflow-tooltip":"",sortable:""}),p(C,{prop:"kc_bm",label:"课程编码","min-width":"120",align:"center","show-overflow-tooltip":"",sortable:""}),p(C,{prop:"kc_mc",label:"课程名称","min-width":"120",align:"left","header-align":"center",sortable:""}),p(C,{prop:"num_total",label:"题库总量","min-width":"120",align:"center","header-align":"center",sortable:"","show-overflow-tooltip":""}),p(C,{prop:"num_subjective",label:"主观题量","min-width":"100",align:"center","header-align":"center",sortable:""}),p(C,{prop:"num_objective",label:"客观题量","min-width":"100",align:"center","header-align":"center",sortable:""}),p(C,{label:"试题来源"},{default:g((()=>[p(C,{prop:"num_source_1",label:"自编","min-width":"100",align:"center","header-align":"center",sortable:""}),p(C,{prop:"num_source_3",label:"历年真题","min-width":"100",align:"center","header-align":"center",sortable:""}),p(C,{prop:"num_source_4",label:"全真模拟","min-width":"100",align:"center","header-align":"center",sortable:""})])),_:1})])),_:1},8,["data","height"])),[[H,y.loading]]),c("div",v,[p(V,{background:"",layout:"total, sizes, prev, pager, next, jumper",total:y.total,"page-size":y.pageSize,"current-page":y.currentPage,"page-sizes":[20,50,100,500,y.total],onSizeChange:k,onCurrentChange:x},null,8,["total","page-size","current-page","page-sizes"])])])])}}});export{y as default};
