import{b as e,c as l,D as a}from"./settings-CpWN36lq.js";import{d as t,r as o,K as i,o as d,a as n,L as r,c as u,b as s,e as c,j as p,w as m,p as h,t as f,M as g,h as C,i as v,m as _,q as y,F as w,f as k,E as b,v as F}from"./index-BERmxY3Y.js";const D={class:"moduleListfrom page"},S={class:"header"},V={class:"body"},B={key:0,style:{color:"blue"}},P={key:1,style:{color:"Red"}},x={style:{width:"100%","text-align":"center"}},M={style:{width:"235px"}},L={class:"dialog-footer"},R={class:"dialog_icon_div"},T={class:"dialog-footer"},U=t({__name:"module-list",setup(t){const U=o(["InfoFilled","CircleCheckFilled","SuccessFilled","WarningFilled","CircleCloseFilled","QuestionFilled","WarnTriangleFilled","UserFilled","MoreFilled","Tools","HomeFilled","Menu","UploadFilled","Avatar","HelpFilled","Share","StarFilled","Comment","Histogram","Grid","Promotion","DeleteFilled","RemoveFilled","CirclePlusFilled","Plus","Minus","CirclePlus","Search","Female","Male","Aim","House","FullScreen","Loading","Link","Service","Pointer","Star","Notification","Connection","ChatDotRound","Setting","Clock","Position","Discount","Odometer","ChatSquare","ChatRound","ChatLineRound","ChatLineSquare","ChatDotSquare","View","Hide","Unlock","Lock","RefreshRight","RefreshLeft","Refresh","Bell","MuteNotification","User","Check","CircleCheck","Warning","CircleClose","Close","PieChart","More","Compass","Filter","Switch","Select","SemiSelect","CloseBold","EditPen","Edit","Message","MessageBox","TurnOff","Finished","Delete","Crop","SwitchButton","Operation","Open","Remove","ZoomOut","ZoomIn","ArrowLeft","ArrowUp","ArrowRight","ArrowDown","ArrowLeftBold","ArrowUpBold","ArrowRightBold","ArrowDownBold","DArrowRight","DArrowLeft","Download","Upload","Top","Bottom","Back","Right","TopRight","TopLeft","BottomRight","BottomLeft","Sort","SortUp","SortDown","Rank","CaretLeft","CaretTop","CaretRight","CaretBottom","DCaret","Expand","Fold","DocumentAdd","Document","Notebook","Tickets","Memo","Collection","Postcard","ScaleToOriginal","SetUp","DocumentDelete","DocumentChecked","DataBoard","DataAnalysis","CopyDocument","FolderChecked","Files","Folder","FolderDelete","FolderRemove","FolderOpened","DocumentCopy","DocumentRemove","FolderAdd","FirstAidKit","Reading","DataLine","Management","Checked","Ticket","Failed","TrendCharts","List","Microphone","Mute","Mic","VideoPause","VideoCamera","VideoPlay","Headset","Monitor","Film","Camera","Picture","PictureRounded","Iphone","Cellphone","VideoCameraFilled","PictureFilled","Platform","CameraFilled","BellFilled","Location","LocationInformation","DeleteLocation","Coordinate","Bicycle","OfficeBuilding","School","Guide","AddLocation","MapLocation","Place","LocationFilled","Van","Watermelon","Pear","NoSmoking","Smoking","Mug","GobletSquareFull","GobletFull","KnifeFork","Sugar","Bowl","MilkTea","Lollipop","Coffee","Chicken","Dish","IceTea","ColdDrink","CoffeeCup","DishDot","IceDrink","IceCream","Dessert","IceCreamSquare","ForkSpoon","IceCreamRound","Food","HotWater","Grape","Fries","Apple","Burger","Goblet","GobletSquare","Orange","Cherry","Printer","Calendar","CreditCard","Box","Money","Refrigerator","Cpu","Football","Brush","Suitcase","SuitcaseLine","Umbrella","AlarmClock","Medal","GoldMedal","Present","Mouse","Watch","QuartzWatch","Magnet","Help","Soccer","ToiletPaper","ReadingLamp","Paperclip","MagicStick","Basketball","Baseball","Coin","Goods","Sell","SoldOut","Key","ShoppingCart","ShoppingCartFull","ShoppingTrolley","Phone","Scissor","Handbag","ShoppingBag","Trophy","TrophyBase","Stopwatch","Timer","CollectionTag","TakeawayBox","PriceTag","Wallet","Opportunity","PhoneFilled","WalletFilled","GoodsFilled","Flag","BrushFilled","Briefcase","Stamp","Sunrise","Sunny","Ship","MostlyCloudy","PartlyCloudy","Sunset","Drizzling","Pouring","Cloudy","Moon","MoonNight","Lightning","ChromeFilled","Eleme","ElemeFilled","ElementPlus","Shop","SwitchFilled","WindPower"]);o(["申报","搜索","导出","申请","查看","添加","编辑","删除"]),o(!1);const A=o(window.innerHeight-155),z=o(!1),E=o(!1),O=o(!1),G=o(!1),H=o(!1),W=o(!1),I=o(""),q=o(),N=o(!1);var K=o({puid:0,id:0,pid:0,title:"",path:"",component:"",icon:"",sn:0,reamrk:"",status:1,type:"menu_dir"});const j=i({tableDataData:[],loading:!1}),Q=o("展开全部"),Z=()=>{j.loading=!0;var l={status:z.value?"":"1"};e(l).then((e=>{j.tableDataData=e.data,j.loading=!1}))},X=e=>{K.value={puid:e.puid,id:e.id,pid:e.pid,title:e.title,path:e.path,component:e.component,icon:e.icon,sn:e.sn,reamrk:e.reamrk,status:e.status,type:0===e.pid?"menu_dir":"menu"},I.value=0===e.pid?"编辑父节点":"编辑子节点",H.value=0===e.pid,W.value=0===e.pid,G.value=!1,E.value=!0},J=e=>{var l=[],a=0;e>0?(l=(l=j.tableDataData.filter((l=>{if(l.id===e)return l})))[0].children,a=10):(l=j.tableDataData.filter((l=>{if(l.pid===e)return l})),a=100);var t=l.map((e=>e.id)),o=Math.max(...t);return 0==t.length&&(o=e),o+a},Y=()=>{0===K.value.status&&"menu_dir"===K.value.type&&b({message:"你正在禁用父节点，如果禁用其下所有子节点将会同时被禁用！！",type:"warning"})},$=e=>0===e.status?"success-row":"",ee=(e,l)=>{e.forEach((e=>{q.value.toggleRowExpansion(e,l),e.children&&le(e.children,l)}))},le=(e,l)=>{e.forEach((e=>{q.value.toggleRowExpansion(e,l),e.children&&le(e.children,l)}))};return d((()=>{Z()})),(e,t)=>{const o=n("el-button"),i=n("el-checkbox"),d=n("el-table-column"),le=n("el-icon"),ae=n("Edit"),te=n("Delete"),oe=n("CirclePlusFilled"),ie=n("el-table"),de=n("el-radio"),ne=n("el-radio-group"),re=n("el-form-item"),ue=n("el-input"),se=n("el-input-number"),ce=n("el-form"),pe=n("el-dialog"),me=r("loading");return s(),u("div",D,[c("div",S,[p(o,{type:"success",plain:"",size:"default",icon:"refresh",round:"",onClick:Z}),p(o,{type:"primary",plain:"",onClick:t[0]||(t[0]=e=>{N.value?(N.value=!1,Q.value="展开全部",ee(j.tableDataData,!1)):(N.value=!0,Q.value="全部折叠",ee(j.tableDataData,!0))}),size:"default",round:"",icon:"View","aria-label":""},{default:m((()=>[h(f(Q.value),1)])),_:1}),p(o,{type:"success",plain:"",onClick:t[1]||(t[1]=e=>(K.value={puid:0,id:J(0),pid:0,title:"",path:"",component:"",icon:"",sn:0,reamrk:"",status:1,type:"menu_dir"},I.value="添加父节点",E.value=!0,G.value=!1,H.value=!1,void(W.value=!1))),icon:"CirclePlus",round:"",size:"default"},{default:m((()=>t[21]||(t[21]=[h("添加父节点")]))),_:1,__:[21]}),p(i,{style:{"margin-left":"10px"},modelValue:z.value,"onUpdate:modelValue":t[2]||(t[2]=e=>z.value=e),size:"default",onChange:t[3]||(t[3]=e=>Z())},{default:m((()=>t[22]||(t[22]=[h("显示全部")]))),_:1,__:[22]},8,["modelValue"])]),c("div",V,[g((s(),C(ie,{border:"",height:A.value,data:j.tableDataData,"row-class-name":$,"row-key":"id",class:"moduleTable","default-expand-all":!1,"highlight-current-row":"",ref_key:"tablexTree",ref:q},{default:m((()=>[p(d,{prop:"",label:"#",align:"center",width:"60"}),p(d,{prop:"id",label:"节点编码",align:"center","min-width":"80"}),p(d,{prop:"pid",label:"父节点",align:"center","min-width":"80"}),p(d,{prop:"title",label:"中文名称","header-align":"center",align:"left",width:"160","show-overflow-tooltip":""}),p(d,{prop:"path",label:"节点路径","header-align":"center",align:"left","min-width":"250"}),p(d,{prop:"icon",label:"图标",align:"center","min-width":"60","show-overflow-tooltip":""},{default:m((e=>[p(le,{size:16},{default:m((()=>[(s(),C(v(e.row.icon)))])),_:2},1024)])),_:1}),p(d,{prop:"sn",label:"排序","header-align":"center",align:"center","min-width":"80"}),p(d,{prop:"icon",label:"状态",align:"center","min-width":"60","show-overflow-tooltip":""},{default:m((e=>[1===e.row.status?(s(),u("span",B,"启用")):(s(),u("span",P,"禁用"))])),_:1}),p(d,{prop:"status",label:"操作",align:"center",width:"200"},{default:m((e=>[0===e.row.pid?(s(),C(o,{key:0,link:"",type:"primary",size:"small",onClick:l=>X(e.row)},{default:m((()=>[p(le,null,{default:m((()=>[p(ae)])),_:1}),t[23]||(t[23]=h("编辑"))])),_:2,__:[23]},1032,["onClick"])):_("",!0),0!==e.row.pid?(s(),C(o,{key:1,link:"",type:"primary",size:"small",onClick:l=>X(e.row)},{default:m((()=>[p(le,null,{default:m((()=>[p(ae)])),_:1}),t[24]||(t[24]=h("编辑"))])),_:2,__:[24]},1032,["onClick"])):_("",!0),(0===e.row.pid&&0===e.row.children.length||0!==e.row.pid)&&1!==e.row.status?(s(),C(o,{key:2,link:"",type:"danger",size:"small",onClick:l=>{return t=e.row,void F.confirm("此操作将永久删除【"+t.title+"】节点功能, 是否继续?","提示",{confirmButtonText:"删除",cancelButtonText:"取消",type:"warning"}).then((()=>{const e={puid:t.puid};a(e).then((e=>{e.data>0?(b({type:"success",message:"成功删除!"+e.data+"条"}),Z()):b({type:"info",message:"删除失败!"+e.msg})})).catch((e=>{b({type:"info",message:"删除失败!"+e})}))})).catch((()=>{b({type:"info",message:"取消删除!"})}));var t}},{default:m((()=>[p(le,null,{default:m((()=>[p(te)])),_:1}),t[25]||(t[25]=h("删除"))])),_:2,__:[25]},1032,["onClick"])):_("",!0),0===e.row.pid?(s(),C(o,{key:3,link:"",type:"primary",size:"small",onClick:l=>{return a=e.row,K.value={puid:0,id:J(a.id),pid:a.id,title:"",path:"",component:"",icon:"",sn:0,reamrk:"",status:1,type:"menu"},I.value="添加子节点",H.value=!0,W.value=!0,G.value=!1,void(E.value=!0);var a}},{default:m((()=>[p(le,null,{default:m((()=>[p(oe)])),_:1}),t[26]||(t[26]=h("添加子节点"))])),_:2,__:[26]},1032,["onClick"])):_("",!0)])),_:1})])),_:1},8,["height","data"])),[[me,j.loading]])]),p(pe,{modelValue:E.value,"onUpdate:modelValue":t[17]||(t[17]=e=>E.value=e),title:I.value,"custom-class":"parentNodeclass",draggable:"",width:"800","close-on-press-escape":!1,"close-on-click-modal":!1},{footer:m((()=>[c("span",L,[p(o,{onClick:t[15]||(t[15]=e=>E.value=!1)},{default:m((()=>t[31]||(t[31]=[h("关闭")]))),_:1,__:[31]}),p(o,{type:"success",onClick:t[16]||(t[16]=e=>("menu_dir"===K.value.type?K.value.component="Layout":K.value.component=K.value.path,void l(K.value).then((e=>{1===e.data&&("menu_dir"===K.value.type&&K.value.status,E.value=!1,b({message:"保存成功！",type:"success"}),Z())}))))},{default:m((()=>t[32]||(t[32]=[h(" 保存 ")]))),_:1,__:[32]})])])),default:m((()=>[c("div",x,[p(ce,{model:y(K),inline:!0,"label-width":"80px"},{default:m((()=>[p(re,{label:"节点类型"},{default:m((()=>[p(ne,{modelValue:y(K).type,"onUpdate:modelValue":t[4]||(t[4]=e=>y(K).type=e),disabled:W.value,style:{width:"235px"}},{default:m((()=>[p(de,{label:"menu"},{default:m((()=>t[27]||(t[27]=[h("功能")]))),_:1,__:[27]}),p(de,{label:"menu_dir"},{default:m((()=>t[28]||(t[28]=[h("父节点")]))),_:1,__:[28]})])),_:1},8,["modelValue","disabled"])])),_:1}),p(re,{label:"中文名称"},{default:m((()=>[p(ue,{modelValue:y(K).title,"onUpdate:modelValue":t[5]||(t[5]=e=>y(K).title=e),style:{width:"235px"}},null,8,["modelValue"])])),_:1}),p(re,{label:"父节点"},{default:m((()=>[p(ue,{modelValue:y(K).pid,"onUpdate:modelValue":t[6]||(t[6]=e=>y(K).pid=e),disabled:H.value,style:{width:"235px"}},null,8,["modelValue","disabled"])])),_:1}),p(re,{label:"节点编码"},{default:m((()=>[p(ue,{modelValue:y(K).id,"onUpdate:modelValue":t[7]||(t[7]=e=>y(K).id=e),disabled:G.value,style:{width:"235px"}},null,8,["modelValue","disabled"])])),_:1}),p(re,{label:"路径"},{default:m((()=>[p(ue,{modelValue:y(K).path,"onUpdate:modelValue":t[8]||(t[8]=e=>y(K).path=e),style:{width:"590px"},placeholder:"/文件夹名/文件名"},null,8,["modelValue"])])),_:1}),p(re,{label:"是否启用"},{default:m((()=>[p(ne,{modelValue:y(K).status,"onUpdate:modelValue":t[11]||(t[11]=e=>y(K).status=e),style:{width:"235px"}},{default:m((()=>[p(de,{label:1,onChange:t[9]||(t[9]=e=>Y())},{default:m((()=>t[29]||(t[29]=[h("启用")]))),_:1,__:[29]}),p(de,{label:0,onChange:t[10]||(t[10]=e=>Y())},{default:m((()=>t[30]||(t[30]=[h("禁用")]))),_:1,__:[30]})])),_:1},8,["modelValue"])])),_:1}),p(re,{label:"图标:"},{default:m((()=>[c("div",M,[c("span",null,[p(le,{size:20},{default:m((()=>[(s(),C(v(y(K).icon)))])),_:1})]),c("a",{style:{color:"blue",float:"right"},onClick:t[12]||(t[12]=e=>O.value=!0)},"查看")])])),_:1}),p(re,{label:"排序"},{default:m((()=>[p(se,{modelValue:y(K).sn,"onUpdate:modelValue":t[13]||(t[13]=e=>y(K).sn=e),min:0,placeholder:"排序",style:{width:"235px"}},null,8,["modelValue"])])),_:1}),p(re,{label:"备注"},{default:m((()=>[p(ue,{modelValue:y(K).reamrk,"onUpdate:modelValue":t[14]||(t[14]=e=>y(K).reamrk=e),type:"textarea",style:{width:"235px"}},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])])])),_:1},8,["modelValue","title"]),p(pe,{draggable:"",title:"请选择图标","custom-class":"icondialogclass",modelValue:O.value,"onUpdate:modelValue":t[20]||(t[20]=e=>O.value=e),"close-on-press-escape":!1,"close-on-click-modal":!1},{footer:m((()=>[c("span",T,[p(o,{type:"success",onClick:t[19]||(t[19]=e=>O.value=!1)},{default:m((()=>t[33]||(t[33]=[h("确定")]))),_:1,__:[33]})])])),default:m((()=>[c("div",R,[p(ne,{modelValue:y(K).icon,"onUpdate:modelValue":t[18]||(t[18]=e=>y(K).icon=e)},{default:m((()=>[(s(!0),u(w,null,k(U.value,(e=>(s(),u("span",{key:e,style:{padding:"20px","line-height":"40px"}},[p(de,{label:e},{default:m((()=>[p(le,{size:20},{default:m((()=>[(s(),C(v(e)))])),_:2},1024)])),_:2},1032,["label"])])))),128))])),_:1},8,["modelValue"])])])),_:1},8,["modelValue"])])}}});export{U as default};
