import{f as e}from"./exam-jF1-Sa8S.js";import{a as l}from"./course-DUm3UmHR.js";import{A as a}from"./AssignResourceDialog-C5I0hvnU.js";import{d as t,r as o,N as r,o as s,a as u,L as i,c as d,b as n,e as p,M as c,j as v,w as m,F as f,f as _,h as b,t as h,m as y,p as g,E as k}from"./index-BERmxY3Y.js";import"./kcmgr-_69jTmcm.js";import"./dzs-DZN_gNG7.js";const I={class:"coursedetail",style:{padding:"5px"}},x={style:{float:"left"}},w={style:{float:"right",color:"var(--el-text-color-secondary)","font-size":"13px"}},V={key:1},j={key:1},C=t({__name:"course_detail",setup(t){const C=o({visible:!1,courseInfoId:0,courseBaseId:0,title:"",plate_id:0}),B=o([]),U=o(""),z=o([]),R=o(!1),$=o([]),A=o(""),E=r();s((()=>{e({}).then((e=>{200==e.code?(B.value=e.data,B.value.forEach((e=>{e.mc=`【${e.kc_bm}】${e.kc_mc}`}))):k.error(e.msg)})),N()}));const N=()=>{R.value=!0,l({}).then((e=>{U.value||A.value?(z.value=U.value?e.data.listdata.filter((e=>e.couse_info_id==U.value)):e.data.listdata,z.value=A.value?z.value.filter((e=>e.learn_type==A.value)):z.value):z.value=e.data.listdata,$.value=e.data.columns})).finally((()=>{R.value=!1}))},T=()=>{C.value.visible=!1,k.success("资料分配成功")};return(e,l)=>{const t=u("el-button"),o=u("el-option"),r=u("el-select"),s=u("el-table-column"),k=u("el-link"),q=u("el-table"),D=i("loading");return n(),d("div",I,[p("div",null,[v(t,{type:"primary",onClick:N,icon:"RefreshRight"}),v(r,{modelValue:A.value,"onUpdate:modelValue":l[0]||(l[0]=e=>A.value=e),class:"m-2",placeholder:"类型",style:{width:"100px","margin-left":"5px"},clearable:"",filterable:"",onChange:N},{default:m((()=>[v(o,{key:"微课",label:"微课",value:"微课"}),v(o,{key:"成人高考",label:"成人高考",value:"成人高考"}),v(o,{key:"自考",label:"自考",value:"自考"})])),_:1},8,["modelValue"]),v(r,{modelValue:U.value,"onUpdate:modelValue":l[1]||(l[1]=e=>U.value=e),class:"m-2",placeholder:"在线课程",style:{width:"190px","margin-left":"5px"},clearable:"",filterable:"",onChange:N},{default:m((()=>[(n(!0),d(f,null,_(B.value,(e=>(n(),b(o,{key:e.id,label:e.mc,value:e.id},{default:m((()=>[p("span",x,h(e.mc),1),p("span",w," 版本："+h(e.ver),1)])),_:2},1032,["label","value"])))),128))])),_:1},8,["modelValue"])]),c((n(),b(q,{data:z.value,width:"100%",height:"calc(100vh - 163px)",ref:"multipleTable","highlight-current-row":"",size:"small",border:"",style:{"margin-top":"5px"}},{default:m((()=>[z.value.length>0?(n(),b(s,{key:0,label:"序号",align:"center",width:"45","header-align":"center",type:"index"})):y("",!0),(n(!0),d(f,null,_($.value.filter((e=>0==e.plate_id)),(e=>(n(),b(s,{label:e.title,prop:e.prop,align:"center",sortable:"","header-align":"center"},{default:m((l=>["num_ols_chapter"===e.prop?(n(),b(k,{key:0,onClick:e=>{return a=l.row,void E.push({path:"/onlinecourse/online_chapter",query:{course_info_id:a.course_info_id}});var a},type:"primary"},{default:m((()=>[g(h(l.row[e.prop]),1)])),_:2},1032,["onClick"])):(n(),d("span",V,h(l.row[e.prop]),1))])),_:2},1032,["label","prop"])))),256)),v(s,{label:"模块"},{default:m((()=>[(n(!0),d(f,null,_($.value.filter((e=>e.plate_id>0)),(e=>(n(),b(s,{label:e.title,prop:e.prop,align:"center",sortable:"","header-align":"center"},{default:m((l=>[l.row[e.prop]>=0?(n(),b(k,{key:0,onClick:a=>{return t=l.row,o=e.plate_id,C.value.courseInfoId=t.course_info_id,C.value.courseBaseId=t.course_base_id,C.value.plate_id=o,C.value.title=`指定资料 - ${t.kc_mc}`,void(C.value.visible=!0);var t,o},type:"primary"},{default:m((()=>[g(h(l.row[e.prop]),1)])),_:2},1032,["onClick"])):(n(),d("span",j))])),_:2},1032,["label","prop"])))),256))])),_:1})])),_:1},8,["data"])),[[D,R.value]]),v(a,{modelValue:C.value.visible,"onUpdate:modelValue":l[2]||(l[2]=e=>C.value.visible=e),courseInfoId:C.value.courseInfoId,courseBaseId:C.value.courseBaseId,plateId:C.value.plate_id,title:C.value.title,onSave:T},null,8,["modelValue","courseInfoId","courseBaseId","plateId","title"])])}}});export{C as default};
