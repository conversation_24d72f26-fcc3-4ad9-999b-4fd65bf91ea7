import{A as a,B as e}from"./kcmgr-_69jTmcm.js";import{d as t,r as s,K as l,o as n,a as o,L as r,c as d,b as i,j as u,w as c,M as p,h as m,p as g,t as h,e as _,E as w,_ as f}from"./index-BERmxY3Y.js";const v={class:"app-container"},b=f(t({__name:"video_check_rule",setup(t){const f=s(!1),b=s(!1),y=s([]);l({visible:!1,title:"添加规则",loading:!1});const x=async()=>{try{f.value=!0,b.value=!0;const e=await a({});"200"===e.code||200===parseInt(e.code)?y.value=(e.data||[]).map((a=>({...a,statusLoading:!1,status:"1"===a.status||1===a.status}))):w.error(e.msg||"获取数据失败")}catch(e){w.error("获取数据失败")}finally{f.value=!1,setTimeout((()=>{b.value=!1}),100)}};return n((()=>{x()})),(a,t)=>{const s=o("el-table-column"),l=o("el-tag"),n=o("el-input-number"),V=o("el-switch"),L=o("el-table"),C=o("el-card"),I=r("loading");return i(),d("div",v,[u(C,{class:"box-card"},{default:c((()=>[p((i(),m(L,{data:y.value,border:"",style:{width:"100%"},stripe:""},{default:c((()=>[u(s,{prop:"id",label:"ID",width:"80",align:"center"}),u(s,{prop:"config_version",label:"版本",width:"120",align:"center"}),u(s,{label:"是否执行",width:"120",align:"center"},{default:c((({row:a})=>[u(l,{type:a.is_enabled?"success":"danger"},{default:c((()=>[g(h(a.is_enabled?"是":"否"),1)])),_:2},1032,["type"])])),_:1}),u(s,{label:"检查方法",width:"150",align:"center"},{default:c((({row:a})=>[u(l,{type:"popup"===a.check_method?"primary":"warning"},{default:c((()=>{return[g(h((e=a.check_method,{popup:"弹窗验证",face_recognition:"人脸识别"}[e]||e)),1)];var e})),_:2},1032,["type"])])),_:1}),u(s,{label:"检查时间",width:"150",align:"center"},{default:c((({row:a})=>[u(n,{modelValue:a.time,"onUpdate:modelValue":e=>a.time=e,min:1,max:999,size:"small",onChange:t=>(async a=>{if(!b.value)try{const t=await e({id:a.id,time:a.time});"200"===t.code||200===parseInt(t.code)?w.success("时间更新成功"):w.error(t.msg||"时间更新失败")}catch(t){w.error("时间更新失败")}})(a),style:{width:"100px"}},null,8,["modelValue","onUpdate:modelValue","onChange"]),t[0]||(t[0]=_("span",{style:{"margin-left":"5px"}},"分钟",-1))])),_:1}),u(s,{label:"状态",width:"200",align:"center"},{default:c((({row:a})=>[u(V,{modelValue:a.status,"onUpdate:modelValue":e=>a.status=e,loading:a.statusLoading,onChange:t=>(async a=>{if(!b.value)try{a.statusLoading=!0;const t=await e({id:a.id,status:a.status?"1":"0"});"200"===t.code||200===parseInt(t.code)?(w.success((a.status?"启用":"禁用")+"成功"),x()):(a.status=!a.status,w.error(t.msg||"状态更新失败"))}catch(t){a.status=!a.status,w.error("状态更新失败")}finally{a.statusLoading=!1}})(a),"active-text":"启用","inactive-text":"禁用"},null,8,["modelValue","onUpdate:modelValue","loading","onChange"])])),_:1}),u(s,{prop:"created_at",label:"创建时间",width:"180",align:"center"},{default:c((({row:a})=>{return[g(h((e=a.created_at,e?new Date(e).toLocaleString("zh-CN"):"-")),1)];var e})),_:1})])),_:1},8,["data"])),[[I,f.value]])])),_:1})])}}}),[["__scopeId","data-v-4910d3d3"]]);export{b as default};
