import{I as s}from"./index-BERmxY3Y.js";async function t(t){return s.post("mgr_dzs/getDzsList",t)}async function n(t){return s.post("mgr_dzs/insertDzsList",t)}async function r(){return s.post("mgr_course/infolist")}async function o(){return s.post("mgr_course/infojclist")}async function e(t){return s.post("/file/upload_to_drive",t)}async function a(t){return s.post("mgr_dzs/getContentFromCoze",t)}async function u(t){return s.post("mgr_dzs/checkContentFromCoze",t)}async function c(t){return s.post("/mgr_dzs/UpdateDzs",t)}async function d(t){return await s.post("/mgr_dzs/course_dzs_info",t)}async function i(t){return s.post("/mgr_dzs/UpdateDzsContent",t)}async function g(t){return s.post("/mgr_dzs/UpdateDzsMenu",t)}async function p(t){return s.post("/mgr_dzs/UpdateDzsZsd",t)}async function f(t){return s.post("/mgr_dzs/getJcList",t)}async function m(t){return s.post("/mgr_dzs/getJcVer",t)}async function z(t){return s.post("/mgr_dzs/editJc",t)}async function _(t){return s.post("/mgr_dzs/addJc",t)}async function y(t){return s.post("/mgr_dzs/getJcDatas",t)}async function C(){return s.post("mgr_dzs/getSelectCourseJcList")}async function l(t){return s.post("mgr_dzs/getCourseKsdgList",t)}async function D(t){return s.post("mgr_dzs/setCourseKsdgList",t)}async function J(t){return s.post("mgr_dzs/getContentKsdgFromCoze",t)}async function w(t){return s.post("mgr_dzs/updateCourseKsdg",t)}function K(t){return s.post("mgr_dzs/getCourseKnowledgeNodePage",t)}function L(t){return s.post("mgr_dzs/setKnowledgeNodeData",t)}function N(t){return s.post("mgr_dzs/importKnowledgeNodeFromExcel",t)}function U(t){return s.post("/file/upload_to_drive",t)}function h(t){return s.post("mgr_dzs/DelCourseknowledgeNodeData",t)}function k(t){return s.post("mgr_dzs/SaveCourseknowledgeNodeData",t)}function v(t){return s.post("mgr_dzs/getHierarchyJcData",t)}function x(t){return s.post("mgr_dzs/SynToJcChapter",t)}export{d as C,h as D,z as E,m as G,x as S,c as U,t as a,o as b,u as c,C as d,v as e,w as f,a as g,J as h,n as i,l as j,y as k,r as l,f as m,_ as n,K as o,U as p,L as q,N as r,D as s,k as t,e as u,i as v,g as w,p as x};
