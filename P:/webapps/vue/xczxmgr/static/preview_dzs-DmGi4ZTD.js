import{d as e,r as a,o as l,S as t,af as o,a as u,h as s,b as n,w as c,e as d,j as i,q as r,a4 as v,v as m,E as p,_ as f}from"./index-BERmxY3Y.js";import{v as V}from"./dzs-DZN_gNG7.js";const h={class:"editor-container"},g=f(e({__name:"preview_dzs",props:{modelValue:{type:Boolean,default:!1},id:{},title:{default:"内容预览"},content:{default:""}},emits:["update:content","update:modelValue","close","save"],setup(e,{emit:f}){const g=e,_=f,w=a(g.modelValue),y=a(""),T=a("");let x=null;l((()=>{y.value=g.content,T.value=g.content}));const C=e=>{y.value=e,x&&clearTimeout(x),x=setTimeout((()=>{_("update:content",e)}),300)};t((()=>g.content),(e=>{e!==y.value&&(y.value=e,w.value&&(T.value=e))}),{immediate:!0}),t((()=>g.modelValue),(e=>{w.value=e,e&&(T.value=g.content)}));const b=async()=>{if(y.value!==T.value)try{await m.confirm("内容已修改，是否保存更改？","提示",{confirmButtonText:"保存",cancelButtonText:"不保存",type:"warning",distinguishCancelAndClose:!0});try{const e=await V({id:g.id,content:y.value});if(200!==e.code)return void p.error(e.msg||"保存失败");p.success("保存成功"),T.value=y.value,_("update:content",y.value),_("save",y.value)}catch(e){return void p.error("保存失败，请重试")}}catch(e){if("cancel"===e)T.value=y.value;else if("close"===e)return}T.value=y.value,_("update:modelValue",!1),_("close"),x&&(clearTimeout(x),x=null),_("update:content",y.value)};return o((()=>{x&&clearTimeout(x)})),(e,a)=>{const l=u("el-icon"),t=u("v-md-editor"),o=u("el-dialog");return n(),s(o,{modelValue:w.value,"onUpdate:modelValue":a[1]||(a[1]=e=>w.value=e),title:e.title,fullscreen:"","close-on-click-modal":!1,"close-on-press-escape":!0,"show-close":!1,"destroy-on-close":"",class:"preview-dialog",onClose:b},{default:c((()=>[d("div",{class:"dialog-close",onClick:b},[i(l,null,{default:c((()=>[i(r(v))])),_:1})]),d("div",h,[i(t,{modelValue:y.value,"onUpdate:modelValue":a[0]||(a[0]=e=>y.value=e),height:"calc(100vh - 120px)",onChange:C},null,8,["modelValue"])])])),_:1},8,["modelValue","title"])}}}),[["__scopeId","data-v-5bba9872"]]);export{g as default};
