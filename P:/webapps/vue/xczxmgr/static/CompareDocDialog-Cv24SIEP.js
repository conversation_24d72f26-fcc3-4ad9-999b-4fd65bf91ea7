import{d as e,r as l,S as a,a as s,h as o,b as c,w as d,e as n,j as t,q as r,a4 as i,c as p,_ as u}from"./index-BERmxY3Y.js";const m={class:"compare-container"},v={class:"panel left-panel"},f={class:"panel-content"},w={class:"panel right-panel"},V={class:"panel-content"},h=["src"],_={key:1,class:"empty-hint"},g=u(e({__name:"CompareDocDialog",props:{modelValue:{type:Boolean,default:!1},content:{default:""},docUrl:{default:""}},emits:["update:modelValue","close"],setup(e,{emit:u}){const g=e,k=u,x=l(g.modelValue),y=()=>{x.value=!1,k("update:modelValue",!1),k("close")};return a((()=>g.modelValue),(e=>{x.value=e})),a(x,(e=>{k("update:modelValue",e),e||k("close")})),(e,l)=>{const a=s("el-icon"),u=s("v-md-preview"),g=s("el-empty"),k=s("el-dialog");return c(),o(k,{modelValue:x.value,"onUpdate:modelValue":l[0]||(l[0]=e=>x.value=e),fullscreen:"","close-on-click-modal":!1,"close-on-press-escape":!0,"show-close":!1,class:"compare-dialog",onClose:y},{default:d((()=>{return[n("div",{class:"dialog-close",onClick:y},[t(a,null,{default:d((()=>[t(r(i))])),_:1})]),n("div",m,[n("div",v,[l[1]||(l[1]=n("div",{class:"panel-header"},"Markdown 内容",-1)),n("div",f,[t(u,{text:e.content,class:"md-preview"},null,8,["text"])])]),n("div",w,[l[2]||(l[2]=n("div",{class:"panel-header"},"原文档",-1)),n("div",V,[e.docUrl?(c(),p("iframe",{key:0,src:(s=e.docUrl,`https://view.officeapps.live.com/op/view.aspx?src=${encodeURIComponent(s)}&wdOrigin=BROWSELINK`),frameborder:"0",class:"office-preview",allowfullscreen:""},null,8,h)):(c(),p("div",_,[t(g,{description:"无文档可预览"})]))])])])];var s})),_:1},8,["modelValue"])}}}),[["__scopeId","data-v-0e61c7d2"]]);export{g as default};
