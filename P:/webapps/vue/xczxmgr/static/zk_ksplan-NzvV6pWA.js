import{d as e,r as l,o as a,E as t,a as o,L as s,c as u,b as d,e as i,M as n,j as r,w as _,p as c,F as p,f as m,h as v,q as b,D as f,G as y,t as g,v as h}from"./index-BERmxY3Y.js";import{v as k,b as x,w as V,x as w,g as z,y as C,f as U}from"./exam-jF1-Sa8S.js";const F={class:"examplan",style:{padding:"5px"}},j={key:0,style:{color:"#409EFF"}},E={key:1,style:{color:"#909399"}},D={style:{"margin-top":"10px",display:"flex","justify-content":"center"}},L={style:{}},M={style:{padding:"10px 20px"}},$={style:{color:"#409EFF"}},S={style:{color:"green"}},T={style:{color:"green"}},B={class:"dialog-footer"},A={class:"m-2",style:{display:"flex","justify-content":"center"}},K={style:{padding:"10px 20px"}},O={style:{padding:"10px 0px"}},P={style:{color:"#409EFF","font-weight":"bold"}},R={style:{color:"#409EFF","font-weight":"bold"}},q={style:{padding:"0px 20px",height:"380px",width:"610px","overflow-y":"auto"}},G=["onDragstart","onDragenter"],H={style:{width:"100%",display:"flex","align-items":"center"}},J={style:{"margin-left":"15px"}},N={style:{"margin-left":"40px","margin-top":"1px"},class:"cj_ksplan-item"},Z={class:"dialog-footer",style:{padding:"10px 20px","text-align":"center","border-top":"1px solid #F2F6FC"}},I={class:"dialog-footer"},Q=e({__name:"zk_ksplan",setup(e){const Q=l(""),W=l([]);l({});const X=l(),Y=l(window.innerHeight-200),ee=l(!1);a((()=>{ve(),ye()}));const le=l({id:"",bm_zy_id:"",xlcc_bm:"",xlcc_mc:"",zy_bm:"",zy_mc:"",status:"1",course_base_id:"",course_info_id:""}),ae=l("");l("");const te=l(!1),oe=l([]),se=l([]),ue=l(!1),de=l([]),ie=l([]),ne=l({}),re=l([]),_e=l([]),ce=l(1),pe=l(30),me=l(!1),ve=()=>{me.value=!0,k({filterZy:ae.value}).then((e=>{200==e.code?W.value=e.data.filter((e=>!Q.value||(e.kc_bm.includes(Q.value)||e.kc_mc.includes(Q.value)))):t.error(e.msg)})).finally((()=>{me.value=!1}))},be=e=>{le.value.id=le.value.id?le.value.id:0,le.value.zy_mc=oe.value.find((e=>e.zy_bm==le.value.zy_bm)).zy_mc,le.value.kc_mc=se.value.find((e=>e.kc_bm==le.value.kc_bm)).kc_mc,le.value.ks_sj=le.value.ks_sj.join(","),le.value.update_type=e,V(le.value).then((e=>{"200"==e.code?(t.success(e.msg),te.value=!1,ee.value=!1,ve()):t.error(e.msg)}))},fe=e=>{X.value=e},ye=()=>{x({}).then((e=>{200==e.code?(oe.value=e.data,oe.value.forEach((e=>{e.zy_mcs=`(${e.zy_bm})${e.zy_mc}`}))):t.error(e.msg)}))},ge=()=>{z({page:1,size:900}).then((e=>{if(200==e.code){se.value=e.data.list.filter((e=>2==e.learn_type));var l=W.value.filter((e=>{if(e.bm_zy_id==le.value.bm_zy_id)return e.course_base_id}));se.value.forEach((e=>{e.kc_mcs=`(${e.kc_bm})${e.kc_mc}`,l.length>0?l.forEach((l=>{l.course_base_id==e.id&&(e.disabled=!0)})):e.disabled=!1}))}else t.error(e.msg)}))},he=()=>{U({id:le.value.course_base_id}).then((e=>{200==e.code?(re.value=e.data,re.value.forEach((e=>{e.mc="版本："+e.ver+"【"+(e.is_online="在线课程")+"】【"+(1==e.is_pub?"已发布":"未发布")+"】"}))):t.error(e.msg)}))},ke=e=>{var l=se.value.find((l=>l.id==e));le.value.kc_mc=l.kc_mc,le.value.kc_bm=l.kc_bm,he()};let xe=0;function Ve(e){e.preventDefault(),e.dataTransfer.dropEffect="move"}function we(e){e.target.classList.remove("moveing")}const ze=e=>{pe.value=e,ce.value=1},Ce=e=>{ce.value=e};return(e,l)=>{const a=o("el-button"),k=o("el-option"),x=o("el-select"),V=o("el-input"),z=o("el-table-column"),U=o("EditPen"),Ue=o("el-icon"),Fe=o("el-link"),je=o("el-tag"),Ee=o("el-table"),De=o("el-pagination"),Le=o("el-divider"),Me=o("el-form-item"),$e=o("el-input-number"),Se=o("el-radio"),Te=o("el-radio-group"),Be=o("el-form"),Ae=o("el-tab-pane"),Ke=o("el-tabs"),Oe=o("el-dialog"),Pe=o("el-transfer"),Re=s("loading");return d(),u("div",F,[i("div",null,[r(a,{type:"primary",onClick:ve,icon:"RefreshRight"}),r(a,{type:"primary",onClick:l[0]||(l[0]=e=>(le.value={id:"",bm_zy_id:"",zy_bm:"",zy_mc:"",ks_lx:"",kc_bm:"",kc_mc:"",ks_sj:"",remark:"",xf:0,is_yy2_tk:"0",status:"1",course_base_id:"",course_info_id:""},ye(),ge(),void(te.value=!0))),plain:"",icon:"DocumentAdd"},{default:_((()=>l[30]||(l[30]=[c("添加")]))),_:1,__:[30]}),r(a,{type:"danger",onClick:l[1]||(l[1]=e=>{X.value?h.confirm("此操作将永久删除该计划, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{w(X.value.id).then((e=>{200==e.code?(t.success(e.msg),ve()):t.error(e.msg)}))})).catch((()=>{t({type:"info",message:"已取消删除"})})):t.warning("请先选择要删除的课程")}),plain:"",icon:"DeleteFilled"},{default:_((()=>l[31]||(l[31]=[c("删除")]))),_:1,__:[31]}),r(x,{modelValue:ae.value,"onUpdate:modelValue":l[2]||(l[2]=e=>ae.value=e),class:"m-2",placeholder:"请选择专业",style:{width:"190px","margin-left":"5px"},onChange:l[3]||(l[3]=e=>ve()),clearable:""},{default:_((()=>[(d(!0),u(p,null,m(oe.value,(e=>(d(),v(k,{key:e.zy_bm,label:e.zy_mcs,value:e.zy_bm},null,8,["label","value"])))),128))])),_:1},8,["modelValue"]),r(V,{modelValue:Q.value,"onUpdate:modelValue":l[5]||(l[5]=e=>Q.value=e),clearable:"",onKeyup:y(ve,["enter","native"]),style:{width:"230px","margin-left":"5px"},placeholder:"课程编码或则课程名称",class:"input-with-select"},{append:_((()=>[r(a,{icon:b(f),onClick:l[4]||(l[4]=e=>ve())},null,8,["icon"])])),_:1},8,["modelValue"])]),n((d(),v(Ee,{border:"",height:Y.value,data:W.value.slice((ce.value-1)*pe.value,ce.value*pe.value),size:"mini",stripe:"",style:{width:"100%","margin-top":"10px"},"highlight-current-row":"",onCurrentChange:fe},{default:_((()=>[r(z,{type:"index",width:"50",align:"center"}),r(z,{prop:"zy_mc",label:"专业名称",align:"center",sortable:""},{default:_((e=>[c(" ("+g(e.row.zy_bm)+")"+g(e.row.zy_mc),1)])),_:1}),r(z,{prop:"kc_bm",width:"120",label:"课程编码",align:"center",sortable:""}),r(z,{prop:"kc_mc",width:"340",label:"课程名称",align:"left"},{default:_((e=>[r(Fe,{onClick:l=>{return a=e.$index,le.value=W.value[a],le.value.status=le.value.status.toString(),le.value.course_info_id=le.value.course_info_id?le.value.course_info_id:"",le.value.is_yy2_tk=le.value.is_yy2_tk.toString(),le.value.ks_sj="string"==typeof le.value.ks_sj?le.value.ks_sj.split(","):le.value.ks_sj,te.value=!0,ye(),ge(),void he();var a},type:"primary"},{default:_((()=>[c(g(e.row.kc_mc)+"  ",1),r(Ue,{style:{"font-size":"13px"}},{default:_((()=>[r(U)])),_:1})])),_:2},1032,["onClick"])])),_:1}),r(z,{prop:"course_info_id",width:"100",label:"在线课程",align:"center",sortable:""},{default:_((e=>[e.row.course_info_id>0?(d(),u("span",j,"已指定")):(d(),u("span",E,"未指定"))])),_:1}),r(z,{prop:"xlcc_mc",width:"120",label:"学历层次",align:"center",sortable:""}),r(z,{prop:"ks_lx",label:"考试类型",width:"120",align:"center",sortable:""}),r(z,{prop:"status",label:"状态",align:"center",sortable:""},{default:_((e=>[1==e.row.status?(d(),v(je,{key:0,type:"success"},{default:_((()=>l[32]||(l[32]=[c("正常")]))),_:1,__:[32]})):(d(),v(je,{key:1,type:"danger"},{default:_((()=>l[33]||(l[33]=[c("禁用")]))),_:1,__:[33]}))])),_:1}),r(z,{prop:"remark",label:"备注",align:"center"}),r(z,{prop:"create_name",label:"创建人",align:"center"}),r(z,{prop:"create_date",label:"修改时间",align:"center",sortable:""})])),_:1},8,["height","data"])),[[Re,me.value]]),i("div",D,[r(De,{onSizeChange:ze,onCurrentChange:Ce,"current-page":ce.value,"page-sizes":[30,50,100,200],"page-size":pe.value,layout:"total, sizes, prev, pager, next, jumper",total:W.value.length},null,8,["current-page","page-size","total"])]),r(Oe,{modelValue:te.value,"onUpdate:modelValue":l[20]||(l[20]=e=>te.value=e),title:"添加专业信息",width:"750",top:"6vh","append-to-body":!0,"close-on-click-modal":!1,"close-on-press-escape":!1},{footer:_((()=>[i("span",B,[r(a,{onClick:l[18]||(l[18]=e=>te.value=!1)},{default:_((()=>l[43]||(l[43]=[c("取消")]))),_:1,__:[43]}),r(a,{type:"primary",onClick:l[19]||(l[19]=e=>ee.value=!0),disabled:!le.value.zy_bm||!le.value.kc_bm},{default:_((()=>l[44]||(l[44]=[c("保存 ")]))),_:1,__:[44]},8,["disabled"])])])),default:_((()=>[i("div",L,[r(Ke,{"tab-position":"top",style:{},class:"demo-tabs"},{default:_((()=>[r(Ae,{label:"基本设置"},{default:_((()=>[i("div",M,[r(Be,{inline:!0,model:le.value,class:"demo-form-inline","label-position":"right","label-width":"110px"},{default:_((()=>[r(Le,{"content-position":"left"},{default:_((()=>l[34]||(l[34]=[c("专业设置")]))),_:1,__:[34]}),r(Me,{label:"专业编码"},{default:_((()=>[r(x,{modelValue:le.value.bm_zy_id,"onUpdate:modelValue":l[6]||(l[6]=e=>le.value.bm_zy_id=e),class:"m-2",placeholder:"专业编码",style:{width:"190px"},onChange:l[7]||(l[7]=e=>{return l=oe.value.find((e=>e.id==le.value.bm_zy_id)),le.value.zy_bm=l.zy_bm,le.value.zy_mc=l.zy_mc,le.value.xlcc_bm=l.xlcc_bm,le.value.xlcc_mc=l.xlcc_mc,void ge();var l})},{default:_((()=>[(d(!0),u(p,null,m(oe.value,(e=>(d(),v(k,{key:e.id,label:e.zy_mcs,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),r(Me,{label:"专业层次"},{default:_((()=>[i("span",$,"("+g(le.value.xlcc_bm)+")"+g(le.value.xlcc_mc),1)])),_:1}),r(Le,{"content-position":"left"},{default:_((()=>l[35]||(l[35]=[c("课程设置")]))),_:1,__:[35]}),r(Me,{label:"选择课程"},{default:_((()=>[r(x,{modelValue:le.value.course_base_id,"onUpdate:modelValue":l[8]||(l[8]=e=>le.value.course_base_id=e),class:"m-2",placeholder:"选择课程",style:{width:"190px"},onChange:ke},{default:_((()=>[(d(!0),u(p,null,m(se.value,(e=>(d(),v(k,{key:e.id,disabled:e.disabled,label:e.kc_mcs,value:e.id},null,8,["disabled","label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),r(Me,{label:"在线课程版本"},{default:_((()=>[r(x,{modelValue:le.value.course_info_id,"onUpdate:modelValue":l[9]||(l[9]=e=>le.value.course_info_id=e),class:"m-2",placeholder:"在线课程",style:{width:"190px"}},{default:_((()=>[(d(!0),u(p,null,m(re.value,(e=>(d(),v(k,{key:e.id,label:e.mc,value:e.id},{default:_((()=>[i("span",null,[c("版本："+g(e.ver)+"【",1),i("span",S,g(e.is_online="在线课程"),1),l[36]||(l[36]=c("】 【")),i("span",T,g(1==e.is_pub?"已发布":"未发布"),1),l[37]||(l[37]=c("】"))])])),_:2},1032,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),r(Me,{label:"课程编码"},{default:_((()=>[r(V,{modelValue:le.value.kc_bm,"onUpdate:modelValue":l[10]||(l[10]=e=>le.value.kc_bm=e),disabled:!0,placeholder:"请输入课程名称",style:{width:"190px"}},null,8,["modelValue"])])),_:1}),r(Me,{label:"课程名称"},{default:_((()=>[r(V,{modelValue:le.value.kc_mc,"onUpdate:modelValue":l[11]||(l[11]=e=>le.value.kc_mc=e),disabled:!0,placeholder:"请输入课程名称",style:{width:"190px"}},null,8,["modelValue"])])),_:1}),r(Le,{"content-position":"left"},{default:_((()=>l[38]||(l[38]=[c("课程其他属性")]))),_:1,__:[38]}),r(Me,{label:"考试类型"},{default:_((()=>[r(x,{modelValue:le.value.ks_lx,"onUpdate:modelValue":l[12]||(l[12]=e=>le.value.ks_lx=e),class:"m-2",placeholder:"考试类型",style:{width:"190px"}},{default:_((()=>[r(k,{key:"省考",label:"省考",value:"省考"}),r(k,{key:"统考",label:"统考",value:"统考"})])),_:1},8,["modelValue"])])),_:1}),r(Me,{label:"学分"},{default:_((()=>[r($e,{modelValue:le.value.xf,"onUpdate:modelValue":l[13]||(l[13]=e=>le.value.xf=e),style:{width:"190px"}},null,8,["modelValue"])])),_:1}),r(Me,{label:"考试月份"},{default:_((()=>[r(x,{modelValue:le.value.ks_sj,"onUpdate:modelValue":l[14]||(l[14]=e=>le.value.ks_sj=e),style:{width:"190px"},multiple:"","collapse-tags":"","collapse-tags-tooltip":"",placeholder:"考试月份"},{default:_((()=>[(d(),u(p,null,m(["1","2","3","4","5","6","7","8","9","10","11","12"],(e=>r(k,{key:e,label:e+"月",value:e},null,8,["label","value"]))),64))])),_:1},8,["modelValue"])])),_:1}),r(Me,{label:"状态"},{default:_((()=>[r(Te,{modelValue:le.value.status,"onUpdate:modelValue":l[15]||(l[15]=e=>le.value.status=e)},{default:_((()=>[r(Se,{label:"1"},{default:_((()=>l[39]||(l[39]=[c("启用")]))),_:1,__:[39]}),r(Se,{label:"0"},{default:_((()=>l[40]||(l[40]=[c("禁用")]))),_:1,__:[40]})])),_:1},8,["modelValue"])])),_:1}),r(Me,{label:"英语二替考课程"},{default:_((()=>[r(Te,{modelValue:le.value.is_yy2_tk,"onUpdate:modelValue":l[16]||(l[16]=e=>le.value.is_yy2_tk=e)},{default:_((()=>[r(Se,{label:"1"},{default:_((()=>l[41]||(l[41]=[c("启用")]))),_:1,__:[41]}),r(Se,{label:"0"},{default:_((()=>l[42]||(l[42]=[c("禁用")]))),_:1,__:[42]})])),_:1},8,["modelValue"])])),_:1}),r(Me,{label:"备注"},{default:_((()=>[r(V,{modelValue:le.value.remark,"onUpdate:modelValue":l[17]||(l[17]=e=>le.value.remark=e),rows:2,type:"textarea",style:{width:"500px"}},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])])])),_:1})])),_:1})])])),_:1},8,["modelValue"]),r(Oe,{modelValue:ue.value,"onUpdate:modelValue":l[25]||(l[25]=e=>ue.value=e),width:"700","close-on-click-modal":!1,"close-on-press-escape":!1,"append-to-body":!0,top:"6vh"},{default:_((()=>[i("div",A,[r(Ke,{"tab-position":"top",style:{},class:"demo-tabs"},{default:_((()=>[r(Ae,{label:"板块设置"},{default:_((()=>[i("div",K,[i("div",O,[l[45]||(l[45]=c(" 为【")),i("span",P,g(ne.value.zy_mc),1),l[46]||(l[46]=c("】专业的【")),i("span",R,g(ne.value.kc_mc),1),l[47]||(l[47]=c("】课程指定板块 "))]),r(Pe,{modelValue:ie.value,"onUpdate:modelValue":l[21]||(l[21]=e=>ie.value=e),data:de.value,titles:["未开通板块","已开通板块"],onClick:l[22]||(l[22]=e=>(_e.value=[],void(ie.value.length>0&&de.value.forEach((e=>{ie.value.indexOf(e.id)>-1&&(e.is_list=e.is_list?e.is_list:0,e.is_blank=e.is_blank?e.is_blank:0,e.show_progress=e.show_progress?e.show_progress:0,_e.value.push(e))})))))},null,8,["modelValue","data"])])])),_:1}),r(Ae,{label:"板块详细设置"},{default:_((()=>[i("div",q,[(d(!0),u(p,null,m(_e.value,((e,a)=>(d(),u("div",{class:"item",key:e.id,draggable:"true",onDragstart:e=>{return t=a,(l=e).stopPropagation(),xe=t,void setTimeout((()=>{l.target.classList.add("moveing")}),0);var l,t},onDragenter:e=>function(e,l){if(e.preventDefault(),xe!==l){const e=_e.value[xe];_e.value.splice(xe,1),_e.value.splice(l,0,e),xe=l}}(e,a),onDragend:we,onDragover:Ve},[i("div",H,[l[48]||(l[48]=i("div",{style:{"margin-top":"4px"},class:"dragCurr"},[i("svg",{viewBox:"0 0 1024 1024",width:"30",height:"30"},[i("path",{d:"M512 0a512 512 0 0 1 512 512 512 512 0 0 1-512 512A512 512 0 0 1 0 512 512 512 0 0 1 512 0z",fill:"#FFFFFF","p-id":"7425"}),i("path",{d:"M509.5424 720.6912L593.92 636.5184l35.2256 35.2256-119.1936 118.784-118.784-118.784 35.2256-35.2256zM509.952 245.76l118.784 118.784-34.816 35.2256-83.7632-84.1728-84.1728 84.1728L389.12 364.544l119.808-118.784zM307.2 482.304v-49.7664h409.6v49.7664z m0 112.8448v-49.7664h409.6v49.7664z",fill:"#2693FF","p-id":"7426"})])],-1)),i("div",J," 【"+g(a+1)+"】 "+g(e.title),1)]),i("div",N,[r(x,{modelValue:e.is_list,"onUpdate:modelValue":l=>e.is_list=l,placeholder:"展示方式",size:"mini",style:{width:"100px"}},{default:_((()=>[(d(),v(k,{key:1,label:"有列表",value:1})),(d(),v(k,{key:0,label:"无列表",value:0}))])),_:2},1032,["modelValue","onUpdate:modelValue"]),r(x,{modelValue:e.is_blank,"onUpdate:modelValue":l=>e.is_blank=l,placeholder:"打开方式",size:"mini",style:{width:"150px","margin-left":"15px"}},{default:_((()=>[(d(),v(k,{key:1,label:"新标签页打开",value:1})),(d(),v(k,{key:0,label:"当前页打开",value:0}))])),_:2},1032,["modelValue","onUpdate:modelValue"]),r(x,{modelValue:e.show_progress,"onUpdate:modelValue":l=>e.show_progress=l,placeholder:"显示进度条",size:"mini",style:{width:"150px","margin-left":"15px"}},{default:_((()=>[(d(),v(k,{key:1,label:"显示进度条",value:1})),(d(),v(k,{key:0,label:"不显示进度条",value:0}))])),_:2},1032,["modelValue","onUpdate:modelValue"])])],40,G)))),128))])])),_:1})])),_:1})]),i("div",Z,[r(a,{onClick:l[23]||(l[23]=e=>ue.value=!1)},{default:_((()=>l[49]||(l[49]=[c("取消")]))),_:1,__:[49]}),r(a,{type:"primary",onClick:l[24]||(l[24]=e=>(()=>{var e=[];if(_e.value.forEach(((l,a)=>{var t={plate_id:l.id,is_list:l.is_list,is_blank:l.is_blank,show_progress:l.show_progress,sn:a+1};e.push(t)})),0!=e.length){var l={id:ne.value.id,jsonstr:JSON.stringify(e)};C(l).then((e=>{"200"==e.code?(t.success(e.msg),ve()):t.error(e.msg)}))}else t.warning("请至少选择一个板块")})())},{default:_((()=>l[50]||(l[50]=[c("保存 ")]))),_:1,__:[50]})])])),_:1},8,["modelValue"]),r(Oe,{modelValue:ee.value,"onUpdate:modelValue":l[29]||(l[29]=e=>ee.value=e),width:"400","close-on-click-modal":!1,"close-on-press-escape":!1,"append-to-body":!0,center:!0},{footer:_((()=>[i("span",I,[r(a,{onClick:l[26]||(l[26]=e=>te.value=!1)},{default:_((()=>l[51]||(l[51]=[c("取消")]))),_:1,__:[51]}),r(a,{type:"success",onClick:l[27]||(l[27]=e=>be(1)),disabled:!le.value.zy_bm||!le.value.kc_bm},{default:_((()=>l[52]||(l[52]=[c("保存并更新所有基础课程 ")]))),_:1,__:[52]},8,["disabled"]),r(a,{type:"primary",onClick:l[28]||(l[28]=e=>be(0)),disabled:!le.value.zy_bm||!le.value.kc_bm},{default:_((()=>l[53]||(l[53]=[c("保存")]))),_:1,__:[53]},8,["disabled"])])])),default:_((()=>[l[54]||(l[54]=i("div",{style:{width:"100%","text-align":"center",height:"50px","line-height":"50px"}}," 是否保存？ ",-1))])),_:1,__:[54]},8,["modelValue"])])}}});export{Q as default};
