import{d as e,o as l,p as a,q as o,r as t,t as d,D as i}from"./dzs-DZN_gNG7.js";import{m as r}from"./message-Df6PNwXY.js";import{d as n,K as s,r as c,o as u,x as p,a as _,L as m,c as g,b as h,e as f,j as w,w as b,p as y,F as v,f as k,h as x,t as V,M as F,m as z,E as M,P as j,v as C}from"./index-BERmxY3Y.js";const S={class:"books_zsd page"},T={class:"header"},U={style:{float:"left"}},P={style:{float:"right",color:"#67C23A","font-size":"13px"}},H={class:"body"},Z={class:"header",style:{"margin-bottom":"10px"}},A={style:{"margin-top":"16px","text-align":"right"},class:"div_pagination"},E={style:{float:"left"}},L={style:{float:"right",color:"#67C23A","font-size":"13px"}},R={key:0,class:"upload-file-info"},q={class:"dialog-footer"},B={style:{width:"100%","text-align":"center"}},D={class:"dialog-footer"},N=n({__name:"zsd",setup(n){const N=s({loading:!1,tableHeight:window.innerHeight-410,keySearch:"",course_jc_id:5,dataTable:[],dataZsdTable:[],total:0,pageSize:30,currentPage:1}),I=c([]),J=s({visible:!1,progress:0,uploading:!1,form:{}}),K=s({visible:!1,title:"",FromModule:{}}),G=c([]),O=()=>{N.loading=!0;const e={keySearch:N.keySearch,course_jc_id:N.course_jc_id,currentPage:N.currentPage,pageSize:N.pageSize};l(e).then((e=>{N.dataTable=e.data||[],N.dataZsdTable=e.data_zsd||[],N.total=e.total||N.dataTable.length,N.loading=!1})).catch((e=>{N.loading=!1}))},Q=(e,l)=>{if(!e)return"-";const a=G.value.find((l=>Number(l.id)===Number(e)));return(null==a?void 0:a[l])||"-"},W=async()=>{J.visible=!0,J.form={id:0,title:"",file:null,course_id:N.course_jc_id,url_change:0}},X=c(),Y=e=>e+"%",$=e=>{X.value.clearFiles();const l=e[0];l.uid=j(),X.value.handleStart(l)},ee=e=>{const l=e.raw||e.file;return"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"===l.type?(J.form.file=l,J.form.title=l.name.split(".")[0],!1):(r("只能上传xlsx 格式的文件！","warning"),!1)},le=async()=>{if(J.form.title)if(J.form.file)if(J.form.course_id){J.uploading=!0,J.progress=0;try{const e=new FormData;e.append("file",J.form.file),a(e).then((e=>{e.success?ae(e):r("文件上传失败","error"),N.loading=!1})).catch((e=>{}))}catch(e){r("上传失败","error")}finally{J.uploading=!1,J.progress=0}}else r("请选择所属教材课程","warning");else r("请选择要上传的文件","warning");else r("请输入标题","warning")},ae=e=>{const l={id:J.form.id,course_jc_id:J.form.course_id,title:J.form.title,url:e.url,url_change:J.form.url_change};o(l).then((e=>{200===e.code?(r("上传成功","success"),J.visible=!1,O()):r(e.msg||"保存数据失败","error"),N.loading=!1})).catch((e=>{}))},oe=e=>{switch(e){case"Success":return"success";case"Fail":return"danger";case"Running":return"warning";default:return"info"}},te=e=>{switch(e){case"Success":return"成功";case"Fail":return"失败";case"Running":return"执行中";default:return"未执行"}},de=e=>{I.value=e},ie=()=>{if(!(null!=I&&I.value.length>0))return M({message:"请至少勾选一条数据进行删除！",type:"error"}),!1;{const e=I.value;C.confirm("此操作将永久删除【"+e.length+"】条数据, 是否继续?","提示",{confirmButtonText:"删除",cancelButtonText:"取消",type:"warning"}).then((()=>{const l=[];for(var a=0;a<e.length;a++)l.push(e[a].id);var o=l.join(",");i({ids:o}).then((e=>{e.data>0?(M({type:"success",message:"成功删除!"+e.data+"条"}),O()):M({type:"info",message:"删除失败!"+e.msg})})).catch((e=>{M({type:"info",message:"删除失败!"+e})}))})).catch((()=>{M({type:"info",message:"取消删除!"})}))}},re=e=>{N.pageSize=e,O()},ne=e=>{N.currentPage=e,O()},se=()=>{N.tableHeight=window.innerHeight-410};return u((()=>{(async()=>{try{const l=await e();200===l.code?(G.value=l.data,O()):r(l.msg||"获取课程列表失败","warning")}catch(l){r("获取课程列表失败","error")}})(),se(),window.addEventListener("resize",se)})),p((()=>{window.removeEventListener("resize",se)})),(e,l)=>{const a=_("el-button"),o=_("el-input"),i=_("el-tag"),n=_("el-option"),s=_("el-select"),c=_("el-table-column"),u=_("el-table"),p=_("el-pagination"),j=_("el-form-item"),C=_("el-link"),ae=_("upload-filled"),se=_("el-icon"),ce=_("el-upload"),ue=_("el-progress"),pe=_("el-form"),_e=_("el-dialog"),me=m("loading");return h(),g("div",S,[f("div",T,[w(a,{type:"success",plain:"",size:"default",icon:"refresh",round:"",onClick:O}),w(o,{modelValue:N.keySearch,"onUpdate:modelValue":l[0]||(l[0]=e=>N.keySearch=e),placeholder:"知识点",size:"default",style:{width:"260px",margin:"0 10px"},clearable:"",onChange:O},{append:b((()=>[w(a,{icon:"Search",onClick:O})])),_:1},8,["modelValue"]),w(i,{class:"el_tag_5"},{default:b((()=>l[16]||(l[16]=[y("课程教材")]))),_:1,__:[16]}),w(s,{modelValue:N.course_jc_id,"onUpdate:modelValue":l[1]||(l[1]=e=>N.course_jc_id=e),placeholder:"请选择教材课程",clearable:"",filterable:"",style:{width:"260px"},onChange:O},{default:b((()=>[(h(!0),g(v,null,k(G.value,(e=>(h(),x(n,{key:e.id,label:"[ "+e.course_code+" ] "+e.course_name,value:e.id},{default:b((()=>[f("span",U,V("[ "+e.course_code+" ] "+e.course_name),1),f("span",P,V(e.ver),1)])),_:2},1032,["label","value"])))),128))])),_:1},8,["modelValue"]),w(a,{type:"primary",size:"small",onClick:W,disabled:N.dataZsdTable.length>0,icon:"upload"},{default:b((()=>[y(V(N.dataZsdTable.length>0?"当前教材已有文档不能新增上传只能重传":"上传层次关系表"),1)])),_:1},8,["disabled"])]),f("div",H,[F((h(),x(u,{data:N.dataZsdTable,border:"",class:"modzsdTable",height:"150",style:{width:"100%","margin-bottom":"20px"}},{default:b((()=>[w(c,{prop:"",type:"index",label:"序号",align:"center",width:"60","header-align":"center"}),w(c,{prop:"course_code",label:"课程编码",width:"80",align:"center","show-overflow-tooltip":""},{default:b((e=>[y(V(Q(e.row.course_jc_id,"course_code")),1)])),_:1}),w(c,{prop:"course_name",label:"课程名称","min-width":"120",align:"left","header-align":"center"},{default:b((e=>[y(V(Q(e.row.course_jc_id,"course_name")),1)])),_:1}),w(c,{prop:"ver",label:"教材版本",width:"80",align:"center","header-align":"center"},{default:b((e=>[y(V(Q(e.row.course_jc_id,"ver")),1)])),_:1}),w(c,{prop:"title",label:"标题","min-width":"200",align:"left","header-align":"center","show-overflow-tooltip":""}),w(c,{prop:"url",label:"文档URL","min-width":"200",align:"left","header-align":"center","show-overflow-tooltip":""}),w(c,{prop:"create_date",label:"操作时间","min-width":"120",align:"center","header-align":"center"}),w(c,{prop:"create_name",label:"操作人","min-width":"120",align:"center","header-align":"center"}),w(c,{label:"执行状态",align:"center",width:"100","header-align":"center"},{default:b((e=>[w(i,{type:oe(e.row.execute_status)},{default:b((()=>[y(V(te(e.row.execute_status)),1)])),_:2},1032,["type"])])),_:1}),w(c,{label:"操作",align:"center",width:"200","header-align":"center"},{default:b((e=>["Success"!==e.row.execute_status?(h(),x(a,{key:0,size:"small",type:"primary",icon:"magic-stick",disabled:!(!e.row.execute_id||"Running"!==e.row.execute_status),onClick:l=>(async e=>{try{N.loading=!0;const l={url:e.url,course_jc_id:e.course_jc_id,id:e.id};t(l).then((e=>{200===e.code?(r("导入数据库！"+e.data+"条","success"),O()):r(e.msg||"AI内容获取失败","warning"),N.loading=!1})).catch((e=>{}))}catch(l){r("AI内容获取失败","error")}})(e.row)},{default:b((()=>l[17]||(l[17]=[y("文档读取 ")]))),_:2,__:[17]},1032,["disabled","onClick"])):z("",!0),w(a,{size:"small",type:"warning",icon:"edit",onClick:l=>{return a=e.row,J.visible=!0,void(J.form={id:a.id,title:"",file:null,course_id:a.course_jc_id,url_change:1});var a}},{default:b((()=>l[18]||(l[18]=[y("重传 ")]))),_:2,__:[18]},1032,["onClick"])])),_:1})])),_:1},8,["data"])),[[me,N.loading]]),f("div",Z,[w(a,{type:"primary",plain:"",size:"default",round:"",icon:"CirclePlus",onClick:l[2]||(l[2]=e=>(()=>{if(!(N.course_jc_id>0))return M({message:"请先选择课程教材！",type:"error"}),!1;K.FromModule={id:0,course_jc_id:N.course_jc_id,course_code:Q(N.course_jc_id,"course_code"),course_name:Q(N.course_jc_id,"course_name")},K.title="新增知识点层级",K.visible=!0})())},{default:b((()=>l[19]||(l[19]=[y("新增")]))),_:1,__:[19]}),w(a,{type:"success",plain:"",size:"default",round:"",icon:"edit",onClick:l[3]||(l[3]=e=>(()=>{if(null==I.value||1!==I.value.length)return M({message:"请勾选一条数据进行编辑！",type:"error"}),!1;K.title="编辑知识点层级",K.visible=!0,K.FromModule=I.value[0]})())},{default:b((()=>l[20]||(l[20]=[y("编辑")]))),_:1,__:[20]}),w(a,{type:"danger",plain:"",size:"default",round:"",icon:"delete",onClick:ie},{default:b((()=>l[21]||(l[21]=[y("删除")]))),_:1,__:[21]})]),F((h(),x(u,{data:N.dataTable,border:"",class:"modTable",height:N.tableHeight,style:{width:"100%"},onSelectionChange:de},{default:b((()=>[w(c,{type:"selection",width:"60",align:"center"}),w(c,{prop:"",type:"index",label:"序号",align:"center",width:"60","header-align":"center"}),w(c,{prop:"linked_knowledge_point_code",label:"知识点编码",width:"80",align:"center","header-align":"center","show-overflow-tooltip":""}),w(c,{prop:"linked_knowledge_point_name",label:"知识点","min-width":"120",align:"left","header-align":"center","show-overflow-tooltip":""}),w(c,{prop:"node_code",label:"节点编码",width:"80",align:"center","header-align":"center"}),w(c,{prop:"node_name",label:"节点名称","min-width":"120",align:"left","header-align":"center","show-overflow-tooltip":""}),w(c,{prop:"chapter_code",label:"章节编码",width:"80",align:"center","header-align":"center"}),w(c,{prop:"chapter_name",label:"章节名称","min-width":"120",align:"left","header-align":"center","show-overflow-tooltip":""}),w(c,{prop:"course_code",label:"课程编码",width:"80",align:"center","show-overflow-tooltip":""}),w(c,{prop:"course_name",label:"课程名称","min-width":"120",align:"left","header-align":"center","show-overflow-tooltip":""},{default:b((e=>[y(V(Q(e.row.course_jc_id,"course_name")),1)])),_:1}),w(c,{prop:"ver",label:"教材版本",width:"80",align:"center","header-align":"center"},{default:b((e=>[y(V(Q(e.row.course_jc_id,"ver")),1)])),_:1}),w(c,{prop:"create_date",label:"操作时间","min-width":"120",align:"center","header-align":"center"}),w(c,{prop:"create_name",label:"操作人","min-width":"120",align:"center","header-align":"center"})])),_:1},8,["data","height"])),[[me,N.loading]]),f("div",A,[w(p,{background:"",layout:"total, sizes, prev, pager, next, jumper",total:N.total,"page-size":N.pageSize,"current-page":N.currentPage,"page-sizes":[20,50,100,500,N.total],onSizeChange:re,onCurrentChange:ne},null,8,["total","page-size","current-page","page-sizes"])])]),w(_e,{modelValue:J.visible,"onUpdate:modelValue":l[6]||(l[6]=e=>J.visible=e),title:"上传知识点关系表",width:"600px",draggable:"","close-on-click-modal":!1,"close-on-press-escape":!J.uploading},{footer:b((()=>[f("span",q,[w(a,{onClick:l[5]||(l[5]=e=>J.visible=!1),disabled:J.uploading},{default:b((()=>l[26]||(l[26]=[y("取消")]))),_:1,__:[26]},8,["disabled"]),w(a,{type:"primary",onClick:le,loading:J.uploading,disabled:J.uploading},{default:b((()=>[y(V(J.uploading?"上传中...":"确定"),1)])),_:1},8,["loading","disabled"])])])),default:b((()=>[w(pe,{model:J.form,"label-width":"140px"},{default:b((()=>[w(j,{label:"所属课程教材",required:""},{default:b((()=>[w(s,{modelValue:J.form.course_id,"onUpdate:modelValue":l[4]||(l[4]=e=>J.form.course_id=e),placeholder:"请选择课程教材",filterable:"",style:{width:"300px"}},{default:b((()=>[(h(!0),g(v,null,k(G.value,(e=>(h(),x(n,{key:e.id,label:"[ "+e.course_code+" ] "+e.course_name,value:e.id},{default:b((()=>[f("span",E,V("[ "+e.course_code+" ] "+e.course_name),1),f("span",L,V(e.ver),1)])),_:2},1032,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),w(j,{label:"Excel导入模板"},{default:b((()=>[w(C,{type:"primary",href:"https://cdn8a.swufe-online.com/oc/mfile/public-comfyui/62383b90-b3fc-4c31-9e87-7704b1d8ace4.xlsx"},{default:b((()=>l[22]||(l[22]=[y("下载知识点关系表模板")]))),_:1,__:[22]})])),_:1}),w(j,{label:"知识点关系表文件",required:""},{default:b((()=>[w(ce,{class:"upload-demo",drag:"",ref_key:"upload",ref:X,"show-file-list":!1,action:"#","auto-upload":!1,"on-change":ee,limit:1,multiple:!1,disabled:J.uploading,"on-exceed":$,accept:".xlsx"},{tip:b((()=>l[23]||(l[23]=[f("div",{class:"el-upload__tip",style:{color:"red"}}," 仅支持 xlsx 格式！请严格按照模板上传 ",-1)]))),default:b((()=>[w(se,{class:"el-icon--upload"},{default:b((()=>[w(ae)])),_:1}),l[24]||(l[24]=f("div",{class:"el-upload__text"},[y(" 将文件拖到此处，或"),f("em",null,"点击上传")],-1))])),_:1,__:[24]},8,["disabled"]),J.form.file?(h(),g("div",R," 已选择: "+V(J.form.file.name)+" ("+V((J.form.file.size/1048576).toFixed(2))+"MB) ",1)):z("",!0)])),_:1}),J.uploading?(h(),x(j,{key:0},{default:b((()=>[w(ue,{percentage:J.progress,format:Y},null,8,["percentage"]),l[25]||(l[25]=f("div",{class:"upload-status"},"正在上传中，请勿关闭窗口...",-1))])),_:1,__:[25]})):z("",!0)])),_:1},8,["model"])])),_:1},8,["modelValue","close-on-press-escape"]),w(_e,{modelValue:K.visible,"onUpdate:modelValue":l[15]||(l[15]=e=>K.visible=e),ref:"userDialog",title:K.title,draggable:"",width:"840px","close-on-press-escape":!1,"close-on-click-modal":!1},{footer:b((()=>[f("span",D,[w(a,{onClick:l[13]||(l[13]=e=>K.visible=!1),icon:"Close"},{default:b((()=>l[27]||(l[27]=[y("关闭")]))),_:1,__:[27]}),w(a,{type:"success",onClick:l[14]||(l[14]=e=>{""!==K.FromModule.chapter_code&&null!==K.FromModule.chapter_code&&void 0!==K.FromModule.chapter_code?""!==K.FromModule.node_code&&null!==K.FromModule.node_code&&void 0!==K.FromModule.node_code?""!==K.FromModule.linked_knowledge_point_code&&null!==K.FromModule.linked_knowledge_point_code&&void 0!==K.FromModule.linked_knowledge_point_code?d(K.FromModule).then((e=>{e.data>0?(K.visible=!1,M({message:"保存成功！",type:"success"}),O()):M({message:"保存失败！"+e.msg,type:"error"})})):M({message:"知识点序号不能为空",type:"info"}):M({message:"节序号不能为空",type:"info"}):M({message:"章序号不能为空",type:"info"})}),icon:"CircleCheck"},{default:b((()=>l[28]||(l[28]=[y(" 保存 ")]))),_:1,__:[28]})])])),default:b((()=>[f("div",B,[w(pe,{model:K.FromModule,inline:!0,"label-width":"90px"},{default:b((()=>[w(j,{label:"章序号"},{default:b((()=>[w(o,{modelValue:K.FromModule.chapter_code,"onUpdate:modelValue":l[7]||(l[7]=e=>K.FromModule.chapter_code=e),placeholder:"章序号（01）",style:{width:"150px"}},null,8,["modelValue"])])),_:1}),w(j,{label:"章名称"},{default:b((()=>[w(o,{modelValue:K.FromModule.chapter_name,"onUpdate:modelValue":l[8]||(l[8]=e=>K.FromModule.chapter_name=e),placeholder:"章名称",style:{width:"350px"}},null,8,["modelValue"])])),_:1}),w(j,{label:"节序号"},{default:b((()=>[w(o,{modelValue:K.FromModule.node_code,"onUpdate:modelValue":l[9]||(l[9]=e=>K.FromModule.node_code=e),placeholder:"节序号(01)",style:{width:"150px"}},null,8,["modelValue"])])),_:1}),w(j,{label:"节名称"},{default:b((()=>[w(o,{modelValue:K.FromModule.node_name,"onUpdate:modelValue":l[10]||(l[10]=e=>K.FromModule.node_name=e),placeholder:"节名称",style:{width:"350px"}},null,8,["modelValue"])])),_:1}),w(j,{label:"知识点序号",prop:"sfzh"},{default:b((()=>[w(o,{modelValue:K.FromModule.linked_knowledge_point_code,"onUpdate:modelValue":l[11]||(l[11]=e=>K.FromModule.linked_knowledge_point_code=e),placeholder:"知识点序号（001）",style:{width:"150px"}},null,8,["modelValue"])])),_:1}),w(j,{label:"知识点",prop:"email"},{default:b((()=>[w(o,{modelValue:K.FromModule.linked_knowledge_point_name,"onUpdate:modelValue":l[12]||(l[12]=e=>K.FromModule.linked_knowledge_point_name=e),placeholder:"知识点",style:{width:"350px"}},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])])])),_:1},8,["modelValue","title"])])}}});export{N as default};
