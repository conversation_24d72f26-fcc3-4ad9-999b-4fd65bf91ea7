import{d as a,r as t,u as s,s as e,o as l,g as i,a as d,c,b as n,e as o,t as v,F as p,f as r,h as u,i as h,j as y,w as m,k as g,_ as x}from"./index-BERmxY3Y.js";const j={style:{padding:"20px","background-color":"#e8e8e8",height:"100vh","min-width":"1000px"}},f={style:{display:"flex",gap:"30px","align-items":"start"}},_={style:{width:"70%"}},b={class:"tjcard"},k={class:"tjdataitem"},w={class:"tjdatanumber"},z={class:"tjdataitem"},C={class:"tjdatanumber"},D={class:"tjdataitem"},F={class:"tjdatanumber"},E={class:"tjdataitem"},I={class:"tjdatanumber"},O={class:"tjdataitem"},P={class:"tjdatanumber"},q={class:"kjrk",style:{width:"100%","overflow-x":"auto","overflow-y":"hidden"}},A={key:0,style:{color:"#909399",width:"100%",height:"100%",display:"flex","justify-content":"center","align-items":"center"}},B={key:1,style:{display:"flex",gap:"0px"}},G=["onClick"],H={class:"menu_icon"},J={key:0,class:"el-icon"},K={key:1,class:"el-icon"},L={class:"menu_text"},M={class:"ggcard",style:{width:"27%"}},N={class:"ggcarddata"},Q={key:0,style:{color:"#909399",width:"100%",height:"100%",display:"flex","justify-content":"center","align-items":"center"}},R={key:1},S={style:{"font-size":"14px",color:"#333"},class:"notice_title"},T={style:{display:"flex",gap:"30px","margin-top":"50px"}},U=x(a({__name:"home",setup(a){t("homeOther");const x=s(),{user_type_id:U}=e(x),V=t([]),W=t({kczs:0,stzs:0,xss:0,dzs:0,spzsc:0}),X=t([]);l((()=>{Y()}));const Y=()=>{i().then((a=>{V.value=a.data.menulist,W.value=a.data.mgrdata[0],X.value=a.data.noticedata})).catch((a=>{}))};return(a,t)=>{const s=d("el-table-column"),e=d("el-table"),l=d("el-card");return n(),c("div",j,[o("div",f,[o("div",_,[t[7]||(t[7]=o("h3",null,"统计总览",-1)),o("div",b,[t[5]||(t[5]=o("div",{class:"tjbg"},null,-1)),t[6]||(t[6]=o("div",{class:"tjdataitem"},[o("div",{class:"tjdatanumber"},"51"),o("div",{class:"tjdatatips"},"题库数量")],-1)),o("div",k,[o("div",w,v(W.value.stzs),1),t[0]||(t[0]=o("div",{class:"tjdatatips"},"试题总数",-1))]),o("div",z,[o("div",C,v(W.value.kczs),1),t[1]||(t[1]=o("div",{class:"tjdatatips"},"课程总数",-1))]),o("div",D,[o("div",F,v(W.value.dzs),1),t[2]||(t[2]=o("div",{class:"tjdatatips"},"课件总数",-1))]),o("div",E,[o("div",I,v(W.value.xss),1),t[3]||(t[3]=o("div",{class:"tjdatatips"},"学生总数",-1))]),o("div",O,[o("div",P,v(W.value.spzsc),1),t[4]||(t[4]=o("div",{class:"tjdatatips"},"视频时长",-1))])]),t[8]||(t[8]=o("h3",{style:{"margin-top":"15px"}},"快捷入口",-1)),o("div",q,[0===V.value.length?(n(),c("div",A," 暂无快捷入口 ~")):(n(),c("div",B,[(n(!0),c(p,null,r(V.value,((a,t)=>(n(),c("div",{class:"menu_item",key:t,onClick:t=>(a=>{a.path&&g.push({path:a.path})})(a)},[o("div",H,[a.icon?(n(),c("i",J,[(n(),u(h(a.icon),{class:"icons el-icon"}))])):(n(),c("i",K,[(n(),u(h("Pointer"),{class:"icons el-icon"}))]))]),o("div",L,v(a.title),1)],8,G)))),128))]))])]),o("div",M,[t[9]||(t[9]=o("h3",null,"公告",-1)),o("div",N,[0===X.value.length?(n(),c("div",Q," 暂无公告 ~ ")):(n(),c("div",R,[(n(!0),c(p,null,r(X.value,((a,t)=>(n(),c("div",{key:t,style:{padding:"10px","border-bottom":"1px solid #DCDFE6"}},[o("div",S,v(a.title),1)])))),128))]))])])]),o("div",T,[y(l,{shadow:"hover",style:{flex:"1"}},{default:m((()=>[t[10]||(t[10]=o("h3",null,"进行中的课程",-1)),y(e,{data:[],style:{width:"100%","margin-top":"10px"},height:"350",stripe:"",size:"small"},{default:m((()=>[y(s,{prop:"date",label:"课程名称"}),y(s,{prop:"date",label:"学习人数"}),y(s,{prop:"date",label:"学完人数"})])),_:1})])),_:1,__:[10]}),y(l,{shadow:"hover",style:{flex:"1"},class:"ggcard"},{default:m((()=>t[11]||(t[11]=[o("h3",null,"学习总览",-1),o("div",{style:{height:"350px","font-size":"12px",display:"flex","justify-content":"center","align-items":"center",color:"#909399"}}," 暂无数据 ",-1)]))),_:1,__:[11]})])])}}}),[["__scopeId","data-v-b91fc4ac"]]);export{U as default};
